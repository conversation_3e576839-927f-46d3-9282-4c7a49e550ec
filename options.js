// 简化版选项页面脚本 - 移除 AI 配置，只保留基本设置
class OptionsPage {
  constructor() {
    this.init();
  }

  async init() {
    this.bindEvents();
    await this.loadSettings();
    this.updateVersion();
  }

  bindEvents() {
    // 保存设置
    document.getElementById('save-btn').addEventListener('click', () => this.saveSettings());

    // 取消
    document.getElementById('cancel-btn').addEventListener('click', () => window.close());

    // 数据管理
    document.getElementById('export-settings-btn').addEventListener('click', () => this.exportSettings());
    document.getElementById('import-settings-btn').addEventListener('click', () => this.importSettings());
    document.getElementById('reset-settings-btn').addEventListener('click', () => this.resetSettings());

    // 文件导入
    document.getElementById('import-file').addEventListener('change', (e) => this.handleFileImport(e));
  }

  async loadSettings() {
    try {
      const settings = await chrome.storage.sync.get([
        'flomoApiUrl',
        'enableNotifications',
        'enableCache',
        'performanceMode',
        'autoAITags'
      ]);

      // 加载 Flomo 配置
      if (settings.flomoApiUrl) {
        document.getElementById('flomo-api-url').value = settings.flomoApiUrl;
      }

      // 加载功能设置
      document.getElementById('enable-notifications').checked = settings.enableNotifications !== false;
      document.getElementById('enable-cache').checked = settings.enableCache !== false;
      document.getElementById('auto-ai-tags').checked = settings.autoAITags === true;

      // 加载性能模式
      if (settings.performanceMode) {
        document.getElementById('performance-mode').value = settings.performanceMode;
      }

      this.showStatus('设置加载完成', 'success');
    } catch (error) {
      console.error('加载设置失败:', error);
      this.showStatus('加载设置失败', 'error');
    }
  }

  async saveSettings() {
    try {
      const settings = {
        flomoApiUrl: document.getElementById('flomo-api-url').value.trim(),
        enableNotifications: document.getElementById('enable-notifications').checked,
        enableCache: document.getElementById('enable-cache').checked,
        autoAITags: document.getElementById('auto-ai-tags').checked,
        performanceMode: document.getElementById('performance-mode').value
      };

      // 验证 Flomo API URL
      if (settings.flomoApiUrl && !this.isValidUrl(settings.flomoApiUrl)) {
        this.showStatus('请输入有效的 HTTPS URL', 'error');
        return;
      }

      await chrome.storage.sync.set(settings);
      this.showStatus('设置保存成功', 'success');
      
      // 延迟关闭窗口
      setTimeout(() => window.close(), 1500);
    } catch (error) {
      console.error('保存设置失败:', error);
      this.showStatus('保存设置失败', 'error');
    }
  }

  isValidUrl(string) {
    try {
      const url = new URL(string);
      return url.protocol === "https:";
    } catch (_) {
      return false;
    }
  }

  async exportSettings() {
    try {
      const settings = await chrome.storage.sync.get();
      
      // 移除敏感信息
      const exportData = {
        ...settings,
        // 不导出 API Keys 等敏感信息
        aiApiKeys: undefined,
        flomoApiUrl: undefined
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `flomo-extension-settings-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
      this.showStatus('设置导出成功', 'success');
    } catch (error) {
      console.error('导出设置失败:', error);
      this.showStatus('导出设置失败', 'error');
    }
  }

  importSettings() {
    document.getElementById('import-file').click();
  }

  async handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const text = await file.text();
      const settings = JSON.parse(text);
      
      // 验证导入的数据
      if (typeof settings !== 'object') {
        throw new Error('无效的设置文件格式');
      }

      // 只导入安全的设置
      const safeSettings = {
        enableNotifications: settings.enableNotifications,
        enableCache: settings.enableCache,
        autoAITags: settings.autoAITags,
        performanceMode: settings.performanceMode
      };

      await chrome.storage.sync.set(safeSettings);
      await this.loadSettings();
      this.showStatus('设置导入成功', 'success');
    } catch (error) {
      console.error('导入设置失败:', error);
      this.showStatus('导入设置失败: ' + error.message, 'error');
    }
    
    // 清空文件输入
    event.target.value = '';
  }

  async resetSettings() {
    if (!confirm('确定要重置所有设置吗？此操作不可撤销。')) {
      return;
    }

    try {
      // 只清除非敏感设置
      await chrome.storage.sync.remove([
        'enableNotifications',
        'enableCache',
        'autoAITags',
        'performanceMode'
      ]);

      await this.loadSettings();
      this.showStatus('设置重置成功', 'success');
    } catch (error) {
      console.error('重置设置失败:', error);
      this.showStatus('重置设置失败', 'error');
    }
  }

  updateVersion() {
    const manifest = chrome.runtime.getManifest();
    const versionElement = document.getElementById('version');
    if (versionElement) {
      versionElement.textContent = `v${manifest.version}`;
    }
  }

  showStatus(message, type) {
    const statusElement = document.getElementById('status');
    if (statusElement) {
      statusElement.textContent = message;
      statusElement.className = `status ${type}`;
      statusElement.style.display = 'block';
      
      // 自动隐藏成功消息
      if (type === 'success') {
        setTimeout(() => {
          statusElement.style.display = 'none';
        }, 3000);
      }
    }
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new OptionsPage();
  console.log('✅ Options 页面初始化完成');
});
