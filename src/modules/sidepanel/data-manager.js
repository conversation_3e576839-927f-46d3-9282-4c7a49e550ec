// 数据管理模块
export class DataManager {
  constructor() {
    this.contentData = null;
    this.cache = new Map(); // 缓存已处理的内容
  }

  // 加载待处理内容
  async loadPendingContent() {
    try {
      // 从storage获取待处理内容
      const result = await chrome.storage.local.get(['pendingContent']);
      const pendingContent = result.pendingContent;

      if (pendingContent) {
        this.contentData = pendingContent;
        return pendingContent;
      }

      return null;
    } catch (error) {
      console.error('加载待处理内容失败:', error);
      return null;
    }
  }

  // 清除待处理内容
  async clearPendingContent() {
    try {
      await chrome.storage.local.remove('pendingContent');
      this.contentData = null;
    } catch (error) {
      console.error('清除待处理内容失败:', error);
    }
  }

  // 获取配置
  async getConfig() {
    try {
      // 首先从环境配置获取默认值
      let defaultConfig = { flomo: {}, ai: {} };

      if (typeof EnvConfig !== 'undefined') {
        try {
          const envConfig = EnvConfig.getCurrentConfig();
          defaultConfig = {
            flomo: {
              apiUrl: envConfig.flomo.apiUrl || ''
            },
            ai: {
              provider: envConfig.aiProvider,
              model: envConfig.aiModel,
              apiKey: EnvConfig.getApiKey(envConfig.aiProvider),
              baseUrl: EnvConfig.getProviderConfig(envConfig.aiProvider).baseUrl
            }
          };

          console.log('✅ 从环境配置加载默认设置');
        } catch (envError) {
          console.warn('⚠️ 环境配置加载失败:', envError);
        }
      }

      // 从存储获取用户配置（主要是 Flomo API URL）
      const result = await chrome.storage.sync.get(['flomoConfig', 'aiConfig', 'flomoApiUrl']);
      
      console.log('📥 获取到的存储配置:', result);

      // 检查是否有旧格式的配置，如果有则迁移
      if (result.flomoApiUrl && (!result.flomoConfig || !result.flomoConfig.apiUrl)) {
        console.log('🔄 检测到旧格式配置，正在迁移...');
        const flomoConfig = { apiUrl: result.flomoApiUrl };
        await chrome.storage.sync.set({ flomoConfig });
        result.flomoConfig = flomoConfig;
        console.log('✅ 配置迁移完成');
      }

      // 合并配置：环境配置作为默认值，用户配置覆盖（主要是 Flomo URL）
      const mergedConfig = {
        flomo: {
          ...defaultConfig.flomo,
          ...(result.flomoConfig || {})
        },
        ai: {
          ...defaultConfig.ai,
          // AI 配置主要使用环境配置，但保留用户可能的覆盖
          ...(result.aiConfig || {})
        }
      };

      // 如果环境配置可用，确保 AI 配置使用环境值
      if (typeof EnvConfig !== 'undefined' && defaultConfig.ai.provider) {
        mergedConfig.ai = {
          ...mergedConfig.ai,
          provider: defaultConfig.ai.provider,
          model: defaultConfig.ai.model,
          apiKey: defaultConfig.ai.apiKey,
          baseUrl: defaultConfig.ai.baseUrl
        };
      }

      return mergedConfig;
    } catch (error) {
      console.error('获取配置失败:', error);

      // 备用配置
      if (typeof EnvConfig !== 'undefined') {
        try {
          const envConfig = EnvConfig.getCurrentConfig();
          return {
            flomo: { apiUrl: '' },
            ai: {
              provider: envConfig.aiProvider,
              model: envConfig.aiModel,
              apiKey: EnvConfig.getApiKey(envConfig.aiProvider),
              baseUrl: EnvConfig.getProviderConfig(envConfig.aiProvider).baseUrl
            }
          };
        } catch (envError) {
          console.error('备用环境配置也失败:', envError);
        }
      }

      return { flomo: {}, ai: {} };
    }
  }

  // 保存到Flomo
  async saveToFlomo(content, config) {
    // 验证配置
    if (!config || !config.apiUrl) {
      throw new Error('请先配置Flomo API地址');
    }

    // 构建完整内容
    const fullContent = this.buildFullContent(content);

    // 发送请求
    const response = await fetch(config.apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({ content: fullContent })
    });

    if (!response.ok) {
      throw new Error(`保存失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    // 保存成功后清除待处理内容
    if (result.code === 200 || result.code === 0) {
      await this.clearPendingContent();
      return { success: true };
    } else {
      return { success: false, error: result.message || '保存失败' };
    }
  }

  // 构建完整内容
  buildFullContent(content) {
    if (!this.contentData) {
      return content;
    }

    const { pageTitle, pageUrl, timestamp } = this.contentData;

    // 格式化时间
    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    // 构建元数据
    const metadata = `\n\n---\n页面：[${pageTitle}](${pageUrl})\n时间：${formatDate(timestamp)}`;

    return content + metadata;
  }

  // 缓存内容处理结果
  cacheContent(content, result) {
    const key = this.generateCacheKey(content);
    this.cache.set(key, {
      result,
      timestamp: Date.now()
    });
  }

  // 获取缓存的内容处理结果
  getCachedContent(content) {
    const key = this.generateCacheKey(content);
    const cached = this.cache.get(key);

    // 检查缓存是否过期（5分钟）
    if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
      return cached.result;
    }

    // 清除过期缓存
    if (cached) {
      this.cache.delete(key);
    }

    return null;
  }

  // 生成缓存键
  generateCacheKey(content) {
    // 简单的哈希函数
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  // 清理缓存
  clearCache() {
    const size = this.cache.size;
    this.cache.clear();

    // 显示清理结果
    const statusElement = document.getElementById('status-message');
    if (statusElement) {
      statusElement.textContent = `已清理 ${size} 项缓存`;
      statusElement.className = 'status-message success';
      statusElement.style.display = 'block';

      // 3秒后隐藏
      setTimeout(() => {
        statusElement.style.display = 'none';
      }, 3000);
    }
  }

  // 预热缓存
  async warmupCache() {
    // 显示预热状态
    const statusElement = document.getElementById('status-message');
    if (statusElement) {
      statusElement.textContent = '正在预热缓存...';
      statusElement.className = 'status-message info';
      statusElement.style.display = 'block';
    }

    try {
      // 模拟预热过程
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 预热成功
      if (statusElement) {
        statusElement.textContent = '缓存预热完成';
        statusElement.className = 'status-message success';

        // 3秒后隐藏
        setTimeout(() => {
          statusElement.style.display = 'none';
        }, 3000);
      }
    } catch (error) {
      // 预热失败
      if (statusElement) {
        statusElement.textContent = `预热失败: ${error.message}`;
        statusElement.className = 'status-message error';

        // 5秒后隐藏
        setTimeout(() => {
          statusElement.style.display = 'none';
        }, 5000);
      }
    }
  }

  // 获取缓存统计
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  // 清理资源
  cleanup() {
    // 清理缓存
    this.cache.clear();

    // 清理内容数据
    this.contentData = null;

    console.log('🧹 DataManager 资源已清理');
  }
}