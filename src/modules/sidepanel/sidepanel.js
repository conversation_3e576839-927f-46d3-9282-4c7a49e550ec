// 侧边栏主模块
import { UIRenderer } from './ui-renderer.js';
import { EventHandler } from './event-handler.js';
import { AIService } from './ai-service.js';
import { DataManager } from './data-manager.js';

// 全局变量
let uiRenderer;
let eventHandler;
let aiService;
let dataManager;

// 初始化
async function init() {
  try {
    // 创建模块实例
    uiRenderer = new UIRenderer();
    dataManager = new DataManager();
    aiService = new AIService();
    eventHandler = new EventHandler(uiRenderer, dataManager, aiService);

    // 加载待处理内容
    const contentData = await dataManager.loadPendingContent();

    if (!contentData) {
      // 显示空状态
      uiRenderer.showEmptyState();
      return;
    }

    // 获取配置
    const config = await dataManager.getConfig();
    
    console.log('📋 获取到的配置:', config);

    // 检查API配置
    if (!config.flomo || !config.flomo.apiUrl) {
      console.log('⚠️ API 配置缺失，显示配置提示');
      uiRenderer.showApiConfigNeeded(contentData.selectedText);
      return;
    }
    
    console.log('✅ API 配置有效:', config.flomo.apiUrl);

    // 设置AI配置
    await aiService.setConfig(config.ai);

    // 渲染内容
    renderContent(contentData, config);

    // 绑定事件
    eventHandler.bindEvents();

    // 初始化字符计数
    uiRenderer.updateCharCount();

    // 初始化标签状态
    uiRenderer.initializeTagStates();
  } catch (error) {
    console.error('初始化失败:', error);
    document.getElementById('main-content').innerHTML = `
      <div class="error-state">
        <div class="error-state-icon">❌</div>
        <div class="error-state-title">初始化失败</div>
        <div class="error-state-description">
          ${error.message}
        </div>
      </div>
    `;
  }
}

// 渲染内容
function renderContent(contentData, config) {
  const { selectedText, pageTitle, pageUrl, timestamp } = contentData;

  // 创建主内容容器
  const mainContent = document.getElementById('main-content');
  mainContent.innerHTML = '';

  // 创建元数据卡片
  const metadataCard = uiRenderer.createMetadataCard(pageTitle, pageUrl, timestamp);
  mainContent.appendChild(metadataCard);

  // 检查是否有格式化内容
  const hasFormatting = contentData.html && contentData.html.trim() !== '';

  // 创建格式选择部分
  const formatSection = uiRenderer.createFormatSection(hasFormatting, contentData.html, selectedText);
  mainContent.appendChild(formatSection);

  // 创建AI部分
  const aiSection = uiRenderer.createAISection();
  mainContent.appendChild(aiSection);

  // 创建编辑部分
  const editSection = uiRenderer.createEditSection(selectedText);
  mainContent.appendChild(editSection);

  // 创建底部操作区域
  const bottomSection = document.createElement('div');
  bottomSection.className = 'bottom-section';
  bottomSection.innerHTML = `
    <div class="cache-info">
      <span id="cache-stats">缓存: 0 项</span>
      <div class="cache-actions">
        <button id="clear-cache-btn" class="btn btn-secondary btn-small">清理</button>
        <button id="warmup-cache-btn" class="btn btn-secondary btn-small">预热</button>
      </div>
    </div>
    <div class="action-buttons">
      <button id="cancel-btn" class="btn btn-secondary">取消</button>
      <button id="save-btn" class="btn btn-primary">保存到 Flomo</button>
    </div>
  `;
  mainContent.appendChild(bottomSection);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);

// 页面卸载时清理资源
window.addEventListener('beforeunload', cleanup);
window.addEventListener('unload', cleanup);

// 清理所有模块资源
function cleanup() {
  try {
    if (eventHandler) {
      eventHandler.cleanup();
    }
    if (uiRenderer) {
      uiRenderer.cleanup();
    }
    if (dataManager) {
      dataManager.cleanup();
    }
    if (aiService) {
      aiService.cleanup();
    }

    // 清理全局变量
    uiRenderer = null;
    eventHandler = null;
    aiService = null;
    dataManager = null;

    console.log('🧹 所有模块资源已清理');
  } catch (error) {
    console.error('清理资源时出错:', error);
  }
}