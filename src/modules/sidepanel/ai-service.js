// AI服务模块
export class AIService {
  constructor() {
    this.apiConfig = null;
    this.initializeFromEnvConfig();
  }

  // 从环境配置初始化
  initializeFromEnvConfig() {
    console.log('🔍 尝试从环境配置初始化 AI 服务...');
    console.log('🔍 EnvConfig 是否可用:', typeof EnvConfig !== 'undefined');
    
    if (typeof EnvConfig !== 'undefined') {
      try {
        console.log('🔍 EnvConfig 对象:', EnvConfig);
        const envConfig = EnvConfig.getCurrentConfig();
        console.log('🔍 获取到的环境配置:', envConfig);
        
        const providerConfig = EnvConfig.getProviderConfig(envConfig.aiProvider);
        console.log('🔍 获取到的提供商配置:', providerConfig);

        this.apiConfig = {
          provider: envConfig.aiProvider,
          model: envConfig.aiModel,
          apiUrl: `${providerConfig.baseUrl}/chat/completions`,
          apiKey: providerConfig.apiKey,
          baseUrl: providerConfig.baseUrl
        };

        console.log('✅ AI服务已从环境配置初始化:', {
          provider: this.apiConfig.provider,
          model: this.apiConfig.model,
          baseUrl: this.apiConfig.baseUrl,
          apiUrl: this.apiConfig.apiUrl
        });
      } catch (error) {
        console.error('❌ AI服务环境配置初始化失败:', error);
        console.error('错误详情:', error.message, error.stack);
      }
    } else {
      console.error('❌ EnvConfig 对象不可用，无法初始化 AI 服务');
    }
  }

  // 设置API配置（主要用于覆盖环境配置）
  async setConfig(config) {
    // 如果有环境配置，优先使用环境配置的 AI 设置
    if (typeof EnvConfig !== 'undefined' && this.apiConfig) {
      // 只允许覆盖非 AI 相关的配置
      this.apiConfig = {
        ...this.apiConfig, // 保留环境配置的 AI 设置
        ...config,
        // 确保 AI 核心配置不被覆盖
        provider: this.apiConfig.provider,
        model: this.apiConfig.model,
        apiKey: this.apiConfig.apiKey,
        baseUrl: this.apiConfig.baseUrl,
        apiUrl: this.apiConfig.apiUrl
      };

      console.log('🔧 配置已更新（保留环境AI配置）');
    } else {
      // 没有环境配置时，允许完全覆盖
      this.apiConfig = config;
      console.log('🔧 配置已更新（使用传入配置）');
    }
  }

  // 处理AI功能
  async process(functionName, content) {
    // 检查配置
    if (!this.apiConfig || !this.apiConfig.apiUrl) {
      throw new Error('请先配置AI API地址');
    }

    // 根据功能类型处理
    switch (functionName) {
      case 'tags':
        return await this.extractTags(content);
      case 'translate':
        return await this.translateContent(content);
      default:
        throw new Error(`不支持的功能: ${functionName}`);
    }
  }

  // 处理流式AI功能
  async processStreaming(functionName, content, callbacks) {
    const { onChunk, onComplete, onError, signal } = callbacks;

    // 检查配置
    if (!this.apiConfig || !this.apiConfig.apiUrl) {
      throw new Error('请先配置AI API地址');
    }

    try {
      // 根据功能类型处理
      switch (functionName) {
        case 'tags':
          await this.extractTagsStreaming(content, { onChunk, onComplete, onError, signal });
          break;
        case 'translate':
          await this.translateContentStreaming(content, { onChunk, onComplete, onError, signal });
          break;
        default:
          throw new Error(`不支持的功能: ${functionName}`);
      }
    } catch (error) {
      if (onError) onError(error);
      throw error;
    }
  }

  // 提取标签
  async extractTags(content) {
    const prompt = `请为以下内容提取3-8个合适的标签，用中文回答，每个标签用逗号分隔，不要包含#符号：\n\n${content}`;

    const response = await this.callAIAPI(prompt);
    const tags = response.split(',').map(tag => tag.trim()).filter(tag => tag);

    return { tags };
  }

  // 流式提取标签
  async extractTagsStreaming(content, callbacks) {
    const { onChunk, onComplete, onError, signal } = callbacks;
    const prompt = `请为以下内容提取3-8个合适的标签，用中文回答，每个标签用逗号分隔，不要包含#符号：\n\n${content}`;

    let accumulatedContent = '';
    let partialTags = [];

    await this.callAIAPIStreaming(prompt, {
      onChunk: (chunk) => {
        accumulatedContent += chunk;

        // 尝试解析部分标签
        try {
          const currentTags = accumulatedContent.split(',').map(tag => tag.trim()).filter(tag => tag);
          if (currentTags.length > 0 && currentTags.length <= 10) {
            partialTags = currentTags;
          }
        } catch (e) {
          // 解析失败时忽略
        }

        if (onChunk) onChunk(chunk, { partialTags });
      },
      onComplete: () => {
        const tags = accumulatedContent.split(',').map(tag => tag.trim()).filter(tag => tag);
        if (onComplete) onComplete({ tags });
      },
      onError,
      signal
    });
  }



  // 翻译内容
  async translateContent(content) {
    const prompt = `请将以下内容翻译成中文，保持原意：\n\n${content}`;

    const response = await this.callAIAPI(prompt);
    return {
      translation: {
        original: content,
        translated: response,
        originalLanguage: 'auto',
        targetLanguage: 'zh'
      }
    };
  }

  // 流式翻译内容
  async translateContentStreaming(content, callbacks) {
    const { onChunk, onComplete, onError, signal } = callbacks;
    const prompt = `请将以下内容翻译成中文，保持原意：\n\n${content}`;

    let accumulatedContent = '';

    await this.callAIAPIStreaming(prompt, {
      onChunk: (chunk) => {
        accumulatedContent += chunk;
        if (onChunk) onChunk(chunk);
      },
      onComplete: () => {
        const translation = {
          original: content,
          translated: accumulatedContent,
          originalLanguage: 'auto',
          targetLanguage: 'zh'
        };
        if (onComplete) onComplete({ translation });
      },
      onError,
      signal
    });
  }

  // 调用AI API
  async callAIAPI(prompt) {
    if (!this.apiConfig || !this.apiConfig.apiUrl) {
      throw new Error('请先配置AI API地址');
    }

    try {
      const response = await fetch(this.apiConfig.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        },
        body: JSON.stringify({
          prompt: prompt,
          stream: false
        })
      });

      if (!response.ok) {
        let errorMessage = `API请求失败: ${response.status} ${response.statusText}`;

        // 尝试获取更详细的错误信息
        try {
          const errorData = await response.json();
          if (errorData.error || errorData.message) {
            errorMessage += ` - ${errorData.error || errorData.message}`;
          }
        } catch (e) {
          // 忽略JSON解析错误
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      return data.result || data.response || data.content || '';
    } catch (error) {
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('网络连接失败，请检查网络设置');
      }
      throw error;
    }
  }

  // 流式调用AI API
  async callAIAPIStreaming(prompt, callbacks) {
    const { onChunk, onComplete, onError, signal } = callbacks;

    if (!this.apiConfig || !this.apiConfig.apiUrl) {
      throw new Error('请先配置AI API地址');
    }

    try {
      const response = await fetch(this.apiConfig.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...this.getAuthHeaders()
        },
        body: JSON.stringify({
          prompt: prompt,
          stream: true
        }),
        signal
      });

      if (!response.ok) {
        let errorMessage = `API请求失败: ${response.status} ${response.statusText}`;

        // 尝试获取更详细的错误信息
        try {
          const errorData = await response.json();
          if (errorData.error || errorData.message) {
            errorMessage += ` - ${errorData.error || errorData.message}`;
          }
        } catch (e) {
          // 忽略JSON解析错误
        }

        throw new Error(errorMessage);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let done = false;

      try {
        while (!done) {
          const { value, done: readerDone } = await reader.read();
          done = readerDone;

          if (value) {
            const chunk = decoder.decode(value, { stream: true });

            // 处理流式响应
            const lines = chunk.split('\n\n');
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);

                if (data === '[DONE]') {
                  if (onComplete) onComplete();
                  return;
                }

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.result || parsed.response || parsed.content || '';
                  if (content && onChunk) onChunk(content);
                } catch (e) {
                  // 忽略解析错误，继续处理下一行
                  console.warn('解析流式响应数据失败:', e);
                }
              }
            }
          }
        }

        if (onComplete) onComplete();
      } finally {
        // 确保释放reader资源
        reader.releaseLock();
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        // 请求被取消，正常结束
        if (onComplete) onComplete();
      } else {
        if (onError) onError(error);
        throw error;
      }
    }
  }

  // 获取认证头
  getAuthHeaders() {
    if (this.apiConfig.apiKey) {
      return { 'Authorization': `Bearer ${this.apiConfig.apiKey}` };
    }
    return {};
  }

  // 清理资源
  cleanup() {
    // 清理配置
    this.apiConfig = null;

    console.log('🧹 AIService 资源已清理');
  }
}