<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Save to Flomo</title>
  <link rel="stylesheet" href="../../../sidepanel.css">
</head>

<body>
  <div class="container">
    <div class="header">
      <h1>Save to Flomo</h1>
    </div>
    <div id="main-content" class="main-content">
      <!-- 内容将通过JavaScript动态渲染 -->
    </div>
    <div id="status-message" class="status-message" style="display: none;"></div>
  </div>
  <!-- 加载依赖文件 -->
  <script src="../../../env.config.js"></script>
  <script src="../../../html-to-markdown.js"></script>
  <script src="../../../ai-service-manager.js"></script>
  
  <!-- 确保所有依赖对象都可用 -->
  <script>
    // 如果 aiServiceManager 未定义，创建一个默认实例
    if (typeof aiServiceManager === 'undefined' && typeof AIServiceManager !== 'undefined') {
      console.log('创建 aiServiceManager 默认实例');
      window.aiServiceManager = new AIServiceManager();
    }
    
    // 定义一个简化版的 AIFunctions 类，以防 ai-functions.js 加载失败
    if (typeof AIFunctions === 'undefined') {
      console.log('定义简化版 AIFunctions 类');
      window.AIFunctions = class AIFunctions {
        constructor(serviceManager) {
          this.serviceManager = serviceManager;
        }
        
        getFunctionDescriptions() {
          return {
            tags: { name: '智能标签', icon: '🏷️', estimatedTime: '3-5秒' },
            translate: { name: '中英对照', icon: '🌐', estimatedTime: '5-12秒' }
          };
        }
      };
    }
  </script>
  
  <script src="../../../ai-functions.js"></script>
  
  <script>
    // 确保 aiFunctions 已定义
    document.addEventListener('DOMContentLoaded', function() {
      if (typeof aiFunctions === 'undefined') {
        console.log('创建 aiFunctions 默认实例');
        window.aiFunctions = new AIFunctions(window.aiServiceManager);
      }
    });
  </script>
  
  <script type="module" src="sidepanel.js"></script>
</body>

</html>