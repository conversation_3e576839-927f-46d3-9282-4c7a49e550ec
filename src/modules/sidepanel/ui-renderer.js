// UI渲染模块
export class UIRenderer {
  constructor() {
    this.addedTags = new Set(); // 跟踪已添加的标签
  }

  createMetadataCard(pageTitle, pageUrl, timestamp) {
    const card = document.createElement('div');
    card.className = 'metadata-card';

    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    card.innerHTML = `
      <div class="metadata-title">页面信息</div>
      <div class="metadata-item">
        <div class="metadata-label">标题</div>
        <div class="metadata-value">${this.escapeHtml(pageTitle)}</div>
      </div>
      <div class="metadata-item">
        <div class="metadata-label">网址</div>
        <a href="${pageUrl}" target="_blank" class="metadata-value url">${this.escapeHtml(pageUrl)}</a>
      </div>
      <div class="metadata-item">
        <div class="metadata-label">选择时间</div>
        <div class="metadata-value">${formatDate(timestamp)}</div>
      </div>
    `;

    return card;
  }

  createFormatSection(hasFormatting, html, selectedText) {
    const section = document.createElement('div');
    section.className = 'format-section';

    let formatPreview = '';
    if (hasFormatting && html) {
      const preview = htmlToMarkdown.getFormattingPreview(html);
      formatPreview = `检测到格式：${preview.summary}`;
    } else {
      formatPreview = '纯文本内容，无特殊格式';
    }

    section.innerHTML = `
      <div class="format-title">格式选择</div>
      <div class="format-options">
        <div class="format-option ${this.currentFormat === 'auto' ? 'active' : ''}" data-format="auto">
          智能选择
        </div>
      <div class="format-option ${this.currentFormat === 'markdown' ? 'active' : ''}" data-format="markdown">
        保留格式
      </div>
      <div class="format-option ${this.currentFormat === 'plain' ? 'active' : ''}" data-format="plain">
        纯文本
      </div>
    </div>
    <div class="format-preview">${formatPreview}</div>
  `;

    return section;
  }

  createAISection() {
    const section = document.createElement('div');
    section.className = 'ai-section';

    let functions = {};
    let functionButtons = '';
    
    try {
      // 安全地获取 AI 功能描述
      if (typeof aiFunctions !== 'undefined') {
        functions = aiFunctions.getFunctionDescriptions();
        console.log('✅ 成功获取 AI 功能描述');
      } else {
        console.error('❌ aiFunctions 未定义，使用默认功能描述');
        // 使用默认的功能描述
        functions = {
          tags: { name: '智能标签', icon: '🏷️', estimatedTime: '3-5秒' },
          translate: { name: '中英对照', icon: '🌐', estimatedTime: '5-12秒' }
        };
      }
      
      functionButtons = Object.keys(functions).map(key => {
        const func = functions[key];
        return `
          <div class="ai-function" data-function="${key}">
            <div class="ai-function-icon">${func.icon}</div>
            <div class="ai-function-name">${func.name}</div>
          </div>
        `;
      }).join('');
    } catch (error) {
      console.error('❌ 创建 AI 功能按钮失败:', error);
      // 使用简单的默认按钮
      functionButtons = `
        <div class="ai-function" data-function="tags">
          <div class="ai-function-icon">🏷️</div>
          <div class="ai-function-name">智能标签</div>
        </div>
        <div class="ai-function" data-function="translate">
          <div class="ai-function-icon">🌐</div>
          <div class="ai-function-name">中英对照</div>
        </div>
      `;
    }

    section.innerHTML = `
      <div class="ai-title">
        🤖 AI智能处理
        <div class="ai-controls">
          <label class="streaming-toggle">
            <input type="checkbox" id="streaming-enabled" checked>
            <span class="toggle-slider"></span>
            <span class="toggle-label">流式输出</span>
          </label>
        </div>
      </div>
      <div class="ai-functions">
        ${functionButtons}
      </div>
      <div class="ai-progress" id="ai-progress">
        <div class="loading-spinner"></div>
        <div>AI处理中...</div>
        <button class="cancel-streaming-btn" id="cancel-streaming" style="display: none;">取消</button>
      </div>
      <div class="ai-results" id="ai-results"></div>
    `;

    return section;
  }

  createEditSection(selectedText) {
    const section = document.createElement('div');
    section.className = 'edit-section';

    section.innerHTML = `
      <div class="edit-title">编辑内容</div>
      <div class="textarea-container">
        <textarea id="content-textarea" placeholder="在这里编辑要保存到 Flomo 的内容...">${this.escapeHtml(selectedText)}</textarea>
        <div class="char-count" id="char-count">0 字符</div>
      </div>
    `;

    return section;
  }

  showEmptyState() {
    const mainContent = document.getElementById('main-content');
    mainContent.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">📝</div>
        <div class="empty-state-title">没有待保存的内容</div>
        <div class="empty-state-description">
          请在网页上选择文本，然后右键选择"保存到 Flomo"
        </div>
      </div>
    `;
  }

  showApiConfigNeeded(selectedText) {
    const mainContent = document.getElementById('main-content');
    mainContent.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">⚙️</div>
        <div class="empty-state-title">需要配置 API 地址</div>
        <div class="empty-state-description">
          请先配置您的 Flomo API 地址才能保存内容
        </div>
        <button id="open-settings-btn" class="btn btn-primary" style="margin-top: 16px; max-width: 200px;">
          打开配置窗口
        </button>
      </div>
    `;

    // 如果有选中的文本，临时存储它
    if (selectedText) {
      this.contentData = {
        selectedText: selectedText,
        pageTitle: '未知页面',
        pageUrl: '未知地址',
        timestamp: Date.now()
      };
    }

    // 绑定设置按钮事件
    document.getElementById('open-settings-btn').addEventListener('click', () => {
      // 打开扩展的 popup
      chrome.action.openPopup();
    });

    this.showError('请先在设置中配置您的 Flomo API 地址');
  }

  // 显示AI处理进度
  showAIProgress(functionName, isStreaming = false) {
    const progress = document.getElementById('ai-progress');
    const functions = document.querySelectorAll('.ai-function');

    if (progress) {
      progress.style.display = 'block';
      
      // 安全地获取功能描述
      let functionDesc = { name: functionName, icon: '🤖' };
      try {
        if (typeof aiFunctions !== 'undefined') {
          const descriptions = aiFunctions.getFunctionDescriptions();
          if (descriptions && descriptions[functionName]) {
            functionDesc = descriptions[functionName];
          }
        }
      } catch (error) {
        console.error('❌ 获取功能描述失败:', error);
      }

      // 检查是否使用流式输出来决定是否显示取消按钮
      const cancelButton = isStreaming ?
        '<button class="cancel-streaming-btn" id="cancel-streaming">取消</button>' : '';

      progress.innerHTML = `
        <div class="loading-spinner"></div>
        <div>正在${functionDesc.name}中...</div>
        ${cancelButton}
      `;

      // 重新绑定取消按钮事件
      if (isStreaming) {
        const cancelBtn = document.getElementById('cancel-streaming');
        if (cancelBtn) {
          cancelBtn.addEventListener('click', () => {
            this.cancelStreaming();
          });
        }
      }
    }

    // 标记正在处理的功能
    functions.forEach(func => {
      if (func.dataset.function === functionName) {
        func.classList.add('processing');
      }
    });
  }

  // 隐藏AI处理进度
  hideAIProgress() {
    const progress = document.getElementById('ai-progress');
    const functions = document.querySelectorAll('.ai-function');

    if (progress) {
      progress.style.display = 'none';
    }

    functions.forEach(func => {
      func.classList.remove('processing');
    });
  }

  // 显示AI结果
  showAIResult(functionName, result) {
    const resultsContainer = document.getElementById('ai-results');
    if (!resultsContainer) return;

    // 安全地获取功能描述
    let functionDesc = { name: functionName, icon: '🤖' };
    try {
      if (typeof aiFunctions !== 'undefined') {
        const descriptions = aiFunctions.getFunctionDescriptions();
        if (descriptions && descriptions[functionName]) {
          functionDesc = descriptions[functionName];
        }
      }
    } catch (error) {
      console.error('❌ 获取功能描述失败:', error);
    }
    let resultHtml = '';

    switch (functionName) {
      case 'tags':
        if (result.tags && result.tags.length > 0) {
          const tagsHtml = result.tags.map(tag => {
            const isAdded = this.addedTags.has(tag);
            const className = isAdded ? 'ai-tag added' : 'ai-tag';
            const title = isAdded ? '点击移除此标签' : '点击添加此标签';
            const icon = isAdded ? '✓ ' : '';
            return `<span class="${className}" data-tag="${tag}" title="${title}">${icon}${tag}</span>`;
          }).join('');

          resultHtml = `
            <div class="ai-result-item">
              <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
              <div class="ai-tags-container">
                <div class="ai-tags-title">
                  <span>点击单个标签添加/移除，或使用下方按钮添加全部：</span>
                  <div class="ai-tags-legend">
                    <span class="legend-item">
                      <span class="ai-tag demo">未添加</span> 点击添加
                    </span>
                    <span class="legend-item">
                      <span class="ai-tag demo added">✓ 已添加</span> 点击移除
                    </span>
                  </div>
                </div>
                <div class="ai-tags">${tagsHtml}</div>
              </div>
              <button class="ai-apply-btn" data-type="tags" data-content="${result.tags.join(', ')}">
                <span>新增全部标签</span>
                <span style="font-size: 11px; opacity: 0.8;">(${result.tags.length}个)</span>
              </button>
            </div>
          `;
        }
        break;
      case 'translate':
        if (result.translation) {
          const translation = result.translation;
          const originalLang = result.originalLanguage === 'zh' ? '中文' : '英文';
          const targetLang = result.targetLanguage === 'zh' ? '中文' : '英文';

          resultHtml = `
            <div class="ai-result-item">
              <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
              <div class="translation-container">
                <div class="translation-pair">
                  <div class="translation-section">
                    <div class="translation-label">原文 (${originalLang})</div>
                    <div class="translation-content original">${this.escapeHtml(translation.original)}</div>
                  </div>
                  <div class="translation-section">
                    <div class="translation-label">译文 (${targetLang})</div>
                    <div class="translation-content translated">${this.escapeHtml(translation.translated)}</div>
                  </div>
                </div>
              </div>
              <div class="translation-actions">
                <button class="ai-apply-btn" data-type="replace" data-content="${this.escapeHtml(translation.translated)}">使用译文</button>
                <button class="ai-apply-btn secondary" data-type="replace" data-content="${this.escapeHtml(translation.original + '\n\n' + translation.translated)}">保持对照格式</button>
              </div>
            </div>
          `;
        }
        break;
    }

    if (resultHtml) {
      resultsContainer.innerHTML += resultHtml;
      resultsContainer.style.display = 'block';

      // 如果是标签结果，初始化标签状态
      if (functionName === 'tags') {
        setTimeout(() => {
          this.initializeTagStates();
        }, 50);
      }
    }
  }

  // 初始化流式结果显示
  initStreamingResult(functionName) {
    const resultsContainer = document.getElementById('ai-results');
    if (!resultsContainer) return;

    // 安全地获取功能描述
    let functionDesc = { name: functionName, icon: '🤖' };
    try {
      if (typeof aiFunctions !== 'undefined') {
        const descriptions = aiFunctions.getFunctionDescriptions();
        if (descriptions && descriptions[functionName]) {
          functionDesc = descriptions[functionName];
        }
      }
    } catch (error) {
      console.error('❌ 获取功能描述失败:', error);
    }

    // 创建流式结果容器
    const streamingContainer = document.createElement('div');
    streamingContainer.className = 'ai-result-item streaming';
    streamingContainer.id = `streaming-${functionName}`;

    streamingContainer.innerHTML = `
      <div class="ai-result-title">
        ${functionDesc.icon} ${functionDesc.name}
        <span class="streaming-indicator">正在生成中...</span>
      </div>
      <div class="streaming-content" id="streaming-content-${functionName}">
        <div class="typing-cursor">|</div>
      </div>
    `;

    resultsContainer.appendChild(streamingContainer);
    resultsContainer.style.display = 'block';
  }

  // 更新流式结果
  updateStreamingResult(functionName, chunk, currentContent, extraData) {
    const contentElement = document.getElementById(`streaming-content-${functionName}`);
    if (!contentElement) return;

    // 根据不同功能类型处理显示
    switch (functionName) {
      case 'tags':
        this.updateStreamingTags(contentElement, currentContent, extraData);
        break;
      case 'translate':
        this.updateStreamingTranslation(contentElement, currentContent, extraData);
        break;
    }
  }

  // 更新流式文本内容
  updateStreamingText(contentElement, currentContent) {
    // 移除光标
    const cursor = contentElement.querySelector('.typing-cursor');
    if (cursor) cursor.remove();

    // 更新内容
    contentElement.innerHTML = `
      <div class="streaming-text">${this.escapeHtml(currentContent)}</div>
      <div class="typing-cursor">|</div>
    `;

    // 滚动到底部
    contentElement.scrollTop = contentElement.scrollHeight;
  }

  // 更新流式标签
  updateStreamingTags(contentElement, currentContent, partialTags) {
    if (!partialTags || partialTags.length === 0) {
      this.updateStreamingText(contentElement, currentContent);
      return;
    }

    // 移除光标
    const cursor = contentElement.querySelector('.typing-cursor');
    if (cursor) cursor.remove();

    // 显示部分解析的标签
    const tagsHtml = partialTags.map(tag =>
      `<span class="ai-tag streaming-tag">${tag}</span>`
    ).join('');

    contentElement.innerHTML = `
      <div class="streaming-tags">${tagsHtml}</div>
      <div class="streaming-raw-text">${this.escapeHtml(currentContent)}</div>
      <div class="typing-cursor">|</div>
    `;
  }

  // 更新流式翻译
  updateStreamingTranslation(contentElement, currentContent, partialTranslation) {
    // 移除光标
    const cursor = contentElement.querySelector('.typing-cursor');
    if (cursor) cursor.remove();

    if (partialTranslation && partialTranslation.original && partialTranslation.translated) {
      // 显示结构化的翻译结果
      contentElement.innerHTML = `
        <div class="streaming-translation">
          <div class="translation-section">
            <div class="translation-label">原文</div>
            <div class="translation-content original">${this.escapeHtml(partialTranslation.original)}</div>
          </div>
          <div class="translation-section">
            <div class="translation-label">译文</div>
            <div class="translation-content translated">${this.escapeHtml(partialTranslation.translated)}</div>
          </div>
        </div>
        <div class="typing-cursor">|</div>
      `;
    } else {
      // 显示原始内容
      this.updateStreamingText(contentElement, currentContent);
    }
  }

  // 完成流式结果显示
  finalizeStreamingResult(functionName, result) {
    const streamingContainer = document.getElementById(`streaming-${functionName}`);
    if (!streamingContainer) return;

    // 移除流式指示器和光标
    const indicator = streamingContainer.querySelector('.streaming-indicator');
    const cursor = streamingContainer.querySelector('.typing-cursor');
    if (indicator) indicator.remove();
    if (cursor) cursor.remove();

    // 移除流式类名
    streamingContainer.classList.remove('streaming');

    // 用最终结果替换流式内容
    const contentElement = streamingContainer.querySelector('.streaming-content');
    if (contentElement) {
      // 使用现有的showAIResult逻辑生成最终HTML
      const tempContainer = document.createElement('div');
      this.renderFinalResult(tempContainer, functionName, result);

      // 替换内容
      streamingContainer.innerHTML = tempContainer.innerHTML;
    }

    // 如果是标签结果，初始化标签状态
    if (functionName === 'tags') {
      setTimeout(() => {
        this.initializeTagStates();
      }, 50);
    }
  }

  // 渲染最终结果 (复用showAIResult的逻辑)
  renderFinalResult(container, functionName, result) {
    // 安全地获取功能描述
    let functionDesc = { name: functionName, icon: '🤖' };
    try {
      if (typeof aiFunctions !== 'undefined') {
        const descriptions = aiFunctions.getFunctionDescriptions();
        if (descriptions && descriptions[functionName]) {
          functionDesc = descriptions[functionName];
        }
      }
    } catch (error) {
      console.error('❌ 获取功能描述失败:', error);
    }
    let resultHtml = '';

    switch (functionName) {
      case 'tags':
        if (result.tags && result.tags.length > 0) {
          const tagsHtml = result.tags.map(tag => {
            const isAdded = this.addedTags.has(tag);
            const className = isAdded ? 'ai-tag added' : 'ai-tag';
            const title = isAdded ? '点击移除此标签' : '点击添加此标签';
            const icon = isAdded ? '✓ ' : '';
            return `<span class="${className}" data-tag="${tag}" title="${title}">${icon}${tag}</span>`;
          }).join('');

          resultHtml = `
            <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
            <div class="ai-tags-container">
              <div class="ai-tags-title">
                <span>点击单个标签添加/移除，或使用下方按钮添加全部：</span>
                <div class="ai-tags-legend">
                  <span class="legend-item">
                    <span class="ai-tag demo">未添加</span> 点击添加
                  </span>
                  <span class="legend-item">
                    <span class="ai-tag demo added">✓ 已添加</span> 点击移除
                  </span>
                </div>
              </div>
              <div class="ai-tags">${tagsHtml}</div>
            </div>
            <button class="ai-apply-btn" data-type="tags" data-content="${result.tags.join(', ')}">
              <span>新增全部标签</span>
              <span style="font-size: 11px; opacity: 0.8;">(${result.tags.length}个)</span>
            </button>
          `;
        }
        break;
      case 'translate':
        if (result.translation) {
          const translation = result.translation;
          const originalLang = result.originalLanguage === 'zh' ? '中文' : '英文';
          const targetLang = result.targetLanguage === 'zh' ? '中文' : '英文';

          resultHtml = `
            <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
            <div class="translation-container">
              <div class="translation-pair">
                <div class="translation-section">
                  <div class="translation-label">原文 (${originalLang})</div>
                  <div class="translation-content original">${this.escapeHtml(translation.original)}</div>
                </div>
                <div class="translation-section">
                  <div class="translation-label">译文 (${targetLang})</div>
                  <div class="translation-content translated">${this.escapeHtml(translation.translated)}</div>
                </div>
              </div>
            </div>
            <div class="translation-actions">
              <button class="ai-apply-btn" data-type="replace" data-content="${this.escapeHtml(translation.translated)}">使用译文</button>
              <button class="ai-apply-btn secondary" data-type="replace" data-content="${this.escapeHtml(translation.original + '\n\n' + translation.translated)}">保持对照格式</button>
            </div>
          `;
        }
        break;
    }

    container.innerHTML = `<div class="ai-result-item">${resultHtml}</div>`;
  }

  // 更新字符计数
  updateCharCount() {
    const textarea = document.getElementById('content-textarea');
    const charCount = document.getElementById('char-count');

    if (textarea && charCount) {
      const count = textarea.value.length;
      charCount.textContent = `${count} 字符`;
    }
  }

  // 显示成功消息
  showSuccess(message) {
    this.showMessage(message, 'success');
  }

  // 显示错误消息
  showError(message) {
    this.showMessage(message, 'error');
  }

  // 显示信息消息
  showInfo(message) {
    this.showMessage(message, 'info');
  }

  // 显示消息
  showMessage(message, type) {
    const statusElement = document.getElementById('status-message');
    statusElement.textContent = message;
    statusElement.className = `status-message ${type}`;
    statusElement.style.display = 'block';

    // 自动隐藏消息
    setTimeout(() => {
      statusElement.style.display = 'none';
    }, 5000);
  }

  // 解码HTML实体
  decodeHtml(html) {
    const txt = document.createElement('textarea');
    txt.innerHTML = html;
    return txt.value;
  }

  // 转义HTML
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // 检测文本中已存在的标签
  detectExistingTags(content) {
    const tagRegex = /#([^\s#]+)/g;
    const existingTags = new Set();
    let match;

    while ((match = tagRegex.exec(content)) !== null) {
      existingTags.add(match[1]);
    }

    console.log('🔍 检测到已存在的标签:', Array.from(existingTags));
    return existingTags;
  }

  // 更新标签UI状态
  updateTagUI(tagName, isAdded) {
    const tagElements = document.querySelectorAll(`.ai-tag[data-tag="${tagName}"]:not(.demo)`);
    tagElements.forEach(element => {
      if (isAdded) {
        element.classList.add('added');
        element.title = '点击移除此标签';
        element.textContent = `✓ ${tagName}`;
      } else {
        element.classList.remove('added');
        element.title = '点击添加此标签';
        element.textContent = tagName;
      }
    });

    // 更新"新增全部标签"按钮状态
    this.updateAddAllButtonState();
  }

  // 更新"新增全部标签"按钮状态
  updateAddAllButtonState() {
    const addAllBtn = document.querySelector('.ai-apply-btn[data-type="tags"]');
    if (!addAllBtn) return;

    const allTagElements = document.querySelectorAll('.ai-tag:not(.added)');
    const hasRemainingTags = allTagElements.length > 0;

    if (hasRemainingTags) {
      addAllBtn.disabled = false;
      addAllBtn.innerHTML = `
        <span>新增全部标签</span>
        <span style="font-size: 11px; opacity: 0.8;">(${allTagElements.length}个)</span>
      `;
    } else {
      addAllBtn.disabled = true;
      addAllBtn.innerHTML = `
        <span>✓ 所有标签已添加</span>
      `;
    }
  }

  // 初始化标签状态（在渲染内容时调用）
  initializeTagStates() {
    const textarea = document.getElementById('content-textarea');
    if (!textarea) return;

    // 重置已添加标签集合
    this.addedTags.clear();

    // 检测当前内容中的标签
    const existingTags = this.detectExistingTags(textarea.value);
    existingTags.forEach(tag => this.addedTags.add(tag));

    // 更新所有标签UI状态
    const allTagElements = document.querySelectorAll('.ai-tag');
    allTagElements.forEach(element => {
      const tagName = element.dataset.tag;
      if (tagName && this.addedTags.has(tagName)) {
        this.updateTagUI(tagName, true);
      }
    });

    console.log('🔄 初始化标签状态:', Array.from(this.addedTags));
  }

  // 清理资源
  cleanup() {
    // 清理标签状态
    this.addedTags.clear();

    // 清理可能的定时器
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }

    console.log('🧹 UIRenderer 资源已清理');
  }
}