// 事件处理模块
export class EventHandler {
  constructor(uiRenderer, dataManager, aiService) {
    this.uiRenderer = uiRenderer;
    this.dataManager = dataManager;
    this.aiService = aiService;
    this.isStreamingEnabled = true;
    this.currentAbortController = null;

    // 存储事件监听器引用，用于清理
    this.eventListeners = new Map();
    this.boundMethods = new Map();
  }

  // 绑定所有事件
  bindEvents() {
    // 创建绑定方法的引用
    this.boundMethods.set('saveContent', () => this.saveContent());
    this.boundMethods.set('cancelEdit', () => this.cancelEdit());

    // 保存按钮事件
    const saveBtn = document.getElementById('save-btn');
    if (saveBtn) {
      const saveHandler = this.boundMethods.get('saveContent');
      saveBtn.addEventListener('click', saveHandler);
      this.eventListeners.set('save-btn-click', { element: saveBtn, event: 'click', handler: saveHandler });
    }

    // 取消按钮事件
    const cancelBtn = document.getElementById('cancel-btn');
    if (cancelBtn) {
      const cancelHandler = this.boundMethods.get('cancelEdit');
      cancelBtn.addEventListener('click', cancelHandler);
      this.eventListeners.set('cancel-btn-click', { element: cancelBtn, event: 'click', handler: cancelHandler });
    }

    // 格式选择事件
    this.boundMethods.set('formatClick', (e) => {
      if (e.target.classList.contains('format-option')) {
        this.selectFormat(e.target.dataset.format);
      }
    });
    const formatHandler = this.boundMethods.get('formatClick');
    document.addEventListener('click', formatHandler);
    this.eventListeners.set('document-format-click', { element: document, event: 'click', handler: formatHandler });

    // AI功能事件
    this.boundMethods.set('aiClick', (e) => {
      if (e.target.classList.contains('ai-function')) {
        this.processAIFunction(e.target.dataset.function);
      }
    });
    const aiHandler = this.boundMethods.get('aiClick');
    document.addEventListener('click', aiHandler);
    this.eventListeners.set('document-ai-click', { element: document, event: 'click', handler: aiHandler });

    // AI应用按钮事件
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('ai-apply-btn')) {
        this.applyAIResult(e.target);
      }
    });

    // AI标签点击事件
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('ai-tag') && !e.target.classList.contains('demo')) {
        this.toggleTag(e.target.dataset.tag);
      }
    });

    // 流式输出切换事件
    document.getElementById('streaming-enabled')?.addEventListener('change', (e) => {
      this.setStreamingEnabled(e.target.checked);
    });

    // 取消流式处理事件
    document.addEventListener('click', (e) => {
      if (e.target.id === 'cancel-streaming') {
        this.cancelStreaming();
      }
    });

    // 文本域内容变化事件
    document.getElementById('content-textarea')?.addEventListener('input', () => {
      this.uiRenderer.updateCharCount();
    });

    // 文本域键盘事件
    document.getElementById('content-textarea')?.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + Enter 保存
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        this.saveContent();
      }
    });

    // 打开设置按钮事件
    document.addEventListener('click', (e) => {
      if (e.target.id === 'open-settings-btn') {
        chrome.runtime.openOptionsPage();
      }
    });

    // 清理缓存按钮事件
    document.getElementById('clear-cache-btn')?.addEventListener('click', () => {
      this.dataManager.clearCache();
    });

    // 预热缓存按钮事件
    document.getElementById('warmup-cache-btn')?.addEventListener('click', () => {
      this.dataManager.warmupCache();
    });
  }

  // 保存内容到Flomo
  async saveContent() {
    const content = document.getElementById('content-textarea').value;

    try {
      // 验证内容
      if (!content.trim()) {
        this.uiRenderer.showError('内容不能为空');
        return;
      }

      // 检查API配置
      const config = await this.dataManager.getConfig();
      if (!config || !config.flomo || !config.flomo.apiUrl) {
        this.uiRenderer.showApiConfigNeeded(content);
        return;
      }

      // 显示加载状态
      this.setLoading(true);

      try {
        // 保存到Flomo
        const result = await this.dataManager.saveToFlomo(content, config);

        if (result.success) {
          this.uiRenderer.showSuccess('内容已成功保存到 Flomo');

          // 清空内容
          document.getElementById('content-textarea').value = '';
          this.uiRenderer.updateCharCount();

          // 重置标签状态
          this.uiRenderer.addedTags.clear();

          // 3秒后关闭侧边栏
          setTimeout(() => {
            window.close();
          }, 3000);
        } else {
          throw new Error(result.error || '保存失败');
        }
      } catch (error) {
        console.error('保存内容时出错:', error);
        this.uiRenderer.showError(`保存失败: ${error.message}`);
      } finally {
        this.setLoading(false);
      }
    } catch (error) {
      console.error('保存内容时发生意外错误:', error);
      this.uiRenderer.showError(`操作失败: ${error.message || '未知错误'}`);
      this.setLoading(false);
    }
  }

  // 取消编辑
  cancelEdit() {
    // 清空内容
    document.getElementById('content-textarea').value = '';
    this.uiRenderer.updateCharCount();

    // 重置标签状态
    this.uiRenderer.addedTags.clear();

    // 关闭侧边栏
    window.close();
  }

  // 选择格式
  selectFormat(format) {
    // 更新UI
    document.querySelectorAll('.format-option').forEach(option => {
      option.classList.toggle('active', option.dataset.format === format);
    });

    // 更新内容
    this.updateContentByFormat(format);
  }

  // 根据格式更新内容
  async updateContentByFormat(format) {
    const { selectedText, html } = this.dataManager.contentData;
    let newContent = selectedText;

    if (format === 'markdown' && html) {
      // 转换为Markdown
      newContent = htmlToMarkdown.convert(html);
    } else if (format === 'plain') {
      // 保持纯文本
      newContent = selectedText;
    } else {
      // 智能选择
      if (html && htmlToMarkdown.hasFormatting(html)) {
        newContent = htmlToMarkdown.convert(html);
      } else {
        newContent = selectedText;
      }
    }

    // 更新文本域
    document.getElementById('content-textarea').value = newContent;
    this.uiRenderer.updateCharCount();

    // 重新初始化标签状态
    this.uiRenderer.initializeTagStates();
  }

  // 处理AI功能
  async processAIFunction(functionName) {
    const content = document.getElementById('content-textarea').value;

    if (!content.trim()) {
      this.uiRenderer.showError('请先输入内容');
      return;
    }

    // 显示处理进度
    this.uiRenderer.showAIProgress(functionName, this.isStreamingEnabled);

    try {
      if (this.isStreamingEnabled) {
        // 使用流式处理
        await this.processStreamingAIFunction(functionName, content);
      } else {
        // 使用传统处理
        await this.processNormalAIFunction(functionName, content);
      }
    } catch (error) {
      console.error(`${functionName}处理出错:`, error);
      this.uiRenderer.hideAIProgress();
      this.uiRenderer.showError(`处理失败: ${error.message}`);
    }
  }

  // 处理流式AI功能
  async processStreamingAIFunction(functionName, content) {
    // 初始化流式结果显示
    this.uiRenderer.initStreamingResult(functionName);

    // 创建AbortController用于取消请求
    this.currentAbortController = new AbortController();

    let currentContent = '';
    let partialData = null;

    try {
      // 调用AI服务的流式接口
      await this.aiService.processStreaming(functionName, content, {
        onChunk: (chunk, data) => {
          // 更新当前内容
          currentContent += chunk;

          // 更新部分数据
          if (data) {
            partialData = data;
          }

          // 更新UI
          this.uiRenderer.updateStreamingResult(functionName, chunk, currentContent, partialData);
        },
        onComplete: (result) => {
          // 完成流式显示
          this.uiRenderer.finalizeStreamingResult(functionName, result);
        },
        onError: (error) => {
          throw error;
        },
        signal: this.currentAbortController.signal
      });
    } catch (error) {
      if (error.name === 'AbortError') {
        this.uiRenderer.hideAIProgress();
        this.uiRenderer.showInfo('已取消处理');
      } else {
        throw error;
      }
    } finally {
      this.currentAbortController = null;
    }
  }

  // 处理传统AI功能
  async processNormalAIFunction(functionName, content) {
    try {
      // 调用AI服务
      const result = await this.aiService.process(functionName, content);

      // 隐藏进度
      this.uiRenderer.hideAIProgress();

      // 显示结果
      this.uiRenderer.showAIResult(functionName, result);
    } catch (error) {
      console.error(`${functionName}处理出错:`, error);
      this.uiRenderer.hideAIProgress();
      this.uiRenderer.showError(`AI处理失败: ${error.message || '未知错误'}`);
      throw error; // 重新抛出错误，让上层处理
    }
  }

  // 应用AI结果
  applyAIResult(button) {
    const type = button.dataset.type;
    const content = button.dataset.content;
    const textarea = document.getElementById('content-textarea');

    if (!textarea || !content) return;

    switch (type) {
      case 'replace':
        // 替换全部内容
        textarea.value = content;
        break;
      case 'tags':
        // 添加标签
        const tags = content.split(', ');
        tags.forEach(tag => {
          this.addSingleTag(tag);
        });
        break;
    }

    // 更新字符计数和标签状态
    this.uiRenderer.updateCharCount();
    this.uiRenderer.initializeTagStates();
  }

  // 切换标签
  toggleTag(tagName) {
    if (this.uiRenderer.addedTags.has(tagName)) {
      this.removeSingleTag(tagName);
    } else {
      this.addSingleTag(tagName);
    }
  }

  // 添加单个标签
  addSingleTag(tagName) {
    const textarea = document.getElementById('content-textarea');
    if (!textarea) return;

    // 检查标签是否已存在
    if (this.uiRenderer.addedTags.has(tagName)) {
      return;
    }

    // 添加标签到内容末尾
    const content = textarea.value.trim();
    const tagToAdd = ` #${tagName}`;

    if (content === '') {
      textarea.value = `#${tagName}`;
    } else if (!content.endsWith(' ')) {
      textarea.value = content + tagToAdd;
    } else {
      textarea.value = content + `#${tagName}`;
    }

    // 更新标签状态
    this.uiRenderer.addedTags.add(tagName);
    this.uiRenderer.updateTagUI(tagName, true);

    // 更新字符计数
    this.uiRenderer.updateCharCount();
  }

  // 移除单个标签
  removeSingleTag(tagName) {
    const textarea = document.getElementById('content-textarea');
    if (!textarea) return;

    // 从内容中移除标签
    const content = textarea.value;
    const tagToRemove = `#${tagName}`;

    // 移除标签（考虑前后空格）
    let newContent = content.replace(new RegExp(`\\s*${tagToRemove}\\s*`, 'g'), ' ').trim();

    // 如果内容以空格结尾，移除多余空格
    newContent = newContent.replace(/\s+/g, ' ');

    textarea.value = newContent;

    // 更新标签状态
    this.uiRenderer.addedTags.delete(tagName);
    this.uiRenderer.updateTagUI(tagName, false);

    // 更新字符计数
    this.uiRenderer.updateCharCount();
  }

  // 设置流式输出启用状态
  setStreamingEnabled(enabled) {
    this.isStreamingEnabled = enabled;

    // 更新UI
    const toggle = document.getElementById('streaming-enabled');
    if (toggle) {
      toggle.checked = enabled;
    }
  }

  // 取消流式处理
  cancelStreaming() {
    if (this.currentAbortController) {
      this.currentAbortController.abort();
      this.currentAbortController = null;

      // 隐藏进度
      this.uiRenderer.hideAIProgress();

      // 显示取消信息
      this.uiRenderer.showInfo('已取消处理');
    }
  }

  // 设置加载状态
  setLoading(isLoading) {
    const saveBtn = document.getElementById('save-btn');
    const cancelBtn = document.getElementById('cancel-btn');

    if (isLoading) {
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<div class="loading-spinner small"></div>保存中...';
      cancelBtn.disabled = true;
    } else {
      saveBtn.disabled = false;
      saveBtn.innerHTML = '保存到 Flomo';
      cancelBtn.disabled = false;
    }
  }

  // 清理资源和事件监听器
  cleanup() {
    // 取消当前的流式请求
    if (this.currentAbortController) {
      this.currentAbortController.abort();
      this.currentAbortController = null;
    }

    // 清理所有事件监听器
    for (const [key, { element, event, handler }] of this.eventListeners) {
      try {
        element.removeEventListener(event, handler);
      } catch (error) {
        console.warn(`清理事件监听器失败 (${key}):`, error);
      }
    }
    this.eventListeners.clear();

    // 清理绑定方法引用
    this.boundMethods.clear();

    // 清理模块引用
    this.uiRenderer = null;
    this.dataManager = null;
    this.aiService = null;

    console.log('🧹 EventHandler 资源已清理');
  }
}