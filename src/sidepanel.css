:root {
  --background-color: #f2f2f7;
  --text-color: #1d1d1f;
  --secondary-text-color: #6e6e73;
  --accent-color: #007aff;
  --accent-color-hover: #0070e0;
  --border-color: #c6c6c8;
  --input-background: #ffffff;
  --success-color: #34c759;
  --error-color: #ff3b30;
  --warning-color: #ff9500;
  --card-background: #ffffff;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  background-color: var(--background-color);
  color: var(--text-color);
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow-x: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 100%;
}

.header {
  background-color: var(--card-background);
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metadata-card {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.metadata-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-color);
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.metadata-item:last-child {
  margin-bottom: 0;
}

.metadata-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--secondary-text-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metadata-value {
  font-size: 14px;
  color: var(--text-color);
  word-break: break-all;
  line-height: 1.4;
}

.metadata-value.url {
  color: var(--accent-color);
  text-decoration: none;
}

.metadata-value.url:hover {
  text-decoration: underline;
}

.edit-section {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.edit-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-color);
}

.textarea-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

#content-textarea {
  flex: 1;
  background-color: var(--input-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.5;
  resize: none;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  min-height: 200px;
}

#content-textarea:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

.char-count {
  font-size: 12px;
  color: var(--secondary-text-color);
  text-align: right;
  margin-top: 8px;
}

.actions {
  background-color: var(--card-background);
  padding: 16px;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 12px;
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.btn {
  flex: 1;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--accent-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--accent-color-hover);
}

.btn-secondary {
  background-color: transparent;
  color: var(--secondary-text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--background-color);
  color: var(--text-color);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.status-message {
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  display: none;
}

.status-message.success {
  background-color: rgba(52, 199, 89, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(52, 199, 89, 0.3);
}

.status-message.error {
  background-color: rgba(255, 59, 48, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(255, 59, 48, 0.3);
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-text-color);
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.empty-state-description {
  font-size: 14px;
  line-height: 1.4;
}

/* 格式选择样式 */
.format-section {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.format-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-color);
}

.format-options {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.format-option {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--input-background);
  cursor: pointer;
  text-align: center;
  font-size: 13px;
  transition: all 0.2s;
}

.format-option:hover {
  border-color: var(--accent-color);
}

.format-option.active {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.format-preview {
  font-size: 12px;
  color: var(--secondary-text-color);
  padding: 8px;
  background: var(--background-color);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

/* AI功能样式 */
.ai-section {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 16px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.ai-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.ai-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 流式输出开关样式 */
.streaming-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.streaming-toggle input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 40px;
  height: 20px;
  background: var(--border-color);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.streaming-toggle input:checked+.toggle-slider {
  background: var(--accent-color);
}

.streaming-toggle input:checked+.toggle-slider::before {
  transform: translateX(20px);
}

.toggle-label {
  font-size: 12px;
  color: var(--secondary-text-color);
  font-weight: 500;
}

/* 取消流式输出按钮样式 */
.cancel-streaming-btn {
  background: var(--error-color);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  margin-left: 12px;
  transition: all 0.2s ease;
}

.cancel-streaming-btn:hover {
  background: #e53e3e;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 59, 48, 0.3);
}

.cancel-streaming-btn:active {
  transform: translateY(0);
}

.ai-functions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.ai-function {
  padding: 16px 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--input-background);
  cursor: pointer;
  text-align: center;
  font-size: 13px;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  min-height: 80px;
  position: relative;
}

.ai-function:hover {
  border-color: var(--accent-color);
  background: rgba(0, 122, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}

.ai-function.processing {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
  transform: none;
}

.ai-function.processing::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.ai-function-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.ai-function-name {
  font-weight: 600;
  font-size: 12px;
  line-height: 1.2;
}

/* 为不同功能添加特色颜色 */
.ai-function[data-function="tags"]:hover {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.05);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
}

.ai-function[data-function="summary"]:hover {
  border-color: #4ecdc4;
  background: rgba(78, 205, 196, 0.05);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.15);
}

.ai-function[data-function="format"]:hover {
  border-color: #45b7d1;
  background: rgba(69, 183, 209, 0.05);
  box-shadow: 0 4px 12px rgba(69, 183, 209, 0.15);
}

.ai-function[data-function="translate"]:hover {
  border-color: #96ceb4;
  background: rgba(150, 206, 180, 0.05);
  box-shadow: 0 4px 12px rgba(150, 206, 180, 0.15);
}

.ai-results {
  margin-top: 12px;
  padding: 12px;
  background: var(--background-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  display: none;
}

.ai-result-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
}

.ai-result-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.ai-result-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 6px;
}

.ai-result-content {
  font-size: 12px;
  color: var(--secondary-text-color);
  line-height: 1.4;
}

.ai-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.ai-tag {
  background: var(--accent-color);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
  border: 1px solid transparent;
}

.ai-tag:hover {
  background: var(--accent-color-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 122, 255, 0.3);
}

.ai-tag:active {
  transform: translateY(0);
}

.ai-tag.added {
  background: #28a745;
  color: white;
  cursor: pointer;
  opacity: 0.9;
  border: 1px solid #1e7e34;
}

.ai-tag.added:hover {
  background: #dc3545;
  border: 1px solid #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.ai-tag.demo {
  pointer-events: none;
  font-size: 10px;
  padding: 2px 6px;
}

.ai-apply-btn {
  background: var(--success-color);
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.2s;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.ai-apply-btn:hover {
  background: #2db653;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3);
}

.ai-apply-btn:active {
  transform: translateY(0);
}

.ai-apply-btn:disabled {
  background: var(--secondary-text-color);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.ai-tags-container {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.ai-tags-title {
  font-size: 12px;
  color: var(--secondary-text-color);
  margin-bottom: 8px;
  font-weight: 500;
}

.ai-tags-legend {
  display: flex;
  gap: 12px;
  margin-top: 4px;
  font-size: 10px;
  color: var(--secondary-text-color);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 中英对照翻译样式 */
.translation-container {
  margin: 12px 0;
}

.translation-pair {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: var(--background-color);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--border-color);
}

.translation-section {
  flex: 1;
}

.translation-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--secondary-text-color);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.translation-content {
  font-size: 14px;
  line-height: 1.6;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.translation-content.original {
  background: #f8f9fa;
  border-left: 3px solid var(--accent-color);
}

.translation-content.translated {
  background: #f0f9ff;
  border-left: 3px solid var(--success-color);
}

.translation-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.translation-actions .ai-apply-btn {
  flex: 1;
  margin-top: 0;
}

.translation-actions .ai-apply-btn.secondary {
  background: var(--secondary-text-color);
  font-size: 12px;
  padding: 10px 14px;
}

.translation-actions .ai-apply-btn.secondary:hover {
  background: #666;
}

/* 响应式设计 - 大屏幕时使用左右布局 */
@media (min-width: 500px) {
  .translation-pair {
    flex-direction: row;
    gap: 20px;
  }

  .translation-section {
    flex: 1;
  }
}

.ai-progress {
  display: none;
  text-align: center;
  padding: 12px;
  color: var(--secondary-text-color);
  font-size: 13px;
}

.ai-progress .loading-spinner {
  margin: 0 auto 8px;
}

/* 流式输出样式 */
.ai-result-item.streaming {
  border: 2px solid var(--accent-color);
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(0, 122, 255, 0.02));
  position: relative;
  overflow: hidden;
}

.ai-result-item.streaming::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
  animation: streamingProgress 2s infinite;
}

@keyframes streamingProgress {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

.streaming-indicator {
  font-size: 12px;
  color: var(--accent-color);
  font-weight: normal;
  margin-left: 8px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.streaming-content {
  min-height: 40px;
  padding: 12px;
  background: var(--background-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  margin: 12px 0;
  position: relative;
  max-height: 300px;
  overflow-y: auto;
}

.streaming-text {
  line-height: 1.6;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.typing-cursor {
  display: inline-block;
  color: var(--accent-color);
  font-weight: bold;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}

.streaming-tags {
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.streaming-tag {
  background: rgba(0, 122, 255, 0.1);
  color: var(--accent-color);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  border: 1px solid rgba(0, 122, 255, 0.3);
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.streaming-raw-text {
  font-size: 11px;
  color: var(--secondary-text-color);
  background: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 100px;
  overflow-y: auto;
}

.streaming-translation {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.streaming-translation .translation-section {
  background: var(--input-background);
  border-radius: 6px;
  padding: 12px;
  border: 1px solid var(--border-color);
}

.streaming-translation .translation-label {
  font-size: 11px;
  font-weight: 600;
  color: var(--secondary-text-color);
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.streaming-translation .translation-content {
  line-height: 1.6;
  word-wrap: break-word;
}

.streaming-translation .translation-content.original {
  border-left: 3px solid var(--accent-color);
  padding-left: 8px;
}

.streaming-translation .translation-content.translated {
  border-left: 3px solid var(--success-color);
  padding-left: 8px;
}

/* 流式输出完成时的过渡效果 */
.ai-result-item.streaming-complete {
  animation: streamingComplete 0.5s ease-out;
}

@keyframes streamingComplete {
  0% {
    border-color: var(--accent-color);
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(0, 122, 255, 0.02));
  }

  100% {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(52, 199, 89, 0.05), rgba(52, 199, 89, 0.02));
  }
}

@media (max-width: 400px) {

  .header,
  .content,
  .actions {
    padding: 12px;
  }

  .actions {
    flex-direction: column;
  }

  .btn {
    flex: none;
  }

  .ai-functions {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .ai-function {
    padding: 12px 8px;
    min-height: 70px;
  }

  .ai-function-icon {
    font-size: 18px;
  }

  .ai-function-name {
    font-size: 11px;
  }

  .streaming-content {
    max-height: 200px;
  }

  .streaming-translation {
    gap: 8px;
  }

  .streaming-translation .translation-section {
    padding: 8px;
  }
}