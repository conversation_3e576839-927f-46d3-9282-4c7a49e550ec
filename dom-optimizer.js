// DOM操作优化器
class DOMOptimizer {
  constructor() {
    this.updateQueue = [];
    this.isProcessing = false;
    this.observers = new Map();
    this.virtualDOM = new Map();
    this.fragmentCache = new Map();
    this.init();
  }

  init() {
    // 使用requestAnimationFrame批量处理DOM更新
    this.scheduleUpdates();
  }

  // 批量DOM更新
  batchUpdate(updateFn) {
    this.updateQueue.push(updateFn);
    
    if (!this.isProcessing) {
      this.scheduleUpdates();
    }
  }

  scheduleUpdates() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    requestAnimationFrame(() => {
      this.processUpdateQueue();
      this.isProcessing = false;
    });
  }

  processUpdateQueue() {
    if (this.updateQueue.length === 0) return;
    
    console.log(`🔄 批量处理 ${this.updateQueue.length} 个DOM更新`);
    
    // 批量执行所有更新
    const updates = [...this.updateQueue];
    this.updateQueue = [];
    
    // 使用DocumentFragment减少重排
    const fragment = document.createDocumentFragment();
    let hasFragmentUpdates = false;
    
    updates.forEach(updateFn => {
      try {
        const result = updateFn(fragment);
        if (result === 'fragment') {
          hasFragmentUpdates = true;
        }
      } catch (error) {
        console.error('DOM更新失败:', error);
      }
    });
    
    // 如果有fragment更新，一次性应用
    if (hasFragmentUpdates && fragment.children.length > 0) {
      const container = document.body; // 或指定容器
      container.appendChild(fragment);
    }
  }

  // 优化的元素创建
  createElement(tag, attributes = {}, content = '', useFragment = false) {
    const cacheKey = `${tag}_${JSON.stringify(attributes)}_${content}`;
    
    // 检查fragment缓存
    if (this.fragmentCache.has(cacheKey)) {
      const cached = this.fragmentCache.get(cacheKey);
      return cached.cloneNode(true);
    }
    
    const element = document.createElement(tag);
    
    // 批量设置属性
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className') {
        element.className = value;
      } else if (key === 'style' && typeof value === 'object') {
        Object.assign(element.style, value);
      } else if (key.startsWith('data-')) {
        element.dataset[key.slice(5)] = value;
      } else {
        element.setAttribute(key, value);
      }
    });
    
    if (content) {
      if (typeof content === 'string') {
        element.textContent = content;
      } else {
        element.appendChild(content);
      }
    }
    
    // 缓存常用元素
    if (this.fragmentCache.size < 50) {
      this.fragmentCache.set(cacheKey, element.cloneNode(true));
    }
    
    return element;
  }

  // 优化的批量元素创建
  createElements(elementConfigs) {
    const fragment = document.createDocumentFragment();
    
    elementConfigs.forEach(config => {
      const element = this.createElement(
        config.tag,
        config.attributes,
        config.content
      );
      fragment.appendChild(element);
    });
    
    return fragment;
  }

  // 虚拟滚动实现
  createVirtualScroller(container, items, renderItem, itemHeight = 50) {
    const scroller = new VirtualScroller(container, items, renderItem, itemHeight);
    return scroller;
  }

  // 懒加载图片
  lazyLoadImages(selector = 'img[data-src]') {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        });
      });

      document.querySelectorAll(selector).forEach(img => {
        imageObserver.observe(img);
      });

      this.observers.set('images', imageObserver);
    }
  }

  // 防抖的输入处理
  optimizeInputs(selector = 'input, textarea') {
    const inputs = document.querySelectorAll(selector);
    
    inputs.forEach(input => {
      let timer;
      const originalHandler = input.oninput;
      
      input.oninput = (e) => {
        clearTimeout(timer);
        timer = setTimeout(() => {
          if (originalHandler) {
            originalHandler.call(input, e);
          }
        }, 300);
      };
    });
  }

  // 事件委托优化
  delegateEvents(container, eventType, selector, handler) {
    container.addEventListener(eventType, (e) => {
      const target = e.target.closest(selector);
      if (target) {
        handler.call(target, e);
      }
    });
  }

  // 内存泄漏检测
  detectMemoryLeaks() {
    const leaks = [];
    
    // 检查未清理的事件监听器
    if (this.observers.size > 10) {
      leaks.push({
        type: 'observers',
        count: this.observers.size,
        severity: 'warning'
      });
    }
    
    // 检查DOM节点数量
    const nodeCount = document.querySelectorAll('*').length;
    if (nodeCount > 5000) {
      leaks.push({
        type: 'dom_nodes',
        count: nodeCount,
        severity: 'warning'
      });
    }
    
    // 检查缓存大小
    if (this.fragmentCache.size > 100) {
      leaks.push({
        type: 'fragment_cache',
        count: this.fragmentCache.size,
        severity: 'info'
      });
    }
    
    return leaks;
  }

  // 清理资源
  cleanup() {
    // 清理观察器
    this.observers.forEach(observer => {
      observer.disconnect();
    });
    this.observers.clear();
    
    // 清理缓存
    this.fragmentCache.clear();
    this.virtualDOM.clear();
    
    // 清理更新队列
    this.updateQueue = [];
    
    console.log('🧹 DOM优化器已清理');
  }

  // 获取性能统计
  getPerformanceStats() {
    return {
      updateQueue: this.updateQueue.length,
      observers: this.observers.size,
      fragmentCache: this.fragmentCache.size,
      virtualDOM: this.virtualDOM.size,
      domNodes: document.querySelectorAll('*').length,
      memoryLeaks: this.detectMemoryLeaks()
    };
  }
}

// 虚拟滚动器
class VirtualScroller {
  constructor(container, items, renderItem, itemHeight = 50) {
    this.container = container;
    this.items = items;
    this.renderItem = renderItem;
    this.itemHeight = itemHeight;
    this.visibleStart = 0;
    this.visibleEnd = 0;
    this.scrollTop = 0;
    this.containerHeight = 0;
    this.init();
  }

  init() {
    this.containerHeight = this.container.clientHeight;
    this.calculateVisibleRange();
    this.render();
    this.bindEvents();
  }

  calculateVisibleRange() {
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
    const buffer = Math.floor(visibleCount / 2); // 缓冲区
    
    this.visibleStart = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - buffer);
    this.visibleEnd = Math.min(this.items.length, this.visibleStart + visibleCount + buffer * 2);
  }

  render() {
    // 清空容器
    this.container.innerHTML = '';
    
    // 创建滚动容器
    const scrollContainer = document.createElement('div');
    scrollContainer.style.height = `${this.items.length * this.itemHeight}px`;
    scrollContainer.style.position = 'relative';
    
    // 渲染可见项目
    for (let i = this.visibleStart; i < this.visibleEnd; i++) {
      const item = this.items[i];
      const element = this.renderItem(item, i);
      element.style.position = 'absolute';
      element.style.top = `${i * this.itemHeight}px`;
      element.style.height = `${this.itemHeight}px`;
      scrollContainer.appendChild(element);
    }
    
    this.container.appendChild(scrollContainer);
  }

  bindEvents() {
    this.container.addEventListener('scroll', () => {
      this.scrollTop = this.container.scrollTop;
      this.calculateVisibleRange();
      this.render();
    });
  }

  updateItems(newItems) {
    this.items = newItems;
    this.calculateVisibleRange();
    this.render();
  }
}

// 创建全局DOM优化器实例
const domOptimizer = new DOMOptimizer();

// 添加到全局作用域
if (typeof window !== 'undefined') {
  window.domOptimizer = domOptimizer;
}
