// 新手引导系统
class OnboardingManager {
  constructor() {
    this.currentStep = 0;
    this.totalSteps = 5;
    this.isActive = false;
    this.overlay = null;
    this.tooltip = null;
    this.init();
  }

  async init() {
    // 检查是否是首次使用
    const result = await chrome.storage.sync.get(['hasCompletedOnboarding', 'onboardingVersion']);
    const currentVersion = '2.0.0';
    
    if (!result.hasCompletedOnboarding || result.onboardingVersion !== currentVersion) {
      // 延迟启动引导，确保页面完全加载
      setTimeout(() => {
        this.startOnboarding();
      }, 1000);
    }
  }

  startOnboarding() {
    if (this.isActive) return;
    
    this.isActive = true;
    this.currentStep = 0;
    this.createOverlay();
    this.showStep(0);
  }

  createOverlay() {
    // 创建遮罩层
    this.overlay = document.createElement('div');
    this.overlay.className = 'onboarding-overlay';
    this.overlay.innerHTML = `
      <style>
        .onboarding-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.7);
          z-index: 10000;
          pointer-events: none;
        }
        
        .onboarding-tooltip {
          position: fixed;
          background: white;
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          max-width: 320px;
          z-index: 10001;
          pointer-events: auto;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
        }
        
        .onboarding-tooltip h3 {
          margin: 0 0 12px 0;
          font-size: 18px;
          font-weight: 600;
          color: #1d1d1f;
        }
        
        .onboarding-tooltip p {
          margin: 0 0 16px 0;
          font-size: 14px;
          line-height: 1.5;
          color: #6e6e73;
        }
        
        .onboarding-tooltip .buttons {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
        }
        
        .onboarding-tooltip button {
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }
        
        .onboarding-tooltip .btn-secondary {
          background: #f2f2f7;
          color: #6e6e73;
        }
        
        .onboarding-tooltip .btn-secondary:hover {
          background: #e5e5ea;
        }
        
        .onboarding-tooltip .btn-primary {
          background: #007aff;
          color: white;
        }
        
        .onboarding-tooltip .btn-primary:hover {
          background: #0070e0;
        }
        
        .onboarding-progress {
          display: flex;
          gap: 4px;
          margin-bottom: 16px;
        }
        
        .onboarding-progress-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #e5e5ea;
          transition: background 0.2s ease;
        }
        
        .onboarding-progress-dot.active {
          background: #007aff;
        }
        
        .onboarding-highlight {
          position: fixed;
          border: 2px solid #007aff;
          border-radius: 8px;
          pointer-events: none;
          z-index: 9999;
          box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.2);
          transition: all 0.3s ease;
        }
        
        .onboarding-feature-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;
          margin: 16px 0;
        }
        
        .onboarding-feature {
          padding: 12px;
          background: #f8f9fa;
          border-radius: 8px;
          text-align: center;
        }
        
        .onboarding-feature-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }
        
        .onboarding-feature-name {
          font-size: 12px;
          font-weight: 500;
          color: #1d1d1f;
        }
      </style>
    `;
    
    document.body.appendChild(this.overlay);
  }

  showStep(stepIndex) {
    this.currentStep = stepIndex;
    
    // 移除之前的提示框和高亮
    if (this.tooltip) {
      this.tooltip.remove();
    }
    
    const existingHighlight = document.querySelector('.onboarding-highlight');
    if (existingHighlight) {
      existingHighlight.remove();
    }

    const steps = this.getSteps();
    const step = steps[stepIndex];
    
    if (!step) {
      this.completeOnboarding();
      return;
    }

    // 创建提示框
    this.tooltip = document.createElement('div');
    this.tooltip.className = 'onboarding-tooltip';
    this.tooltip.innerHTML = this.createTooltipContent(step, stepIndex);
    
    // 设置位置
    this.positionTooltip(step);
    
    // 高亮目标元素
    if (step.target) {
      this.highlightElement(step.target);
    }
    
    document.body.appendChild(this.tooltip);
    
    // 绑定事件
    this.bindTooltipEvents();
  }

  getSteps() {
    // 根据当前页面返回不同的引导步骤
    if (window.location.pathname.includes('popup.html')) {
      return this.getPopupSteps();
    } else if (window.location.pathname.includes('sidepanel.html')) {
      return this.getSidepanelSteps();
    } else {
      return this.getWebPageSteps();
    }
  }

  getPopupSteps() {
    return [
      {
        title: '🎉 欢迎使用 Flomo 扩展',
        content: '这是一个强大的网页内容保存工具，支持富文本格式和AI智能处理。让我们快速了解如何使用！',
        position: 'center'
      },
      {
        title: '📝 配置 Flomo API',
        content: '首先需要配置您的 Flomo API 地址。这是保存内容到 Flomo 的必要步骤。',
        target: '#api-url',
        position: 'bottom'
      },
      {
        title: '🤖 AI 功能配置（可选）',
        content: '您可以配置AI服务来使用智能标签、内容摘要、格式整理等高级功能。',
        target: '#ai-provider',
        position: 'bottom'
      },
      {
        title: '💾 保存配置',
        content: '配置完成后，点击保存按钮保存您的设置。',
        target: '#save-button',
        position: 'top'
      },
      {
        title: '🚀 开始使用',
        content: '配置完成！现在您可以在任何网页上选择文本，右键选择"保存到 Flomo"来使用扩展了。',
        position: 'center'
      }
    ];
  }

  getSidepanelSteps() {
    return [
      {
        title: '📝 编辑界面',
        content: '这里是内容编辑界面。您可以在保存前预览和修改选中的内容。',
        position: 'center'
      },
      {
        title: '🎨 格式选择',
        content: '如果选中的内容包含格式，您可以选择保留格式、转为纯文本或智能选择。',
        target: '.format-section',
        position: 'right'
      },
      {
        title: '🤖 AI 智能处理',
        content: '使用AI功能可以自动生成标签、摘要、优化格式或翻译内容。',
        target: '.ai-section',
        position: 'right'
      },
      {
        title: '✏️ 内容编辑',
        content: '在这里编辑要保存的内容。支持实时字符计数和自动保存。',
        target: '#content-textarea',
        position: 'left'
      },
      {
        title: '💾 保存内容',
        content: '编辑完成后，点击保存按钮将内容保存到 Flomo。',
        target: '#save-btn',
        position: 'top'
      }
    ];
  }

  getWebPageSteps() {
    return [
      {
        title: '🎯 如何使用扩展',
        content: '在网页上选择任意文本，然后右键选择"保存到 Flomo"即可开始使用。',
        position: 'center'
      },
      {
        title: '⚙️ 配置扩展',
        content: '点击浏览器工具栏中的扩展图标可以打开设置页面进行配置。',
        position: 'center'
      }
    ];
  }

  createTooltipContent(step, stepIndex) {
    const progress = Array.from({ length: this.totalSteps }, (_, i) => 
      `<div class="onboarding-progress-dot ${i <= stepIndex ? 'active' : ''}"></div>`
    ).join('');

    let featuresGrid = '';
    if (stepIndex === 0 && window.location.pathname.includes('popup.html')) {
      featuresGrid = `
        <div class="onboarding-feature-grid">
          <div class="onboarding-feature">
            <div class="onboarding-feature-icon">📝</div>
            <div class="onboarding-feature-name">富文本保存</div>
          </div>
          <div class="onboarding-feature">
            <div class="onboarding-feature-icon">🤖</div>
            <div class="onboarding-feature-name">AI智能处理</div>
          </div>
          <div class="onboarding-feature">
            <div class="onboarding-feature-icon">🏷️</div>
            <div class="onboarding-feature-name">智能标签</div>
          </div>
          <div class="onboarding-feature">
            <div class="onboarding-feature-icon">🌐</div>
            <div class="onboarding-feature-name">中英对照</div>
          </div>
        </div>
      `;
    }

    return `
      <div class="onboarding-progress">${progress}</div>
      <h3>${step.title}</h3>
      <p>${step.content}</p>
      ${featuresGrid}
      <div class="buttons">
        ${stepIndex > 0 ? '<button class="btn-secondary" data-action="prev">上一步</button>' : ''}
        <button class="btn-secondary" data-action="skip">跳过</button>
        <button class="btn-primary" data-action="next">
          ${stepIndex === this.totalSteps - 1 ? '完成' : '下一步'}
        </button>
      </div>
    `;
  }

  positionTooltip(step) {
    if (step.position === 'center') {
      this.tooltip.style.position = 'fixed';
      this.tooltip.style.top = '50%';
      this.tooltip.style.left = '50%';
      this.tooltip.style.transform = 'translate(-50%, -50%)';
      return;
    }

    if (!step.target) return;

    const targetElement = document.querySelector(step.target);
    if (!targetElement) {
      // 如果目标元素不存在，居中显示
      this.positionTooltip({ position: 'center' });
      return;
    }

    const rect = targetElement.getBoundingClientRect();
    const tooltipRect = this.tooltip.getBoundingClientRect();

    let top, left;

    switch (step.position) {
      case 'top':
        top = rect.top - tooltipRect.height - 12;
        left = rect.left + (rect.width - tooltipRect.width) / 2;
        break;
      case 'bottom':
        top = rect.bottom + 12;
        left = rect.left + (rect.width - tooltipRect.width) / 2;
        break;
      case 'left':
        top = rect.top + (rect.height - tooltipRect.height) / 2;
        left = rect.left - tooltipRect.width - 12;
        break;
      case 'right':
        top = rect.top + (rect.height - tooltipRect.height) / 2;
        left = rect.right + 12;
        break;
    }

    // 确保提示框在视窗内
    top = Math.max(12, Math.min(top, window.innerHeight - tooltipRect.height - 12));
    left = Math.max(12, Math.min(left, window.innerWidth - tooltipRect.width - 12));

    this.tooltip.style.top = `${top}px`;
    this.tooltip.style.left = `${left}px`;
  }

  highlightElement(selector) {
    const element = document.querySelector(selector);
    if (!element) return;

    const rect = element.getBoundingClientRect();
    const highlight = document.createElement('div');
    highlight.className = 'onboarding-highlight';
    highlight.style.top = `${rect.top - 4}px`;
    highlight.style.left = `${rect.left - 4}px`;
    highlight.style.width = `${rect.width + 8}px`;
    highlight.style.height = `${rect.height + 8}px`;

    document.body.appendChild(highlight);
  }

  bindTooltipEvents() {
    const buttons = this.tooltip.querySelectorAll('button');
    buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        const action = e.target.dataset.action;
        switch (action) {
          case 'next':
            this.nextStep();
            break;
          case 'prev':
            this.prevStep();
            break;
          case 'skip':
            this.completeOnboarding();
            break;
        }
      });
    });
  }

  nextStep() {
    if (this.currentStep < this.totalSteps - 1) {
      this.showStep(this.currentStep + 1);
    } else {
      this.completeOnboarding();
    }
  }

  prevStep() {
    if (this.currentStep > 0) {
      this.showStep(this.currentStep - 1);
    }
  }

  async completeOnboarding() {
    // 保存完成状态
    await chrome.storage.sync.set({
      hasCompletedOnboarding: true,
      onboardingVersion: '2.0.0',
      onboardingCompletedAt: Date.now()
    });

    // 清理UI
    if (this.overlay) {
      this.overlay.remove();
    }
    if (this.tooltip) {
      this.tooltip.remove();
    }

    const highlight = document.querySelector('.onboarding-highlight');
    if (highlight) {
      highlight.remove();
    }

    this.isActive = false;

    // 显示完成消息
    this.showCompletionMessage();
  }

  showCompletionMessage() {
    // 创建完成提示
    const message = document.createElement('div');
    message.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #34c759;
      color: white;
      padding: 16px 20px;
      border-radius: 8px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
      font-size: 14px;
      font-weight: 500;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      animation: slideIn 0.3s ease;
    `;
    
    message.innerHTML = '🎉 引导完成！开始使用 Flomo 扩展吧！';
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(message);
    
    // 3秒后自动移除
    setTimeout(() => {
      message.remove();
      style.remove();
    }, 3000);
  }

  // 手动重置引导（用于测试）
  async resetOnboarding() {
    await chrome.storage.sync.remove(['hasCompletedOnboarding', 'onboardingVersion', 'onboardingCompletedAt']);
    console.log('引导状态已重置');
  }
}

// 导出引导管理器
const onboardingManager = new OnboardingManager();

// 添加到全局作用域以便调试
if (typeof window !== 'undefined') {
  window.onboardingManager = onboardingManager;
}
