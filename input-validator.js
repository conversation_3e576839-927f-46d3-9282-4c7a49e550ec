// 输入验证和清理模块
class InputValidator {
  constructor() {
    this.maxContentLength = 10000;
    this.maxApiKeyLength = 200;
    this.maxUrlLength = 2000;
    
    // 危险的HTML标签和属性
    this.dangerousTags = ['script', 'iframe', 'object', 'embed', 'form', 'input', 'button'];
    this.dangerousAttributes = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur'];
    
    // URL白名单模式
    this.allowedUrlPatterns = [
      /^https:\/\/flomoapp\.com\//,
      /^https:\/\/.*\.flomoapp\.com\//,
      /^https:\/\/api\.siliconflow\.cn\//,
      /^https:\/\/openrouter\.ai\//,
      /^https:\/\/api\.deepseek\.com\//,
      /^https:\/\/api\.moonshot\.cn\//
    ];
  }

  // 验证和清理文本内容
  validateAndCleanContent(content) {
    if (typeof content !== 'string') {
      throw new Error('内容必须是字符串类型');
    }

    // 长度验证
    if (content.length > this.maxContentLength) {
      throw new Error(`内容过长，最大允许${this.maxContentLength}个字符`);
    }

    // 基本清理：移除控制字符和零宽字符
    let cleaned = content
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 控制字符
      .replace(/[\u200B-\u200D\uFEFF]/g, '') // 零宽字符
      .replace(/\r\n/g, '\n') // 统一换行符
      .trim();

    // 检查是否包含可疑的脚本内容
    if (this.containsSuspiciousContent(cleaned)) {
      throw new Error('内容包含不安全的脚本代码');
    }

    return cleaned;
  }

  // 验证和清理HTML内容
  validateAndCleanHTML(html) {
    if (typeof html !== 'string') {
      throw new Error('HTML内容必须是字符串类型');
    }

    // 长度验证
    if (html.length > this.maxContentLength * 2) { // HTML允许更长
      throw new Error('HTML内容过长');
    }

    // 创建临时DOM元素进行清理
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // 移除危险标签
    this.dangerousTags.forEach(tag => {
      const elements = tempDiv.querySelectorAll(tag);
      elements.forEach(el => el.remove());
    });

    // 移除危险属性
    const allElements = tempDiv.querySelectorAll('*');
    allElements.forEach(el => {
      this.dangerousAttributes.forEach(attr => {
        if (el.hasAttribute(attr)) {
          el.removeAttribute(attr);
        }
      });

      // 移除javascript:协议的链接
      if (el.hasAttribute('href') && el.getAttribute('href').toLowerCase().startsWith('javascript:')) {
        el.removeAttribute('href');
      }

      if (el.hasAttribute('src') && el.getAttribute('src').toLowerCase().startsWith('javascript:')) {
        el.removeAttribute('src');
      }
    });

    return tempDiv.innerHTML;
  }

  // 验证URL
  validateUrl(url) {
    if (typeof url !== 'string') {
      throw new Error('URL必须是字符串类型');
    }

    if (url.length > this.maxUrlLength) {
      throw new Error('URL过长');
    }

    // 基本URL格式验证
    let urlObj;
    try {
      urlObj = new URL(url);
    } catch (error) {
      throw new Error('URL格式无效');
    }

    // 必须是HTTPS协议
    if (urlObj.protocol !== 'https:') {
      throw new Error('URL必须使用HTTPS协议');
    }

    // 检查是否在白名单中
    const isAllowed = this.allowedUrlPatterns.some(pattern => pattern.test(url));
    if (!isAllowed) {
      throw new Error('URL不在允许的域名列表中');
    }

    return url;
  }

  // 验证API密钥
  validateApiKey(apiKey) {
    if (typeof apiKey !== 'string') {
      throw new Error('API密钥必须是字符串类型');
    }

    if (apiKey.length === 0) {
      throw new Error('API密钥不能为空');
    }

    if (apiKey.length > this.maxApiKeyLength) {
      throw new Error('API密钥过长');
    }

    // 检查是否包含可疑字符
    if (!/^[a-zA-Z0-9\-_\.]+$/.test(apiKey)) {
      throw new Error('API密钥包含无效字符');
    }

    return apiKey.trim();
  }

  // 验证提供商ID
  validateProviderId(providerId) {
    if (typeof providerId !== 'string') {
      throw new Error('提供商ID必须是字符串类型');
    }

    const allowedProviders = ['siliconflow', 'openrouter', 'deepseek', 'moonshot'];
    if (!allowedProviders.includes(providerId)) {
      throw new Error('不支持的AI服务提供商');
    }

    return providerId;
  }

  // 验证模型名称
  validateModelName(modelName) {
    if (typeof modelName !== 'string') {
      throw new Error('模型名称必须是字符串类型');
    }

    if (modelName.length === 0) {
      throw new Error('模型名称不能为空');
    }

    if (modelName.length > 100) {
      throw new Error('模型名称过长');
    }

    // 只允许字母、数字、连字符、下划线、点号和斜杠
    if (!/^[a-zA-Z0-9\-_\.\/]+$/.test(modelName)) {
      throw new Error('模型名称包含无效字符');
    }

    return modelName;
  }

  // 检查是否包含可疑内容
  containsSuspiciousContent(content) {
    const suspiciousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /data:text\/html/gi,
      /on\w+\s*=/gi,
      /<iframe\b/gi,
      /<object\b/gi,
      /<embed\b/gi
    ];

    return suspiciousPatterns.some(pattern => pattern.test(content));
  }

  // 清理用户输入的通用方法
  sanitizeInput(input, type = 'text') {
    try {
      switch (type) {
        case 'content':
          return this.validateAndCleanContent(input);
        case 'html':
          return this.validateAndCleanHTML(input);
        case 'url':
          return this.validateUrl(input);
        case 'apikey':
          return this.validateApiKey(input);
        case 'provider':
          return this.validateProviderId(input);
        case 'model':
          return this.validateModelName(input);
        default:
          return this.validateAndCleanContent(input);
      }
    } catch (error) {
      console.error('输入验证失败:', { type, error: error.message });
      throw error;
    }
  }

  // 批量验证对象的所有属性
  validateObject(obj, schema) {
    const result = {};
    const errors = [];

    for (const [key, config] of Object.entries(schema)) {
      try {
        if (config.required && !(key in obj)) {
          throw new Error(`缺少必需字段: ${key}`);
        }

        if (key in obj) {
          result[key] = this.sanitizeInput(obj[key], config.type);
        }
      } catch (error) {
        errors.push({ field: key, error: error.message });
      }
    }

    if (errors.length > 0) {
      throw new Error(`验证失败: ${errors.map(e => `${e.field}: ${e.error}`).join(', ')}`);
    }

    return result;
  }
}

// 创建全局实例
const inputValidator = new InputValidator();

// 如果在扩展环境中，导出到全局
if (typeof window !== 'undefined') {
  window.inputValidator = inputValidator;
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = InputValidator;
}
