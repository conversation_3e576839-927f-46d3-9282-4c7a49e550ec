// 安全存储管理器 - 使用Web Crypto API加密敏感数据
class SecureStorage {
  constructor() {
    this.keyCache = new Map(); // 缓存解密后的密钥（内存中）
    this.algorithm = {
      name: 'AES-GCM',
      length: 256
    };
    this.keyUsage = ['encrypt', 'decrypt'];
    this.storagePrefix = 'flomo_secure_';
    this.masterKeyName = 'flomo_master_key';
  }

  // 生成主密钥
  async generateMasterKey() {
    try {
      const key = await crypto.subtle.generateKey(
        this.algorithm,
        false, // 不可导出
        this.keyUsage
      );

      // 将密钥存储在IndexedDB中（比localStorage更安全）
      await this.storeMasterKey(key);
      return key;
    } catch (error) {
      console.error('生成主密钥失败:', error);
      throw new Error('无法生成加密密钥');
    }
  }

  // 存储主密钥到IndexedDB
  async storeMasterKey(key) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('FlomoSecureStorage', 1);

      request.onerror = () => reject(new Error('无法打开安全存储数据库'));

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('keys')) {
          db.createObjectStore('keys');
        }
      };

      request.onsuccess = (event) => {
        const db = event.target.result;
        const transaction = db.transaction(['keys'], 'readwrite');
        const store = transaction.objectStore('keys');

        store.put(key, this.masterKeyName);

        transaction.oncomplete = () => {
          db.close();
          resolve();
        };

        transaction.onerror = () => {
          db.close();
          reject(new Error('存储主密钥失败'));
        };
      };
    });
  }

  // 获取主密钥
  async getMasterKey() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('FlomoSecureStorage', 1);

      request.onerror = () => reject(new Error('无法打开安全存储数据库'));

      request.onsuccess = (event) => {
        const db = event.target.result;
        const transaction = db.transaction(['keys'], 'readonly');
        const store = transaction.objectStore('keys');
        const getRequest = store.get(this.masterKeyName);

        getRequest.onsuccess = () => {
          db.close();
          if (getRequest.result) {
            resolve(getRequest.result);
          } else {
            // 如果没有主密钥，生成一个新的
            this.generateMasterKey().then(resolve).catch(reject);
          }
        };

        getRequest.onerror = () => {
          db.close();
          reject(new Error('获取主密钥失败'));
        };
      };
    });
  }

  // 加密数据
  async encryptData(data) {
    try {
      const key = await this.getMasterKey();
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(JSON.stringify(data));

      // 生成随机IV
      const iv = crypto.getRandomValues(new Uint8Array(12));

      const encryptedBuffer = await crypto.subtle.encrypt(
        {
          name: this.algorithm.name,
          iv: iv
        },
        key,
        dataBuffer
      );

      // 将IV和加密数据组合
      const combined = new Uint8Array(iv.length + encryptedBuffer.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encryptedBuffer), iv.length);

      // 转换为Base64存储
      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      console.error('数据加密失败:', error);
      throw new Error('数据加密失败');
    }
  }

  // 解密数据
  async decryptData(encryptedData) {
    try {
      const key = await this.getMasterKey();

      // 从Base64解码
      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );

      // 分离IV和加密数据
      const iv = combined.slice(0, 12);
      const encryptedBuffer = combined.slice(12);

      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: this.algorithm.name,
          iv: iv
        },
        key,
        encryptedBuffer
      );

      const decoder = new TextDecoder();
      const decryptedText = decoder.decode(decryptedBuffer);

      return JSON.parse(decryptedText);
    } catch (error) {
      console.error('数据解密失败:', error);
      throw new Error('数据解密失败');
    }
  }

  // 安全存储API密钥
  async setApiKey(provider, apiKey) {
    try {
      if (!provider || !apiKey) {
        throw new Error('提供商和API密钥不能为空');
      }

      // 验证API密钥格式
      if (typeof inputValidator !== 'undefined') {
        inputValidator.validateApiKey(apiKey);
        inputValidator.validateProviderId(provider);
      }

      const encryptedKey = await this.encryptData({
        provider,
        apiKey,
        timestamp: Date.now(),
        version: '1.0'
      });

      // 存储到Chrome storage
      const storageKey = `${this.storagePrefix}${provider}`;
      await chrome.storage.local.set({
        [storageKey]: encryptedKey
      });

      // 缓存解密后的密钥（仅在内存中）
      this.keyCache.set(provider, apiKey);

      console.log(`✅ API密钥已安全存储: ${provider}`);
      return true;
    } catch (error) {
      console.error('存储API密钥失败:', error);
      throw error;
    }
  }

  // 安全获取API密钥
  async getApiKey(provider) {
    try {
      if (!provider) {
        throw new Error('提供商不能为空');
      }

      // 先检查内存缓存
      if (this.keyCache.has(provider)) {
        return this.keyCache.get(provider);
      }

      const storageKey = `${this.storagePrefix}${provider}`;
      const result = await chrome.storage.local.get(storageKey);

      if (!result[storageKey]) {
        return null; // 没有找到密钥
      }

      const decryptedData = await this.decryptData(result[storageKey]);

      // 验证数据完整性
      if (decryptedData.provider !== provider) {
        throw new Error('密钥数据损坏');
      }

      // 缓存到内存
      this.keyCache.set(provider, decryptedData.apiKey);

      return decryptedData.apiKey;
    } catch (error) {
      console.error('获取API密钥失败:', error);
      // 如果解密失败，可能是密钥损坏，删除它
      await this.removeApiKey(provider);
      return null;
    }
  }

  // 删除API密钥
  async removeApiKey(provider) {
    try {
      const storageKey = `${this.storagePrefix}${provider}`;
      await chrome.storage.local.remove(storageKey);
      this.keyCache.delete(provider);
      console.log(`🗑️ API密钥已删除: ${provider}`);
      return true;
    } catch (error) {
      console.error('删除API密钥失败:', error);
      throw error;
    }
  }

  // 获取所有已存储的提供商
  async getStoredProviders() {
    try {
      const result = await chrome.storage.local.get();
      const providers = [];

      for (const key in result) {
        if (key.startsWith(this.storagePrefix)) {
          const provider = key.replace(this.storagePrefix, '');
          providers.push(provider);
        }
      }

      return providers;
    } catch (error) {
      console.error('获取存储的提供商失败:', error);
      return [];
    }
  }

  // 清理所有加密存储
  async clearAll() {
    try {
      const providers = await this.getStoredProviders();

      for (const provider of providers) {
        await this.removeApiKey(provider);
      }

      // 清理内存缓存
      this.keyCache.clear();

      console.log('🧹 所有加密存储已清理');
      return true;
    } catch (error) {
      console.error('清理加密存储失败:', error);
      throw error;
    }
  }

  // 检查Web Crypto API支持
  static isSupported() {
    return typeof crypto !== 'undefined' &&
      typeof crypto.subtle !== 'undefined' &&
      typeof indexedDB !== 'undefined';
  }

  // 迁移旧的明文存储到加密存储
  async migrateFromPlainStorage() {
    try {
      console.log('🔄 开始迁移明文存储到加密存储...');

      // 获取旧的存储数据
      const oldData = await chrome.storage.sync.get('aiApiKeys');

      if (oldData.aiApiKeys && typeof oldData.aiApiKeys === 'object') {
        let migratedCount = 0;

        for (const [provider, apiKey] of Object.entries(oldData.aiApiKeys)) {
          if (apiKey && typeof apiKey === 'string') {
            await this.setApiKey(provider, apiKey);
            migratedCount++;
          }
        }

        if (migratedCount > 0) {
          // 删除旧的明文存储
          await chrome.storage.sync.remove('aiApiKeys');
          console.log(`✅ 已迁移 ${migratedCount} 个API密钥到加密存储`);
        }
      }

      return true;
    } catch (error) {
      console.error('迁移存储失败:', error);
      return false;
    }
  }
}

// 创建全局安全存储实例
const secureStorage = new SecureStorage();

// 导出到全局作用域
if (typeof window !== 'undefined') {
  window.secureStorage = secureStorage;
} else if (typeof self !== 'undefined') {
  // Service Worker环境
  self.secureStorage = secureStorage;
  self.SecureStorage = SecureStorage;
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SecureStorage;
}
