// Service Worker专用错误处理器
class ServiceWorkerErrorHandler {
  constructor() {
    this.errorHistory = [];
    this.config = ErrorConfig || {
      retry: {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 30000,
        backoffMultiplier: 2,
        jitter: true
      },
      timeout: {
        default: 30000,
        ai: 60000,
        flomo: 15000,
        config: 5000
      }
    };
    this.init();
  }

  init() {
    // Service Worker环境的错误监听
    if (typeof self !== 'undefined') {
      self.addEventListener('error', (event) => {
        this.logError('Service Worker Error', event.error);
      });

      self.addEventListener('unhandledrejection', (event) => {
        this.logError('Service Worker Promise Rejection', event.reason);
      });
    }
  }

  // 记录错误
  logError(type, error, context = {}) {
    const errorInfo = {
      type,
      message: error?.message || error,
      stack: error?.stack,
      timestamp: Date.now(),
      context,
      userAgent: 'Service Worker',
      url: 'Service Worker Context'
    };

    this.errorHistory.push(errorInfo);

    // 保持错误历史在合理范围内
    if (this.errorHistory.length > 100) {
      this.errorHistory = this.errorHistory.slice(-50);
    }

    console.error(`[${type}]`, error, context);
  }

  // 网络错误处理（Service Worker专用）
  async handleNetworkError(initialError, operation, requestFn, context = {}) {
    let lastError = initialError;
    let attempt = 0;
    const maxAttempts = this.config.retry.maxAttempts;

    while (attempt < maxAttempts) {
      attempt++;

      try {
        console.log(`🔄 尝试执行 ${operation} (第${attempt}次)`);

        const result = await this.executeWithTimeout(requestFn, operation);

        if (attempt > 1) {
          console.log(`✅ ${operation} 重试成功 (共${attempt}次尝试)`);
        }

        return result;
      } catch (error) {
        lastError = error;
        console.error(`❌ ${operation} 第${attempt}次尝试失败:`, error?.message || '未知错误');

        this.logError(`${operation}_attempt_${attempt}`, error, {
          ...context,
          attempt,
          maxAttempts
        });

        if (attempt < maxAttempts) {
          const delay = this.calculateDelay(attempt);
          console.log(`⏳ ${delay}ms后重试...`);
          await this.delay(delay);
        }
      }
    }

    // 所有重试都失败了
    const finalError = new Error(
      `${operation} 失败: ${this.getFriendlyErrorMessage(lastError)} (已重试${maxAttempts}次)`
    );
    finalError.originalError = lastError;
    finalError.attempts = maxAttempts;

    this.logError(`${operation}_final_failure`, finalError, context);
    throw finalError;
  }

  // 带超时的执行
  async executeWithTimeout(fn, operation) {
    const timeout = this.getTimeoutForOperation(operation);

    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`${operation} 超时 (${timeout}ms)`));
      }, timeout);

      try {
        const result = await fn();
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  // 计算重试延迟
  calculateDelay(attempt) {
    const baseDelay = this.config.retry.baseDelay;
    const multiplier = this.config.retry.backoffMultiplier;
    const maxDelay = this.config.retry.maxDelay;

    let delay = baseDelay * Math.pow(multiplier, attempt - 1);

    // 添加抖动避免雷群效应
    if (this.config.retry.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }

    return Math.min(delay, maxDelay);
  }

  // 获取操作超时时间
  getTimeoutForOperation(operation) {
    const operationKey = operation.toLowerCase();

    if (operationKey.includes('ai')) {
      return this.config.timeout.ai;
    } else if (operationKey.includes('flomo')) {
      return this.config.timeout.flomo;
    } else if (operationKey.includes('config')) {
      return this.config.timeout.config;
    }

    return this.config.timeout.default;
  }

  // 友好的错误消息
  getFriendlyErrorMessage(error) {
    const message = error?.message || error;

    if (typeof message !== 'string') {
      return '未知错误';
    }

    // 网络相关错误
    if (message.includes('Failed to fetch') || message.includes('NetworkError')) {
      return '网络连接失败，请检查网络连接';
    }

    if (message.includes('timeout') || message.includes('超时')) {
      return '请求超时，请稍后重试';
    }

    // API相关错误
    if (message.includes('401') || message.includes('Unauthorized')) {
      return 'API密钥无效，请检查配置';
    }

    if (message.includes('403') || message.includes('Forbidden')) {
      return 'API访问被拒绝，请检查权限';
    }

    if (message.includes('429') || message.includes('Too Many Requests')) {
      return 'API请求过于频繁，请稍后重试';
    }

    if (message.includes('500') || message.includes('Internal Server Error')) {
      return 'API服务器错误，请稍后重试';
    }

    // Chrome扩展相关错误
    if (message.includes('Extension context invalidated')) {
      return '扩展已更新，请刷新页面';
    }

    return message;
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 获取错误统计
  getErrorStats() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const recentErrors = this.errorHistory.filter(error =>
      now - error.timestamp < oneHour
    );

    const errorTypes = {};
    recentErrors.forEach(error => {
      errorTypes[error.type] = (errorTypes[error.type] || 0) + 1;
    });

    return {
      total: this.errorHistory.length,
      recent: recentErrors.length,
      types: errorTypes,
      lastError: this.errorHistory[this.errorHistory.length - 1]
    };
  }

  // 清理错误历史
  clearErrorHistory() {
    this.errorHistory = [];
    console.log('🧹 错误历史已清理');
  }
}

// 创建Service Worker错误处理器实例
const swErrorHandler = new ServiceWorkerErrorHandler();

// 导出到全局作用域
if (typeof self !== 'undefined') {
  self.swErrorHandler = swErrorHandler;
  // 为了兼容性，也导出为errorHandler
  self.errorHandler = swErrorHandler;
}
