// 智能请求缓存管理器
class RequestCache {
  constructor(options = {}) {
    this.cache = new Map();
    this.config = {
      // 默认缓存时间（毫秒）
      defaultTTL: options.defaultTTL || 300000, // 5分钟
      // 最大缓存条目数
      maxEntries: options.maxEntries || 100,
      // 不同功能的缓存时间
      functionTTL: {
        'generateTags': 600000,      // 标签生成：10分钟
        'summarizeContent': 300000,  // 内容摘要：5分钟
        'formatContent': 180000,     // 格式整理：3分钟
        'translateContent': 900000,  // 中英对照：15分钟
        'validateApiKey': 1800000    // API密钥验证：30分钟
      },
      // 启用压缩存储
      enableCompression: options.enableCompression !== false,
      // 缓存命中率统计
      enableStats: options.enableStats !== false
    };

    // 统计信息
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0,
      compressionSaved: 0
    };

    // 定期清理过期缓存
    this.startCleanupTimer();
  }

  // 生成缓存键
  generateCacheKey(functionName, content, options = {}) {
    // 创建内容的哈希值以减少键长度
    const contentHash = this.hashString(content);
    const optionsHash = this.hashString(JSON.stringify(options));

    return `${functionName}:${contentHash}:${optionsHash}`;
  }

  // 简单字符串哈希函数
  hashString(str) {
    let hash = 0;
    if (str.length === 0) return hash;

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash).toString(36);
  }

  // 压缩数据
  compressData(data) {
    if (!this.config.enableCompression) return data;

    try {
      const jsonString = JSON.stringify(data);
      // 简单的压缩：移除多余空格和重复字符
      const compressed = jsonString
        .replace(/\s+/g, ' ')
        .replace(/([{,])\s+/g, '$1')
        .replace(/\s+([}])/g, '$1');

      const saved = jsonString.length - compressed.length;
      this.stats.compressionSaved += saved;

      return {
        compressed: true,
        data: compressed,
        originalSize: jsonString.length,
        compressedSize: compressed.length
      };
    } catch (error) {
      console.warn('数据压缩失败，使用原始数据:', error);
      return data;
    }
  }

  // 解压数据
  decompressData(compressedData) {
    if (!compressedData || !compressedData.compressed) {
      return compressedData;
    }

    try {
      return JSON.parse(compressedData.data);
    } catch (error) {
      console.warn('数据解压失败:', error);
      return null;
    }
  }

  // 获取缓存
  async get(functionName, content, options = {}) {
    const key = this.generateCacheKey(functionName, content, options);
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // 检查是否过期
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // 更新访问时间
    entry.lastAccessed = Date.now();
    entry.accessCount++;

    this.stats.hits++;

    // 解压并返回数据
    const data = this.decompressData(entry.data);

    console.log(`🎯 缓存命中: ${functionName}`, {
      key: key.substring(0, 20) + '...',
      accessCount: entry.accessCount,
      age: Date.now() - entry.createdAt
    });

    return data;
  }

  // 设置缓存
  async set(functionName, content, options = {}, data) {
    const key = this.generateCacheKey(functionName, content, options);
    const ttl = this.config.functionTTL[functionName] || this.config.defaultTTL;

    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxEntries) {
      this.evictLeastRecentlyUsed();
    }

    // 压缩数据
    const compressedData = this.compressData(data);

    const entry = {
      data: compressedData,
      createdAt: Date.now(),
      expiresAt: Date.now() + ttl,
      lastAccessed: Date.now(),
      accessCount: 1,
      functionName,
      contentLength: content.length
    };

    this.cache.set(key, entry);
    this.stats.sets++;

    console.log(`💾 缓存已设置: ${functionName}`, {
      key: key.substring(0, 20) + '...',
      ttl: ttl / 1000 + 's',
      size: this.cache.size
    });

    return data;
  }

  // 淘汰最少使用的缓存项
  evictLeastRecentlyUsed() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.stats.evictions++;
      console.log('🗑️ 淘汰最旧缓存项:', oldestKey.substring(0, 20) + '...');
    }
  }

  // 清理过期缓存
  cleanup() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 个过期缓存项`);
    }

    return cleanedCount;
  }

  // 启动定期清理定时器
  startCleanupTimer() {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanup();
    }, 300000);
  }

  // 清空所有缓存
  clear() {
    const size = this.cache.size;
    this.cache.clear();
    console.log(`🗑️ 已清空所有缓存 (${size} 项)`);
  }

  // 删除特定功能的缓存
  clearFunction(functionName) {
    let deletedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.functionName === functionName) {
        this.cache.delete(key);
        deletedCount++;
      }
    }

    console.log(`🗑️ 已清空 ${functionName} 的缓存 (${deletedCount} 项)`);
    return deletedCount;
  }

  // 获取缓存统计信息
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      currentSize: this.cache.size,
      maxSize: this.config.maxEntries,
      compressionSavedKB: (this.stats.compressionSaved / 1024).toFixed(2)
    };
  }

  // 获取缓存详情
  getCacheDetails() {
    const details = [];

    for (const [key, entry] of this.cache.entries()) {
      details.push({
        key: key.substring(0, 30) + '...',
        functionName: entry.functionName,
        createdAt: new Date(entry.createdAt).toLocaleString(),
        expiresAt: new Date(entry.expiresAt).toLocaleString(),
        accessCount: entry.accessCount,
        contentLength: entry.contentLength,
        age: Math.round((Date.now() - entry.createdAt) / 1000) + 's'
      });
    }

    return details.sort((a, b) => b.accessCount - a.accessCount);
  }

  // 预热缓存（可选功能）
  async warmup(commonRequests = []) {
    console.log('🔥 开始预热缓存...');

    for (const request of commonRequests) {
      try {
        // 这里可以预先执行一些常见的请求
        console.log(`预热: ${request.functionName}`);
      } catch (error) {
        console.warn(`预热失败: ${request.functionName}`, error);
      }
    }
  }

  // 导出缓存数据（用于调试）
  exportCache() {
    const exported = [];

    for (const [key, entry] of this.cache.entries()) {
      exported.push({
        key,
        functionName: entry.functionName,
        createdAt: entry.createdAt,
        expiresAt: entry.expiresAt,
        accessCount: entry.accessCount,
        contentLength: entry.contentLength
      });
    }

    return {
      cache: exported,
      stats: this.getStats(),
      config: this.config
    };
  }
}

// 创建全局缓存实例
const requestCache = new RequestCache({
  defaultTTL: 300000, // 5分钟
  maxEntries: 50,     // 限制缓存大小
  enableCompression: true,
  enableStats: true
});

// 导出到全局作用域
if (typeof window !== 'undefined') {
  window.requestCache = requestCache;
} else if (typeof self !== 'undefined') {
  // Service Worker环境
  self.requestCache = requestCache;
  self.RequestCache = RequestCache;
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RequestCache;
}
