// 安全性测试套件
class SecurityTestSuite {
  constructor() {
    this.testResults = new Map();
    this.originalConsoleLog = console.log;
    this.originalConsoleError = console.error;
    this.capturedLogs = [];
  }

  // 开始捕获控制台输出
  startLogCapture() {
    this.capturedLogs = [];
    console.log = (...args) => {
      this.capturedLogs.push({ type: 'log', args });
      this.originalConsoleLog.apply(console, args);
    };
    console.error = (...args) => {
      this.capturedLogs.push({ type: 'error', args });
      this.originalConsoleError.apply(console, args);
    };
  }

  // 停止捕获控制台输出
  stopLogCapture() {
    console.log = this.originalConsoleLog;
    console.error = this.originalConsoleError;
    return this.capturedLogs;
  }

  // 显示测试结果
  showResult(elementId, passed, message, details = '') {
    const element = document.getElementById(elementId);
    element.style.display = 'block';
    element.className = `test-result ${passed ? 'result-pass' : 'result-fail'}`;
    element.innerHTML = `
      <strong>${passed ? '✅ 通过' : '❌ 失败'}</strong>: ${message}
      ${details ? `<br><small>${details}</small>` : ''}
    `;
    
    this.testResults.set(elementId, { passed, message, details });
    this.updateSummary();
  }

  // 更新测试汇总
  updateSummary() {
    const summary = document.getElementById('test-summary');
    const results = Array.from(this.testResults.values());
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    
    if (total === 0) return;
    
    const passRate = ((passed / total) * 100).toFixed(1);
    summary.className = `test-result ${passed === total ? 'result-pass' : 'result-fail'}`;
    summary.innerHTML = `
      <strong>测试汇总</strong>: ${passed}/${total} 项测试通过 (${passRate}%)
      <br><small>安全等级: ${this.getSecurityLevel(passRate)}</small>
    `;
  }

  // 获取安全等级
  getSecurityLevel(passRate) {
    if (passRate >= 90) return '🟢 高';
    if (passRate >= 70) return '🟡 中';
    return '🔴 低';
  }
}

const securityTest = new SecurityTestSuite();

// 测试API密钥日志泄露
function testApiKeyLogging() {
  const apiKey = document.getElementById('api-key-input').value;
  
  if (!apiKey) {
    securityTest.showResult('api-key-result', false, '请输入测试API密钥');
    return;
  }

  // 开始捕获日志
  securityTest.startLogCapture();
  
  try {
    // 模拟AI服务管理器的日志记录
    if (typeof aiServiceManager !== 'undefined') {
      aiServiceManager.safeLog('info', '测试AI请求', {
        provider: 'test',
        model: 'test-model',
        hasApiKey: !!apiKey
      });
    } else {
      // 模拟日志记录
      console.log('🚀 AI请求开始', {
        provider: 'test',
        model: 'test-model',
        hasApiKey: !!apiKey
      });
    }
    
    // 停止捕获并检查结果
    const logs = securityTest.stopLogCapture();
    
    // 检查是否有API密钥泄露
    const hasLeakage = logs.some(log => 
      log.args.some(arg => 
        typeof arg === 'string' && arg.includes(apiKey) ||
        typeof arg === 'object' && JSON.stringify(arg).includes(apiKey)
      )
    );
    
    if (hasLeakage) {
      securityTest.showResult('api-key-result', false, 
        'API密钥在日志中泄露', 
        '发现日志中包含完整或部分API密钥信息'
      );
    } else {
      securityTest.showResult('api-key-result', true, 
        'API密钥未在日志中泄露', 
        '日志记录安全，未发现API密钥信息'
      );
    }
    
  } catch (error) {
    securityTest.stopLogCapture();
    securityTest.showResult('api-key-result', false, 
      '测试执行失败', 
      error.message
    );
  }
}

// 测试网络请求泄露
function testNetworkLeakage() {
  // 模拟网络请求监控
  const originalFetch = window.fetch;
  let requestCaptured = false;
  let hasLeakage = false;
  
  window.fetch = function(...args) {
    requestCaptured = true;
    const [url, options] = args;
    
    // 检查URL中是否包含敏感信息
    if (url && typeof url === 'string' && url.includes('sk-')) {
      hasLeakage = true;
    }
    
    // 检查请求体中是否包含敏感信息
    if (options && options.body) {
      const body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
      if (body.includes('sk-') && !body.includes('"apiKey"')) {
        hasLeakage = true;
      }
    }
    
    // 检查请求头中是否安全处理了认证信息
    if (options && options.headers) {
      const headers = typeof options.headers === 'object' ? options.headers : {};
      const authHeader = headers['Authorization'] || headers['authorization'];
      if (authHeader && typeof authHeader === 'string' && authHeader.length > 50) {
        // 认证头过长可能包含完整密钥，应该被截断或隐藏
        hasLeakage = true;
      }
    }
    
    return originalFetch.apply(this, args);
  };
  
  // 恢复原始fetch
  setTimeout(() => {
    window.fetch = originalFetch;
    
    if (!requestCaptured) {
      securityTest.showResult('network-result', true, 
        '未检测到网络请求', 
        '当前环境下未发起网络请求，无法完全验证'
      );
    } else if (hasLeakage) {
      securityTest.showResult('network-result', false, 
        '网络请求中发现敏感信息泄露', 
        '请求中包含未经处理的敏感信息'
      );
    } else {
      securityTest.showResult('network-result', true, 
        '网络请求安全', 
        '未在网络请求中发现敏感信息泄露'
      );
    }
  }, 1000);
  
  // 触发一个模拟请求
  try {
    fetch('/test-endpoint', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer sk-test123...',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        content: 'test content',
        apiKey: 'sk-hidden'
      })
    }).catch(() => {
      // 忽略网络错误，我们只关心请求格式
    });
  } catch (error) {
    // 忽略错误
  }
}

// 测试XSS防护
function testXSSProtection() {
  const xssInput = document.getElementById('xss-input').value;
  
  if (!xssInput) {
    securityTest.showResult('xss-result', false, '请输入XSS测试代码');
    return;
  }

  try {
    // 使用输入验证器测试
    if (typeof inputValidator !== 'undefined') {
      try {
        const cleaned = inputValidator.validateAndCleanHTML(xssInput);
        
        // 检查是否成功清理了危险内容
        const hasDangerousContent = cleaned.includes('<script') || 
                                   cleaned.includes('javascript:') || 
                                   cleaned.includes('onerror=') ||
                                   cleaned.includes('onload=');
        
        if (hasDangerousContent) {
          securityTest.showResult('xss-result', false, 
            'XSS防护不完整', 
            '清理后的内容仍包含危险脚本'
          );
        } else {
          securityTest.showResult('xss-result', true, 
            'XSS防护有效', 
            `原始内容已被安全清理: ${cleaned.substring(0, 100)}...`
          );
        }
      } catch (error) {
        securityTest.showResult('xss-result', true, 
          'XSS防护有效', 
          `输入验证器正确拒绝了危险内容: ${error.message}`
        );
      }
    } else {
      securityTest.showResult('xss-result', false, 
        '输入验证器未加载', 
        '无法测试XSS防护功能'
      );
    }
  } catch (error) {
    securityTest.showResult('xss-result', false, 
      '测试执行失败', 
      error.message
    );
  }
}

// 测试URL验证
function testURLValidation() {
  const urlInput = document.getElementById('url-input').value;
  const urls = urlInput.split('\n').filter(url => url.trim());
  
  if (urls.length === 0) {
    securityTest.showResult('url-result', false, '请输入测试URL');
    return;
  }

  let passedCount = 0;
  let totalCount = urls.length;
  const results = [];

  urls.forEach(url => {
    try {
      if (typeof inputValidator !== 'undefined') {
        inputValidator.validateUrl(url.trim());
        results.push(`❌ ${url} - 应该被拒绝但通过了验证`);
      } else {
        results.push(`⚠️ ${url} - 无法测试（验证器未加载）`);
      }
    } catch (error) {
      results.push(`✅ ${url} - 正确拒绝: ${error.message}`);
      passedCount++;
    }
  });

  const passed = passedCount === totalCount;
  securityTest.showResult('url-result', passed, 
    `URL验证测试 ${passedCount}/${totalCount} 项通过`, 
    results.join('<br>')
  );
}

// 测试内容长度限制
function testContentLengthLimit() {
  const longContent = 'A'.repeat(15000); // 超过10000字符限制
  
  try {
    if (typeof inputValidator !== 'undefined') {
      try {
        inputValidator.validateAndCleanContent(longContent);
        securityTest.showResult('length-result', false, 
          '内容长度限制无效', 
          '超长内容未被拒绝'
        );
      } catch (error) {
        securityTest.showResult('length-result', true, 
          '内容长度限制有效', 
          `正确拒绝了${longContent.length}字符的超长内容: ${error.message}`
        );
      }
    } else {
      securityTest.showResult('length-result', false, 
        '输入验证器未加载', 
        '无法测试内容长度限制'
      );
    }
  } catch (error) {
    securityTest.showResult('length-result', false, 
      '测试执行失败', 
      error.message
    );
  }
}

// 测试存储安全
function testStorageSecurity() {
  try {
    // 检查localStorage
    const localStorageItems = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      const value = localStorage.getItem(key);
      if (value && (value.includes('sk-') || value.includes('api') || value.includes('key'))) {
        localStorageItems.push(`${key}: ${value.substring(0, 50)}...`);
      }
    }

    // 检查sessionStorage
    const sessionStorageItems = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      const value = sessionStorage.getItem(key);
      if (value && (value.includes('sk-') || value.includes('api') || value.includes('key'))) {
        sessionStorageItems.push(`${key}: ${value.substring(0, 50)}...`);
      }
    }

    const hasLeakage = localStorageItems.length > 0 || sessionStorageItems.length > 0;

    if (hasLeakage) {
      const details = [
        ...localStorageItems.map(item => `localStorage: ${item}`),
        ...sessionStorageItems.map(item => `sessionStorage: ${item}`)
      ].join('<br>');

      securityTest.showResult('storage-result', false,
        '发现存储中的敏感信息',
        details
      );
    } else {
      securityTest.showResult('storage-result', true,
        '存储安全检查通过',
        '未在本地存储中发现明文敏感信息'
      );
    }
  } catch (error) {
    securityTest.showResult('storage-result', false,
      '存储安全检查失败',
      error.message
    );
  }
}

// 自动运行所有安全测试
function runAllSecurityTests() {
  console.log('🔒 开始运行安全测试套件...');

  // 设置测试数据
  document.getElementById('api-key-input').value = 'sk-test123456789abcdef';
  document.getElementById('xss-input').value = '<script>alert("XSS")</script><img src="x" onerror="alert(\'XSS\')">';
  document.getElementById('url-input').value = 'http://malicious-site.com/api\nhttps://evil.com/steal\njavascript:alert("evil")';

  // 依次运行测试
  setTimeout(() => testApiKeyLogging(), 100);
  setTimeout(() => testNetworkLeakage(), 500);
  setTimeout(() => testXSSProtection(), 1500);
  setTimeout(() => testURLValidation(), 2000);
  setTimeout(() => testContentLengthLimit(), 2500);
  setTimeout(() => testStorageSecurity(), 3000);

  setTimeout(() => {
    console.log('✅ 安全测试套件运行完成');
    generateSecurityReport();
  }, 4000);
}

// 生成安全报告
function generateSecurityReport() {
  const results = Array.from(securityTest.testResults.entries());
  const passed = results.filter(([_, result]) => result.passed).length;
  const total = results.length;

  const report = {
    timestamp: new Date().toISOString(),
    totalTests: total,
    passedTests: passed,
    failedTests: total - passed,
    passRate: ((passed / total) * 100).toFixed(1),
    securityLevel: securityTest.getSecurityLevel((passed / total) * 100),
    details: results.map(([id, result]) => ({
      test: id,
      passed: result.passed,
      message: result.message,
      details: result.details
    }))
  };

  console.log('📊 安全测试报告:', report);

  // 可以将报告发送到服务器或保存到本地
  localStorage.setItem('flomo-security-report', JSON.stringify(report));

  return report;
}

// 页面加载完成后自动运行测试（可选）
document.addEventListener('DOMContentLoaded', () => {
  // 添加自动运行按钮
  const autoRunButton = document.createElement('button');
  autoRunButton.textContent = '🚀 自动运行所有测试';
  autoRunButton.className = 'test-button';
  autoRunButton.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 1000; background-color: #28a745;';
  autoRunButton.onclick = runAllSecurityTests;
  document.body.appendChild(autoRunButton);

  console.log('🔒 安全测试页面已加载，点击按钮开始测试');
});
