// AI功能模块
class AIFunctions {
  constructor(serviceManager) {
    this.serviceManager = serviceManager;
    this.prompts = this.initializePrompts();
  }

  // 初始化提示词模板
  initializePrompts() {
    return {
      tags: {
        system: "你是一个专业的内容标签生成助手。请根据用户提供的内容，生成3-8个相关的标签。标签应该简洁、准确，能够概括内容的主要主题和关键信息。请只返回标签，用逗号分隔，不要包含其他解释。",
        user: (content) => `请为以下内容生成标签：\n\n${content}`
      },

      translate: {
        system: "你是一个专业的中英文翻译助手。请检测用户输入的语言（中文或英文），然后提供高质量的翻译。如果是中文，翻译为英文；如果是英文，翻译为中文。请确保翻译准确、自然、符合目标语言的表达习惯。翻译完成后，请按照以下格式返回：\n\n【原文】\n[原始内容]\n\n【译文】\n[翻译内容]",
        user: (content) => `请翻译以下内容并提供中英对照：\n\n${content}`
      }
    };
  }

  // 通用AI请求执行方法 (支持流式和非流式)
  async _executeAIRequest(promptKey, content, options = {}) {
    const isStreaming = options.stream === true;
    console.log(`🤖 开始执行AI请求: ${promptKey}`, {
      contentLength: content.length,
      options,
      isStreaming
    });

    try {
      const { provider, model, apiKey } = await this.getAIConfig(options);
      console.log(`🔧 使用配置执行${promptKey}`, { provider, model, isStreaming });

      const prompt = this.prompts[promptKey];
      if (!prompt) {
        throw new Error(`未找到提示词模板: ${promptKey}`);
      }

      const messages = [
        { role: 'system', content: prompt.system },
        { role: 'user', content: prompt.user(content) }
      ];

      console.log(`📤 发送${promptKey}请求`, { messagesCount: messages.length, isStreaming });

      // 根据任务类型设置不同的参数
      const requestOptions = this._getRequestOptions(promptKey, content, options);

      if (isStreaming) {
        return this._executeStreamingRequest(provider, apiKey, model, messages, requestOptions, promptKey, options);
      } else {
        return this._executeNormalRequest(provider, apiKey, model, messages, requestOptions, promptKey);
      }
    } catch (error) {
      console.error(`❌ ${promptKey}请求失败:`, error);
      return {
        success: false,
        error: error.message,
        content: ''
      };
    }
  }

  // 执行流式请求
  async _executeStreamingRequest(provider, apiKey, model, messages, requestOptions, promptKey, options) {
    return new Promise((resolve, reject) => {
      let fullContent = '';
      let usage = null;
      let modelName = null;

      const streamOptions = {
        ...requestOptions,
        stream: true,
        onChunk: (chunk, currentContent) => {
          fullContent = currentContent;
          // 调用流式回调
          if (options.onChunk) {
            options.onChunk(chunk, currentContent);
          }
        },
        onComplete: (result) => {
          console.log(`📥 收到${promptKey}流式响应完成`, {
            contentLength: result.content.length,
            usage: result.usage
          });

          resolve({
            success: true,
            content: result.content,
            usage: result.usage,
            model: result.model
          });
        },
        onError: (error) => {
          console.error(`❌ ${promptKey}流式请求失败:`, error);
          reject(error);
        }
      };

      this.serviceManager.sendRequest(
        provider,
        apiKey,
        model,
        messages,
        streamOptions
      ).catch(reject);
    });
  }

  // 执行普通请求
  async _executeNormalRequest(provider, apiKey, model, messages, requestOptions, promptKey) {
    const result = await this.serviceManager.sendRequest(
      provider,
      apiKey,
      model,
      messages,
      requestOptions
    );

    console.log(`📥 收到${promptKey}响应`, {
      contentLength: result.content.length,
      usage: result.usage
    });

    return {
      success: true,
      content: result.content,
      usage: result.usage,
      model: result.model
    };
  }

  // 根据任务类型获取请求选项
  _getRequestOptions(promptKey, content, options) {
    const baseOptions = {
      temperature: options.temperature || 0.7,
      maxTokens: options.maxTokens
    };

    switch (promptKey) {
      case 'tags':
        return {
          ...baseOptions,
          maxTokens: baseOptions.maxTokens || 200,
          temperature: 0.3
        };
      case 'translate':
        return {
          ...baseOptions,
          maxTokens: baseOptions.maxTokens || Math.min(Math.floor(content.length * 2), 2000),
          temperature: 0.2
        };
      default:
        return baseOptions;
    }
  }

  // 生成智能标签 (支持流式)
  async generateTags(content, options = {}) {
    // 检查缓存 (流式模式不使用缓存)
    if (!options.stream && typeof requestCache !== 'undefined') {
      const cached = await requestCache.get('generateTags', content, options);
      if (cached) {
        console.log('🎯 使用缓存的标签生成结果');
        return cached;
      }
    }

    // 如果是流式模式，设置特殊的回调处理
    if (options.stream && options.onChunk) {
      const originalOnChunk = options.onChunk;
      options.onChunk = (chunk, currentContent) => {
        // 对于标签生成，我们可以尝试实时解析部分标签
        const partialTags = this._parsePartialTags(currentContent);
        originalOnChunk(chunk, currentContent, partialTags);
      };
    }

    const result = await this._executeAIRequest('tags', content, options);

    if (result.success) {
      // 解析标签
      const tags = result.content
        .split(/[,，、]/)
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0 && tag.length < 20)
        .slice(0, 8);

      console.log('✅ 标签解析完成', { tags, tagsCount: tags.length });

      const finalResult = {
        success: true,
        tags: tags,
        usage: result.usage
      };

      // 缓存结果 (非流式模式)
      if (!options.stream && typeof requestCache !== 'undefined') {
        await requestCache.set('generateTags', content, options, finalResult);
      }

      return finalResult;
    } else {
      return {
        success: false,
        error: result.error,
        tags: []
      };
    }
  }

  // 解析部分标签 (用于流式显示)
  _parsePartialTags(content) {
    if (!content || content.length < 2) return [];

    try {
      // 尝试解析当前已有的内容中的标签
      const tags = content
        .split(/[,，、]/)
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0 && tag.length < 20)
        .slice(0, 8);

      return tags;
    } catch (error) {
      return [];
    }
  }



  // 中英对照翻译 (支持流式)
  async translateContent(content, options = {}) {
    // 检测语言类型
    const isChineseContent = this.detectLanguage(content);
    console.log('🔍 语言检测结果', { isChineseContent });

    // 检查缓存（包含语言检测结果）(流式模式不使用缓存)
    const cacheOptions = { ...options, isChineseContent };
    if (!options.stream && typeof requestCache !== 'undefined') {
      const cached = await requestCache.get('translateContent', content, cacheOptions);
      if (cached) {
        console.log('🎯 使用缓存的翻译结果');
        return cached;
      }
    }

    // 如果是流式模式，设置特殊的回调处理
    if (options.stream && options.onChunk) {
      const originalOnChunk = options.onChunk;
      options.onChunk = (chunk, currentContent) => {
        // 对于翻译，我们可以尝试实时解析部分翻译结果
        const partialTranslation = this._parsePartialTranslation(currentContent, content, isChineseContent);
        originalOnChunk(chunk, currentContent, partialTranslation);
      };
    }

    const result = await this._executeAIRequest('translate', content, options);

    if (result.success) {
      // 解析翻译结果
      const parsedResult = this.parseTranslationResult(result.content, content, isChineseContent);

      const finalResult = {
        success: true,
        translation: parsedResult,
        originalLanguage: isChineseContent ? 'zh' : 'en',
        targetLanguage: isChineseContent ? 'en' : 'zh',
        usage: result.usage
      };

      // 缓存结果 (非流式模式)
      if (!options.stream && typeof requestCache !== 'undefined') {
        await requestCache.set('translateContent', content, cacheOptions, finalResult);
      }

      return finalResult;
    } else {
      return {
        success: false,
        error: result.error,
        translation: { original: content, translated: '' }
      };
    }
  }

  // 解析部分翻译结果 (用于流式显示)
  _parsePartialTranslation(currentContent, originalContent, isChineseContent) {
    try {
      // 尝试解析当前已有的翻译内容
      const partialResult = this.parseTranslationResult(currentContent, originalContent, isChineseContent);
      return partialResult;
    } catch (error) {
      // 如果解析失败，返回基本结构
      return {
        original: originalContent,
        translated: currentContent
      };
    }
  }

  // 检测语言类型
  detectLanguage(text) {
    // 简单的中文检测：如果中文字符占比超过30%，认为是中文
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const totalChars = text.length;
    const chineseRatio = chineseChars / totalChars;

    console.log('🔍 语言检测详情', {
      chineseChars,
      totalChars,
      chineseRatio,
      isChinese: chineseRatio > 0.3
    });

    return chineseRatio > 0.3;
  }

  // 解析翻译结果
  parseTranslationResult(aiResponse, originalContent, isChineseOriginal) {
    try {
      // 尝试解析AI返回的格式化结果
      const originalMatch = aiResponse.match(/【原文】\s*\n([\s\S]*?)\n\n【译文】/);
      const translatedMatch = aiResponse.match(/【译文】\s*\n([\s\S]*?)$/);

      if (originalMatch && translatedMatch) {
        return {
          original: originalMatch[1].trim(),
          translated: translatedMatch[1].trim(),
          formatted: true
        };
      }

      // 如果AI没有按格式返回，尝试其他解析方式
      const lines = aiResponse.split('\n').filter(line => line.trim());
      if (lines.length >= 2) {
        return {
          original: originalContent,
          translated: lines[lines.length - 1].trim(),
          formatted: false
        };
      }

      // 最后的备选方案
      return {
        original: originalContent,
        translated: aiResponse.trim(),
        formatted: false
      };
    } catch (error) {
      console.warn('⚠️ 翻译结果解析失败，使用备选方案:', error);
      return {
        original: originalContent,
        translated: aiResponse.trim(),
        formatted: false
      };
    }
  }

  // 批量处理多个AI功能
  async processMultiple(content, functions, options = {}) {
    const results = {};
    const errors = [];

    for (const func of functions) {
      try {
        switch (func) {
          case 'tags':
            results.tags = await this.generateTags(content, options);
            break;
          case 'translate':
            results.translate = await this.translateContent(content, options);
            break;
        }
      } catch (error) {
        errors.push({ function: func, error: error.message });
      }
    }

    return {
      results,
      errors,
      success: errors.length === 0
    };
  }

  // 获取AI配置
  async getAIConfig(options = {}) {
    console.log('🔧 获取AI配置', { options });

    // 从选项中获取配置，或从存储中获取默认配置
    if (options.provider && options.apiKey) {
      const config = {
        provider: options.provider,
        model: options.model || this.serviceManager.providers[options.provider].defaultModel,
        apiKey: options.apiKey
      };
      console.log('✅ 使用传入的AI配置', {
        provider: config.provider,
        model: config.model,
        apiKeyLength: config.apiKey.length
      });
      return config;
    }

    // 从Chrome存储获取配置
    let config;
    try {
      if (chrome && chrome.storage && chrome.storage.sync) {
        config = await chrome.storage.sync.get([
          'aiProvider',
          'aiModel'
        ]);
      } else {
        console.warn('⚠️ Chrome存储API不可用，使用默认配置');
        config = {
          aiProvider: 'siliconflow',
          aiModel: 'Qwen/Qwen2.5-7B-Instruct'
        };
      }
    } catch (error) {
      console.warn('⚠️ 获取Chrome存储配置失败，使用默认配置:', error);
      config = {
        aiProvider: 'siliconflow',
        aiModel: 'Qwen/Qwen2.5-7B-Instruct'
      };
    }

    console.log('📦 从存储获取的配置', {
      aiProvider: config.aiProvider,
      aiModel: config.aiModel
    });

    const provider = config.aiProvider || 'siliconflow';

    // 使用安全存储获取API密钥
    let apiKey;
    if (typeof secureStorage !== 'undefined' && SecureStorage.isSupported()) {
      console.log('🔒 使用安全存储获取API密钥');
      apiKey = await secureStorage.getApiKey(provider);
    } else {
      console.log('⚠️ 降级到传统存储获取API密钥');
      // 降级到传统存储
      try {
        if (chrome && chrome.storage && chrome.storage.sync) {
          const legacyConfig = await chrome.storage.sync.get('aiApiKeys');
          const apiKeys = legacyConfig.aiApiKeys || {};
          apiKey = apiKeys[provider];
        } else {
          // 测试环境下使用模拟API密钥
          console.warn('⚠️ Chrome存储API不可用，使用测试API密钥');
          apiKey = 'sk-bpbqulqtsmbywglsemzqantxqhmilksogyeitgcpkbvwioix';
        }
      } catch (error) {
        console.warn('⚠️ 获取API密钥失败，使用测试API密钥:', error);
        apiKey = 'sk-bpbqulqtsmbywglsemzqantxqhmilksogyeitgcpkbvwioix';
      }
    }

    console.log('🔍 解析后的配置', {
      provider,
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey ? apiKey.length : 0,
      availableProviders: Object.keys(this.serviceManager.providers)
    });

    if (!apiKey) {
      const errorMsg = `请先配置 ${this.serviceManager.providers[provider]?.name || provider} 的API密钥`;
      console.error('❌ API密钥未配置:', errorMsg);
      throw new Error(errorMsg);
    }

    // 详细的模型配置调试
    const providerConfig = this.serviceManager.providers[provider];
    const storageModel = config.aiModel;
    const defaultModel = providerConfig?.defaultModel;
    const finalModel = storageModel || defaultModel;

    console.log('🔍 模型配置详细信息', {
      provider,
      storageModel,
      defaultModel,
      finalModel,
      hasProviderConfig: !!providerConfig,
      providerConfigKeys: providerConfig ? Object.keys(providerConfig) : []
    });

    // 如果最终模型为空，抛出详细错误
    if (!finalModel) {
      const errorMsg = `模型配置失败: provider=${provider}, storageModel=${storageModel}, defaultModel=${defaultModel}`;
      console.error('❌ 模型配置为空:', errorMsg);
      throw new Error(errorMsg);
    }

    const finalConfig = {
      provider,
      model: finalModel,
      apiKey
    };

    console.log('✅ 最终AI配置', {
      provider: finalConfig.provider,
      model: finalConfig.model,
      apiKeyLength: finalConfig.apiKey.length
    });

    return finalConfig;
  }

  // 获取功能描述
  getFunctionDescriptions() {
    return {
      tags: {
        name: '智能标签',
        description: '根据内容自动生成相关标签',
        icon: '🏷️',
        estimatedTime: '3-5秒'
      },
      translate: {
        name: '中英对照',
        description: '智能翻译并提供中英文对照',
        icon: '🌐',
        estimatedTime: '5-12秒'
      }
    };
  }
}

// 导出AI功能实例
let aiFunctions;
try {
  aiFunctions = new AIFunctions(aiServiceManager);
  console.log('✅ aiFunctions 实例已创建');
} catch (error) {
  console.error('❌ 创建 aiFunctions 实例失败:', error);
}

// 导出到全局作用域
if (typeof window !== 'undefined') {
  window.aiFunctions = aiFunctions;
  window.AIFunctions = AIFunctions;
} else if (typeof self !== 'undefined') {
  // Service Worker环境
  self.aiFunctions = aiFunctions;
  self.AIFunctions = AIFunctions;
}
