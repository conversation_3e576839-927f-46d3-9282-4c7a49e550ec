// 轻量级测试框架
class TestFramework {
  constructor() {
    this.tests = [];
    this.suites = new Map();
    this.results = {
      passed: 0,
      failed: 0,
      skipped: 0,
      total: 0
    };
    this.currentSuite = null;
    this.beforeEachHooks = [];
    this.afterEachHooks = [];
    this.beforeAllHooks = [];
    this.afterAllHooks = [];
  }

  // 创建测试套件
  describe(name, fn) {
    const previousSuite = this.currentSuite;
    this.currentSuite = {
      name,
      tests: [],
      beforeEach: [],
      afterEach: [],
      beforeAll: [],
      afterAll: []
    };
    
    this.suites.set(name, this.currentSuite);
    
    try {
      fn();
    } finally {
      this.currentSuite = previousSuite;
    }
  }

  // 定义测试用例
  it(description, testFn) {
    const test = {
      description,
      fn: testFn,
      suite: this.currentSuite?.name || 'Global',
      status: 'pending',
      error: null,
      duration: 0
    };

    if (this.currentSuite) {
      this.currentSuite.tests.push(test);
    } else {
      this.tests.push(test);
    }
  }

  // 跳过测试
  xit(description, testFn) {
    this.it(description, () => {
      throw new Error('SKIPPED');
    });
  }

  // 设置钩子函数
  beforeEach(fn) {
    if (this.currentSuite) {
      this.currentSuite.beforeEach.push(fn);
    } else {
      this.beforeEachHooks.push(fn);
    }
  }

  afterEach(fn) {
    if (this.currentSuite) {
      this.currentSuite.afterEach.push(fn);
    } else {
      this.afterEachHooks.push(fn);
    }
  }

  beforeAll(fn) {
    if (this.currentSuite) {
      this.currentSuite.beforeAll.push(fn);
    } else {
      this.beforeAllHooks.push(fn);
    }
  }

  afterAll(fn) {
    if (this.currentSuite) {
      this.currentSuite.afterAll.push(fn);
    } else {
      this.afterAllHooks.push(fn);
    }
  }

  // 断言函数
  expect(actual) {
    return {
      toBe: (expected) => {
        if (actual !== expected) {
          throw new Error(`Expected ${actual} to be ${expected}`);
        }
      },
      
      toEqual: (expected) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`Expected ${JSON.stringify(actual)} to equal ${JSON.stringify(expected)}`);
        }
      },
      
      toBeTruthy: () => {
        if (!actual) {
          throw new Error(`Expected ${actual} to be truthy`);
        }
      },
      
      toBeFalsy: () => {
        if (actual) {
          throw new Error(`Expected ${actual} to be falsy`);
        }
      },
      
      toBeNull: () => {
        if (actual !== null) {
          throw new Error(`Expected ${actual} to be null`);
        }
      },
      
      toBeUndefined: () => {
        if (actual !== undefined) {
          throw new Error(`Expected ${actual} to be undefined`);
        }
      },
      
      toContain: (expected) => {
        if (Array.isArray(actual)) {
          if (!actual.includes(expected)) {
            throw new Error(`Expected array ${JSON.stringify(actual)} to contain ${expected}`);
          }
        } else if (typeof actual === 'string') {
          if (!actual.includes(expected)) {
            throw new Error(`Expected string "${actual}" to contain "${expected}"`);
          }
        } else {
          throw new Error(`toContain can only be used with arrays or strings`);
        }
      },
      
      toThrow: (expectedError) => {
        if (typeof actual !== 'function') {
          throw new Error('toThrow can only be used with functions');
        }
        
        try {
          actual();
          throw new Error('Expected function to throw an error');
        } catch (error) {
          if (expectedError && !error.message.includes(expectedError)) {
            throw new Error(`Expected error to contain "${expectedError}", but got "${error.message}"`);
          }
        }
      },
      
      toHaveProperty: (property) => {
        if (typeof actual !== 'object' || actual === null) {
          throw new Error(`Expected ${actual} to be an object`);
        }
        if (!(property in actual)) {
          throw new Error(`Expected object to have property "${property}"`);
        }
      },
      
      toHaveLength: (length) => {
        if (!actual || typeof actual.length !== 'number') {
          throw new Error(`Expected ${actual} to have a length property`);
        }
        if (actual.length !== length) {
          throw new Error(`Expected length to be ${length}, but got ${actual.length}`);
        }
      }
    };
  }

  // 运行所有测试
  async run() {
    console.log('🧪 开始运行测试...\n');
    
    this.results = { passed: 0, failed: 0, skipped: 0, total: 0 };
    
    // 运行全局beforeAll钩子
    for (const hook of this.beforeAllHooks) {
      await this.runHook(hook, 'beforeAll');
    }
    
    // 运行全局测试
    for (const test of this.tests) {
      await this.runTest(test);
    }
    
    // 运行测试套件
    for (const [suiteName, suite] of this.suites) {
      console.log(`\n📦 测试套件: ${suiteName}`);
      
      // 运行套件的beforeAll钩子
      for (const hook of suite.beforeAll) {
        await this.runHook(hook, 'beforeAll');
      }
      
      // 运行套件中的测试
      for (const test of suite.tests) {
        // 运行beforeEach钩子
        for (const hook of [...this.beforeEachHooks, ...suite.beforeEach]) {
          await this.runHook(hook, 'beforeEach');
        }
        
        await this.runTest(test);
        
        // 运行afterEach钩子
        for (const hook of [...this.afterEachHooks, ...suite.afterEach]) {
          await this.runHook(hook, 'afterEach');
        }
      }
      
      // 运行套件的afterAll钩子
      for (const hook of suite.afterAll) {
        await this.runHook(hook, 'afterAll');
      }
    }
    
    // 运行全局afterAll钩子
    for (const hook of this.afterAllHooks) {
      await this.runHook(hook, 'afterAll');
    }
    
    this.printResults();
    return this.results;
  }

  // 运行单个测试
  async runTest(test) {
    const startTime = Date.now();
    this.results.total++;
    
    try {
      if (typeof test.fn === 'function') {
        await test.fn();
      }
      
      test.status = 'passed';
      test.duration = Date.now() - startTime;
      this.results.passed++;
      
      console.log(`  ✅ ${test.description} (${test.duration}ms)`);
    } catch (error) {
      test.status = error.message === 'SKIPPED' ? 'skipped' : 'failed';
      test.error = error;
      test.duration = Date.now() - startTime;
      
      if (test.status === 'skipped') {
        this.results.skipped++;
        console.log(`  ⏭️  ${test.description} (skipped)`);
      } else {
        this.results.failed++;
        console.log(`  ❌ ${test.description} (${test.duration}ms)`);
        console.log(`     Error: ${error.message}`);
        if (error.stack) {
          console.log(`     Stack: ${error.stack.split('\n')[1]?.trim()}`);
        }
      }
    }
  }

  // 运行钩子函数
  async runHook(hook, type) {
    try {
      await hook();
    } catch (error) {
      console.log(`  ⚠️  ${type} hook failed: ${error.message}`);
    }
  }

  // 打印测试结果
  printResults() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试结果汇总');
    console.log('='.repeat(50));
    console.log(`总计: ${this.results.total}`);
    console.log(`✅ 通过: ${this.results.passed}`);
    console.log(`❌ 失败: ${this.results.failed}`);
    console.log(`⏭️  跳过: ${this.results.skipped}`);
    
    const passRate = this.results.total > 0 
      ? ((this.results.passed / this.results.total) * 100).toFixed(1)
      : 0;
    console.log(`📈 通过率: ${passRate}%`);
    
    if (this.results.failed === 0) {
      console.log('\n🎉 所有测试通过！');
    } else {
      console.log(`\n⚠️  有 ${this.results.failed} 个测试失败`);
    }
  }

  // 创建模拟对象
  createMock(obj = {}) {
    const mock = { ...obj };
    mock._calls = [];
    
    // 为每个方法创建spy
    for (const key in mock) {
      if (typeof mock[key] === 'function') {
        const originalFn = mock[key];
        mock[key] = (...args) => {
          mock._calls.push({ method: key, args });
          return originalFn.apply(mock, args);
        };
        mock[key].mockReturnValue = (value) => {
          mock[key] = () => {
            mock._calls.push({ method: key, args: [] });
            return value;
          };
        };
        mock[key].mockResolvedValue = (value) => {
          mock[key] = () => {
            mock._calls.push({ method: key, args: [] });
            return Promise.resolve(value);
          };
        };
        mock[key].mockRejectedValue = (error) => {
          mock[key] = () => {
            mock._calls.push({ method: key, args: [] });
            return Promise.reject(error);
          };
        };
      }
    }
    
    return mock;
  }

  // 创建spy函数
  createSpy(fn) {
    const spy = (...args) => {
      spy._calls.push(args);
      if (fn) return fn(...args);
    };
    spy._calls = [];
    spy.mockReturnValue = (value) => {
      spy._returnValue = value;
      return spy;
    };
    return spy;
  }
}

// 创建全局测试实例
const testFramework = new TestFramework();

// 导出全局函数
const describe = testFramework.describe.bind(testFramework);
const it = testFramework.it.bind(testFramework);
const xit = testFramework.xit.bind(testFramework);
const beforeEach = testFramework.beforeEach.bind(testFramework);
const afterEach = testFramework.afterEach.bind(testFramework);
const beforeAll = testFramework.beforeAll.bind(testFramework);
const afterAll = testFramework.afterAll.bind(testFramework);
const expect = testFramework.expect.bind(testFramework);

// 导出到全局作用域
if (typeof window !== 'undefined') {
  window.testFramework = testFramework;
  window.describe = describe;
  window.it = it;
  window.xit = xit;
  window.beforeEach = beforeEach;
  window.afterEach = afterEach;
  window.beforeAll = beforeAll;
  window.afterAll = afterAll;
  window.expect = expect;
}
