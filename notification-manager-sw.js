// Service Worker专用通知管理系统
class ServiceWorkerNotificationManager {
  constructor(config) {
    this.config = config || {
      showSuccess: true,
      showRetry: true,
      showError: true,
      autoHideDelay: 5000,
      maxVisible: 3
    };
    
    this.activeNotifications = new Map();
    this.notificationQueue = [];
  }

  // 生成唯一ID
  generateId() {
    return 'notification_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // 显示Chrome通知（Service Worker专用）
  showChromeNotification(type, title, message) {
    const iconMap = {
      success: 'icon.png',
      error: 'icon.png',
      warning: 'icon.png',
      info: 'icon.png'
    };

    const titles = {
      success: '✅ 成功',
      error: '❌ 错误',
      warning: '⚠️ 警告',
      info: 'ℹ️ 信息'
    };

    const notificationOptions = {
      type: 'basic',
      iconUrl: iconMap[type] || iconMap.info,
      title: titles[type] || titles.info,
      message: message
    };

    return new Promise((resolve) => {
      const notificationId = `flomo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      chrome.notifications.create(notificationId, notificationOptions, (id) => {
        if (chrome.runtime.lastError) {
          console.error('Chrome通知创建失败:', chrome.runtime.lastError);
          resolve(false);
        } else {
          console.log(`Chrome通知已显示: ${title} - ${message}`);
          resolve(true);
        }
      });
    });
  }

  // 显示成功通知
  success(title, message) {
    return this.showChromeNotification('success', title, message);
  }

  // 显示错误通知
  error(title, message) {
    return this.showChromeNotification('error', title, message);
  }

  // 显示警告通知
  warning(title, message) {
    return this.showChromeNotification('warning', title, message);
  }

  // 显示信息通知
  info(title, message) {
    return this.showChromeNotification('info', title, message);
  }

  // 显示成功Chrome通知（兼容原API）
  showSuccessChrome(message) {
    return this.showChromeNotification('success', '保存成功', message);
  }

  // 显示错误Chrome通知（兼容原API）
  showErrorChrome(message) {
    return this.showChromeNotification('error', '操作失败', message);
  }

  // 显示警告Chrome通知（兼容原API）
  showWarningChrome(message) {
    return this.showChromeNotification('warning', '注意', message);
  }

  // 显示信息Chrome通知（兼容原API）
  showInfoChrome(message) {
    return this.showChromeNotification('info', '提示', message);
  }

  // 清理所有通知（Service Worker中主要是清理内存）
  clearAll() {
    this.activeNotifications.clear();
    this.notificationQueue = [];
  }

  // 移除特定通知
  remove(id) {
    if (this.activeNotifications.has(id)) {
      this.activeNotifications.delete(id);
      // 尝试清除Chrome通知
      chrome.notifications.clear(id, () => {
        if (chrome.runtime.lastError) {
          console.warn('清除Chrome通知失败:', chrome.runtime.lastError);
        }
      });
    }
  }

  // 获取活跃通知数量
  getActiveCount() {
    return this.activeNotifications.size;
  }

  // 检查是否支持Chrome通知
  isSupported() {
    return typeof chrome !== 'undefined' && 
           chrome.notifications && 
           typeof chrome.notifications.create === 'function';
  }
}

// 创建Service Worker通知管理器实例
const notificationManager = new ServiceWorkerNotificationManager();

// 导出到全局作用域
if (typeof self !== 'undefined') {
  self.notificationManager = notificationManager;
}
