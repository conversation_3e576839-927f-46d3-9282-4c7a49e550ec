// 安全存储测试
describe('SecureStorage', () => {
  let secureStorage;
  let mockChrome;
  let mockIndexedDB;

  beforeEach(() => {
    // 模拟Chrome存储API
    mockChrome = {
      storage: {
        local: {
          set: jest.fn().mockResolvedValue(),
          get: jest.fn().mockResolvedValue({}),
          remove: jest.fn().mockResolvedValue()
        }
      }
    };
    global.chrome = mockChrome;

    // 模拟IndexedDB
    mockIndexedDB = {
      open: jest.fn().mockImplementation(() => {
        const request = {
          onsuccess: null,
          onerror: null,
          onupgradeneeded: null,
          result: {
            transaction: jest.fn().mockReturnValue({
              objectStore: jest.fn().mockReturnValue({
                put: jest.fn(),
                get: jest.fn().mockReturnValue({ onsuccess: null, result: null })
              }),
              oncomplete: null,
              onerror: null
            }),
            close: jest.fn()
          }
        };
        setTimeout(() => request.onsuccess && request.onsuccess({ target: request }), 0);
        return request;
      })
    };
    global.indexedDB = mockIndexedDB;

    // 模拟Web Crypto API
    global.crypto = {
      subtle: {
        generateKey: jest.fn().mockResolvedValue('mock-key'),
        encrypt: jest.fn().mockResolvedValue(new ArrayBuffer(16)),
        decrypt: jest.fn().mockResolvedValue(new ArrayBuffer(16))
      },
      getRandomValues: jest.fn().mockReturnValue(new Uint8Array(12))
    };

    // 模拟TextEncoder/TextDecoder
    global.TextEncoder = jest.fn().mockImplementation(() => ({
      encode: jest.fn().mockReturnValue(new Uint8Array([1, 2, 3]))
    }));
    global.TextDecoder = jest.fn().mockImplementation(() => ({
      decode: jest.fn().mockReturnValue('{"test": "data"}')
    }));

    // 模拟btoa/atob
    global.btoa = jest.fn().mockReturnValue('encoded-data');
    global.atob = jest.fn().mockReturnValue('decoded-data');

    secureStorage = new SecureStorage();
  });

  afterEach(() => {
    delete global.chrome;
    delete global.indexedDB;
    delete global.crypto;
    delete global.TextEncoder;
    delete global.TextDecoder;
    delete global.btoa;
    delete global.atob;
  });

  describe('isSupported', () => {
    it('应该检测Web Crypto API支持', () => {
      expect(SecureStorage.isSupported()).toBe(true);
    });

    it('应该检测不支持的环境', () => {
      delete global.crypto;
      expect(SecureStorage.isSupported()).toBe(false);
    });
  });

  describe('generateMasterKey', () => {
    it('应该生成主密钥', async () => {
      const key = await secureStorage.generateMasterKey();
      expect(key).toBe('mock-key');
      expect(crypto.subtle.generateKey).toHaveBeenCalledWith(
        { name: 'AES-GCM', length: 256 },
        false,
        ['encrypt', 'decrypt']
      );
    });

    it('应该处理密钥生成失败', async () => {
      crypto.subtle.generateKey.mockRejectedValue(new Error('生成失败'));
      
      await expect(secureStorage.generateMasterKey()).rejects.toThrow('无法生成加密密钥');
    });
  });

  describe('encryptData', () => {
    it('应该加密数据', async () => {
      const testData = { test: 'data' };
      const result = await secureStorage.encryptData(testData);
      
      expect(result).toBe('encoded-data');
      expect(crypto.subtle.encrypt).toHaveBeenCalled();
      expect(btoa).toHaveBeenCalled();
    });

    it('应该处理加密失败', async () => {
      crypto.subtle.encrypt.mockRejectedValue(new Error('加密失败'));
      
      await expect(secureStorage.encryptData({ test: 'data' })).rejects.toThrow('数据加密失败');
    });
  });

  describe('decryptData', () => {
    it('应该解密数据', async () => {
      const encryptedData = 'encrypted-data';
      const result = await secureStorage.decryptData(encryptedData);
      
      expect(result).toEqual({ test: 'data' });
      expect(atob).toHaveBeenCalledWith(encryptedData);
      expect(crypto.subtle.decrypt).toHaveBeenCalled();
    });

    it('应该处理解密失败', async () => {
      crypto.subtle.decrypt.mockRejectedValue(new Error('解密失败'));
      
      await expect(secureStorage.decryptData('invalid-data')).rejects.toThrow('数据解密失败');
    });
  });

  describe('setApiKey', () => {
    it('应该安全存储API密钥', async () => {
      const provider = 'siliconflow';
      const apiKey = 'sk-test123456789';
      
      const result = await secureStorage.setApiKey(provider, apiKey);
      
      expect(result).toBe(true);
      expect(mockChrome.storage.local.set).toHaveBeenCalled();
    });

    it('应该验证输入参数', async () => {
      await expect(secureStorage.setApiKey('', 'key')).rejects.toThrow('提供商和API密钥不能为空');
      await expect(secureStorage.setApiKey('provider', '')).rejects.toThrow('提供商和API密钥不能为空');
    });

    it('应该缓存API密钥到内存', async () => {
      const provider = 'siliconflow';
      const apiKey = 'sk-test123456789';
      
      await secureStorage.setApiKey(provider, apiKey);
      
      expect(secureStorage.keyCache.get(provider)).toBe(apiKey);
    });
  });

  describe('getApiKey', () => {
    it('应该从内存缓存获取API密钥', async () => {
      const provider = 'siliconflow';
      const apiKey = 'sk-test123456789';
      
      secureStorage.keyCache.set(provider, apiKey);
      
      const result = await secureStorage.getApiKey(provider);
      expect(result).toBe(apiKey);
    });

    it('应该从存储获取并解密API密钥', async () => {
      const provider = 'siliconflow';
      const encryptedData = 'encrypted-api-key';
      
      mockChrome.storage.local.get.mockResolvedValue({
        [`flomo_secure_${provider}`]: encryptedData
      });
      
      const result = await secureStorage.getApiKey(provider);
      expect(result).toEqual({ test: 'data' }); // 模拟解密结果
    });

    it('应该处理不存在的API密钥', async () => {
      const provider = 'nonexistent';
      
      const result = await secureStorage.getApiKey(provider);
      expect(result).toBeNull();
    });

    it('应该处理解密失败并清理损坏的数据', async () => {
      const provider = 'siliconflow';
      
      mockChrome.storage.local.get.mockResolvedValue({
        [`flomo_secure_${provider}`]: 'corrupted-data'
      });
      
      crypto.subtle.decrypt.mockRejectedValue(new Error('解密失败'));
      
      const result = await secureStorage.getApiKey(provider);
      expect(result).toBeNull();
      expect(mockChrome.storage.local.remove).toHaveBeenCalled();
    });
  });

  describe('removeApiKey', () => {
    it('应该删除API密钥', async () => {
      const provider = 'siliconflow';
      
      secureStorage.keyCache.set(provider, 'test-key');
      
      const result = await secureStorage.removeApiKey(provider);
      
      expect(result).toBe(true);
      expect(mockChrome.storage.local.remove).toHaveBeenCalledWith(`flomo_secure_${provider}`);
      expect(secureStorage.keyCache.has(provider)).toBe(false);
    });
  });

  describe('getStoredProviders', () => {
    it('应该获取所有已存储的提供商', async () => {
      mockChrome.storage.local.get.mockResolvedValue({
        'flomo_secure_siliconflow': 'data1',
        'flomo_secure_openrouter': 'data2',
        'other_key': 'data3'
      });
      
      const providers = await secureStorage.getStoredProviders();
      
      expect(providers).toEqual(['siliconflow', 'openrouter']);
    });
  });

  describe('clearAll', () => {
    it('应该清理所有加密存储', async () => {
      mockChrome.storage.local.get.mockResolvedValue({
        'flomo_secure_siliconflow': 'data1',
        'flomo_secure_openrouter': 'data2'
      });
      
      secureStorage.keyCache.set('siliconflow', 'key1');
      secureStorage.keyCache.set('openrouter', 'key2');
      
      const result = await secureStorage.clearAll();
      
      expect(result).toBe(true);
      expect(mockChrome.storage.local.remove).toHaveBeenCalledTimes(2);
      expect(secureStorage.keyCache.size).toBe(0);
    });
  });

  describe('migrateFromPlainStorage', () => {
    it('应该迁移明文存储到加密存储', async () => {
      const oldData = {
        aiApiKeys: {
          siliconflow: 'sk-old-key-1',
          openrouter: 'sk-old-key-2'
        }
      };
      
      mockChrome.storage.sync = {
        get: jest.fn().mockResolvedValue(oldData),
        remove: jest.fn().mockResolvedValue()
      };
      
      const result = await secureStorage.migrateFromPlainStorage();
      
      expect(result).toBe(true);
      expect(mockChrome.storage.local.set).toHaveBeenCalledTimes(2);
      expect(mockChrome.storage.sync.remove).toHaveBeenCalledWith('aiApiKeys');
    });

    it('应该处理没有旧数据的情况', async () => {
      mockChrome.storage.sync = {
        get: jest.fn().mockResolvedValue({})
      };
      
      const result = await secureStorage.migrateFromPlainStorage();
      
      expect(result).toBe(true);
      expect(mockChrome.storage.local.set).not.toHaveBeenCalled();
    });

    it('应该处理迁移失败', async () => {
      mockChrome.storage.sync = {
        get: jest.fn().mockRejectedValue(new Error('获取失败'))
      };
      
      const result = await secureStorage.migrateFromPlainStorage();
      
      expect(result).toBe(false);
    });
  });
});
