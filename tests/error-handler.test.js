// 错误处理器单元测试
describe('ErrorHandler', () => {
  let errorHandler;
  let mockConfig;

  beforeEach(() => {
    // 创建测试配置
    mockConfig = {
      retry: {
        maxAttempts: 3,
        baseDelay: 100, // 测试时使用较短延迟
        maxDelay: 1000,
        backoffMultiplier: 2,
        jitter: false // 测试时禁用抖动以保证一致性
      },
      errorCategories: {
        nonRetryable: ['api key', 'unauthorized', '401'],
        authentication: ['api key', 'unauthorized'],
        network: ['network', 'fetch'],
        timeout: ['timeout', 'abort'],
        rateLimit: ['429', 'rate limit'],
        serverError: ['500', '502', '503']
      },
      friendlyMessages: {
        authentication_or_config: {
          title: '配置错误',
          message: '请检查您的API配置',
          suggestions: ['检查API密钥']
        },
        network: {
          title: '网络错误',
          message: '网络连接失败',
          suggestions: ['检查网络连接']
        }
      },
      logging: {
        enabled: false // 测试时禁用日志
      }
    };

    errorHandler = new ErrorHandler(mockConfig);
  });

  afterEach(() => {
    errorHandler.clearErrorHistory();
  });

  describe('错误分析', () => {
    it('应该正确识别不可重试的认证错误', () => {
      const error = new Error('API key invalid');
      const analysis = errorHandler.analyzeNetworkError(error);
      
      expect(analysis.retryable).toBe(false);
      expect(analysis.category).toBe('authentication_or_config');
    });

    it('应该正确识别可重试的网络错误', () => {
      const error = new Error('Failed to fetch');
      const analysis = errorHandler.analyzeNetworkError(error);
      
      expect(analysis.retryable).toBe(true);
      expect(analysis.category).toBe('network');
    });

    it('应该正确识别超时错误', () => {
      const error = new Error('Request timeout');
      const analysis = errorHandler.analyzeNetworkError(error);
      
      expect(analysis.retryable).toBe(true);
      expect(analysis.category).toBe('timeout');
    });

    it('应该正确识别服务器错误', () => {
      const error = new Error('HTTP 500: Internal Server Error');
      const analysis = errorHandler.analyzeNetworkError(error);
      
      expect(analysis.retryable).toBe(true);
      expect(analysis.category).toBe('server_error');
    });

    it('应该正确识别频率限制错误', () => {
      const error = new Error('HTTP 429: Too Many Requests');
      const analysis = errorHandler.analyzeNetworkError(error);
      
      expect(analysis.retryable).toBe(true);
      expect(analysis.category).toBe('rate_limit');
    });
  });

  describe('友好错误消息', () => {
    it('应该生成友好的错误消息', () => {
      const error = new Error('API key invalid');
      const analysis = { category: 'authentication_or_config' };
      const friendlyMessage = errorHandler.getFriendlyErrorMessage(error, analysis, 0);
      
      expect(friendlyMessage.title).toBe('配置错误');
      expect(friendlyMessage.message).toBe('请检查您的API配置');
      expect(friendlyMessage.suggestions).toContain('检查API密钥');
      expect(friendlyMessage.originalError).toBe('API key invalid');
    });

    it('应该在重试后显示重试次数', () => {
      const error = new Error('Network error');
      const analysis = { category: 'network' };
      const friendlyMessage = errorHandler.getFriendlyErrorMessage(error, analysis, 2);
      
      expect(friendlyMessage.message).toContain('（已重试 2 次）');
    });
  });

  describe('重试机制', () => {
    it('应该对可重试错误进行重试', async () => {
      let attempts = 0;
      const mockOperation = () => {
        attempts++;
        if (attempts < 3) {
          throw new Error('HTTP 500: Server Error');
        }
        return 'success';
      };

      const result = await errorHandler.handleNetworkError(
        null,
        'testOperation',
        mockOperation
      );

      expect(result).toBe('success');
      expect(attempts).toBe(3);
    });

    it('应该在达到最大重试次数后抛出错误', async () => {
      const mockOperation = () => {
        throw new Error('HTTP 500: Server Error');
      };

      try {
        await errorHandler.handleNetworkError(
          null,
          'testOperation',
          mockOperation
        );
        expect(true).toBe(false); // 不应该到达这里
      } catch (error) {
        expect(error.message).toContain('网络连接失败');
      }
    });

    it('应该不重试不可重试的错误', async () => {
      let attempts = 0;
      const mockOperation = () => {
        attempts++;
        throw new Error('API key invalid');
      };

      try {
        await errorHandler.handleNetworkError(
          null,
          'testOperation',
          mockOperation
        );
        expect(true).toBe(false); // 不应该到达这里
      } catch (error) {
        expect(attempts).toBe(1);
        expect(error.message).toContain('请检查您的API配置');
      }
    });

    it('应该计算正确的延迟时间', () => {
      // 测试指数退避算法
      const baseDelay = 100;
      const multiplier = 2;
      
      // 模拟计算延迟时间的逻辑
      const calculateDelay = (attempt) => {
        return Math.min(
          baseDelay * Math.pow(multiplier, attempt),
          mockConfig.retry.maxDelay
        );
      };

      expect(calculateDelay(0)).toBe(100);
      expect(calculateDelay(1)).toBe(200);
      expect(calculateDelay(2)).toBe(400);
    });
  });

  describe('错误历史记录', () => {
    it('应该记录错误信息', () => {
      const error = new Error('Test error');
      errorHandler.logError('Test', error, { context: 'test' });

      const stats = errorHandler.getErrorStats();
      expect(stats.total).toBe(1);
    });

    it('应该限制错误历史记录大小', () => {
      // 添加超过限制的错误记录
      for (let i = 0; i < 150; i++) {
        errorHandler.logError('Test', new Error(`Error ${i}`));
      }

      const stats = errorHandler.getErrorStats();
      expect(stats.total).toBeLessThanOrEqual(100);
    });

    it('应该正确统计最近的错误', () => {
      // 添加一个旧错误（模拟）
      const oldError = {
        type: 'Old Error',
        message: 'Old error message',
        timestamp: Date.now() - 2 * 60 * 60 * 1000 // 2小时前
      };
      errorHandler.errorHistory.push(oldError);

      // 添加一个新错误
      errorHandler.logError('Recent', new Error('Recent error'));

      const stats = errorHandler.getErrorStats();
      expect(stats.recent).toBe(1); // 只有最近1小时的错误
    });

    it('应该能够清空错误历史', () => {
      errorHandler.logError('Test', new Error('Test error'));
      expect(errorHandler.getErrorStats().total).toBe(1);

      errorHandler.clearErrorHistory();
      expect(errorHandler.getErrorStats().total).toBe(0);
    });
  });

  describe('配置验证', () => {
    it('应该检测缺失的Flomo API配置', async () => {
      // 模拟chrome.storage.sync.get
      const originalChrome = global.chrome;
      global.chrome = {
        storage: {
          sync: {
            get: () => Promise.resolve({}) // 返回空配置
          }
        }
      };

      const issues = await errorHandler.validateConfiguration();
      
      expect(issues).toHaveLength(1);
      expect(issues[0].type).toBe('missing_flomo_api');
      expect(issues[0].severity).toBe('error');

      global.chrome = originalChrome;
    });

    it('应该检测无效的URL格式', async () => {
      // 模拟chrome.storage.sync.get
      const originalChrome = global.chrome;
      global.chrome = {
        storage: {
          sync: {
            get: () => Promise.resolve({
              flomoApiUrl: 'invalid-url'
            })
          }
        }
      };

      const issues = await errorHandler.validateConfiguration();
      
      const urlIssue = issues.find(issue => issue.type === 'invalid_flomo_api');
      expect(urlIssue).toBeTruthy();
      expect(urlIssue.severity).toBe('error');

      global.chrome = originalChrome;
    });

    it('应该检测非HTTPS协议', async () => {
      // 模拟chrome.storage.sync.get
      const originalChrome = global.chrome;
      global.chrome = {
        storage: {
          sync: {
            get: () => Promise.resolve({
              flomoApiUrl: 'http://example.com/api'
            })
          }
        }
      };

      const issues = await errorHandler.validateConfiguration();
      
      const httpsIssue = issues.find(issue => issue.type === 'insecure_flomo_api');
      expect(httpsIssue).toBeTruthy();
      expect(httpsIssue.severity).toBe('warning');

      global.chrome = originalChrome;
    });
  });

  describe('边界情况', () => {
    it('应该处理空错误消息', () => {
      const error = new Error('');
      const analysis = errorHandler.analyzeNetworkError(error);
      
      expect(analysis.retryable).toBe(true);
      expect(analysis.category).toBe('unknown');
    });

    it('应该处理null错误', () => {
      const analysis = errorHandler.analyzeNetworkError(null);
      
      expect(analysis.retryable).toBe(true);
      expect(analysis.category).toBe('unknown');
    });

    it('应该处理undefined错误', () => {
      const analysis = errorHandler.analyzeNetworkError(undefined);
      
      expect(analysis.retryable).toBe(true);
      expect(analysis.category).toBe('unknown');
    });

    it('应该处理没有message属性的错误对象', () => {
      const error = { name: 'CustomError' };
      const analysis = errorHandler.analyzeNetworkError(error);
      
      expect(analysis.retryable).toBe(true);
      expect(analysis.category).toBe('unknown');
    });
  });
});
