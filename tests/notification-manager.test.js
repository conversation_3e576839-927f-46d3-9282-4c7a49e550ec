// 通知管理器单元测试
describe('NotificationManager', () => {
  let notificationManager;
  let mockConfig;

  beforeEach(() => {
    // 清理DOM
    const existingContainer = document.getElementById('notification-container');
    if (existingContainer) {
      existingContainer.remove();
    }

    mockConfig = {
      showSuccess: true,
      showRetry: true,
      showError: true,
      autoHideDelay: 1000, // 测试时使用较短延迟
      maxVisible: 2
    };

    notificationManager = new NotificationManager(mockConfig);
  });

  afterEach(() => {
    notificationManager.clearAll();
  });

  describe('初始化', () => {
    it('应该创建通知容器', () => {
      const container = document.getElementById('notification-container');
      expect(container).toBeTruthy();
    });

    it('应该使用默认配置', () => {
      const defaultManager = new NotificationManager();
      expect(defaultManager.config.maxVisible).toBe(3);
      expect(defaultManager.config.autoHideDelay).toBe(5000);
    });
  });

  describe('通知显示', () => {
    it('应该显示成功通知', () => {
      const id = notificationManager.success('成功', '操作成功完成');
      
      expect(id).toBeTruthy();
      expect(notificationManager.activeNotifications.has(id)).toBe(true);
      
      const notification = document.querySelector('.notification.success');
      expect(notification).toBeTruthy();
      expect(notification.textContent).toContain('成功');
      expect(notification.textContent).toContain('操作成功完成');
    });

    it('应该显示错误通知', () => {
      const id = notificationManager.error('错误', '操作失败');
      
      expect(id).toBeTruthy();
      const notification = document.querySelector('.notification.error');
      expect(notification).toBeTruthy();
      expect(notification.textContent).toContain('错误');
      expect(notification.textContent).toContain('操作失败');
    });

    it('应该显示警告通知', () => {
      const id = notificationManager.warning('警告', '请注意');
      
      expect(id).toBeTruthy();
      const notification = document.querySelector('.notification.warning');
      expect(notification).toBeTruthy();
    });

    it('应该显示信息通知', () => {
      const id = notificationManager.info('信息', '提示信息');
      
      expect(id).toBeTruthy();
      const notification = document.querySelector('.notification.info');
      expect(notification).toBeTruthy();
    });
  });

  describe('通知配置', () => {
    it('应该根据配置决定是否显示通知', () => {
      notificationManager.config.showSuccess = false;
      
      const id = notificationManager.success('成功', '不应该显示');
      expect(id).toBeNull();
      
      const notification = document.querySelector('.notification.success');
      expect(notification).toBeFalsy();
    });

    it('应该限制最大显示数量', () => {
      // 显示超过最大数量的通知
      notificationManager.show({ type: 'info', title: '通知1', message: '消息1' });
      notificationManager.show({ type: 'info', title: '通知2', message: '消息2' });
      notificationManager.show({ type: 'info', title: '通知3', message: '消息3' });
      
      // 应该只显示maxVisible数量的通知
      const visibleNotifications = document.querySelectorAll('.notification');
      expect(visibleNotifications.length).toBe(mockConfig.maxVisible);
      
      // 第三个通知应该在队列中
      expect(notificationManager.notificationQueue.length).toBe(1);
    });
  });

  describe('通知操作', () => {
    it('应该支持操作按钮', () => {
      let actionCalled = false;
      const actions = [{
        id: 'test-action',
        label: '测试操作',
        handler: () => { actionCalled = true; }
      }];

      notificationManager.show({
        type: 'info',
        title: '测试',
        message: '测试消息',
        actions
      });

      const actionButton = document.querySelector('[data-action="test-action"]');
      expect(actionButton).toBeTruthy();
      expect(actionButton.textContent).toBe('测试操作');

      // 模拟点击
      actionButton.click();
      expect(actionCalled).toBe(true);
    });

    it('应该支持关闭按钮', () => {
      const id = notificationManager.info('测试', '测试消息');
      
      const closeButton = document.querySelector('[data-action="close"]');
      expect(closeButton).toBeTruthy();

      // 模拟点击关闭
      closeButton.click();
      
      // 通知应该被移除
      setTimeout(() => {
        expect(notificationManager.activeNotifications.has(id)).toBe(false);
      }, 350); // 等待动画完成
    });
  });

  describe('自动隐藏', () => {
    it('应该自动隐藏通知', (done) => {
      const id = notificationManager.show({
        type: 'info',
        title: '测试',
        message: '自动隐藏测试',
        autoHide: true,
        duration: 100
      });

      expect(notificationManager.activeNotifications.has(id)).toBe(true);

      setTimeout(() => {
        expect(notificationManager.activeNotifications.has(id)).toBe(false);
        done();
      }, 450); // 等待自动隐藏 + 动画时间
    });

    it('应该显示进度条', () => {
      notificationManager.show({
        type: 'info',
        title: '测试',
        message: '进度条测试',
        autoHide: true,
        duration: 1000
      });

      const progressBar = document.querySelector('.notification-progress');
      expect(progressBar).toBeTruthy();
    });

    it('错误通知不应该自动隐藏', () => {
      const id = notificationManager.error('错误', '不应该自动隐藏');
      
      const notificationData = notificationManager.activeNotifications.get(id);
      expect(notificationData.autoHide).toBe(false);
    });
  });

  describe('队列管理', () => {
    it('应该处理通知队列', () => {
      // 填满显示槽位
      const id1 = notificationManager.info('通知1', '消息1');
      const id2 = notificationManager.info('通知2', '消息2');
      
      // 第三个通知应该进入队列
      notificationManager.info('通知3', '消息3');
      expect(notificationManager.notificationQueue.length).toBe(1);
      
      // 隐藏一个通知
      notificationManager.hide(id1);
      
      // 队列中的通知应该被处理
      setTimeout(() => {
        expect(notificationManager.notificationQueue.length).toBe(0);
        expect(document.querySelectorAll('.notification').length).toBe(2);
      }, 350);
    });
  });

  describe('重试通知', () => {
    it('应该显示重试通知', () => {
      const id = notificationManager.retry('测试操作', 2, 3000);
      
      expect(id).toBeTruthy();
      const notification = document.querySelector('.notification.warning');
      expect(notification).toBeTruthy();
      expect(notification.textContent).toContain('正在重试');
      expect(notification.textContent).toContain('测试操作');
      expect(notification.textContent).toContain('第2次重试');
    });
  });

  describe('通知清理', () => {
    it('应该清理所有通知', () => {
      notificationManager.info('通知1', '消息1');
      notificationManager.info('通知2', '消息2');
      
      expect(notificationManager.activeNotifications.size).toBe(2);
      
      notificationManager.clearAll();
      
      expect(notificationManager.activeNotifications.size).toBe(0);
      expect(notificationManager.notificationQueue.length).toBe(0);
    });

    it('应该在页面卸载时清理通知', () => {
      notificationManager.info('通知1', '消息1');
      
      // 模拟页面卸载事件
      const event = new Event('beforeunload');
      window.dispatchEvent(event);
      
      expect(notificationManager.activeNotifications.size).toBe(0);
    });
  });

  describe('配置更新', () => {
    it('应该支持配置更新', () => {
      const newConfig = {
        maxVisible: 5,
        autoHideDelay: 2000
      };
      
      notificationManager.updateConfig(newConfig);
      
      expect(notificationManager.config.maxVisible).toBe(5);
      expect(notificationManager.config.autoHideDelay).toBe(2000);
      expect(notificationManager.config.showSuccess).toBe(true); // 保持原有配置
    });
  });

  describe('ID生成', () => {
    it('应该生成唯一ID', () => {
      const id1 = notificationManager.generateId();
      const id2 = notificationManager.generateId();
      
      expect(id1).toBeTruthy();
      expect(id2).toBeTruthy();
      expect(id1).not.toBe(id2);
      expect(id1).toContain('notification_');
    });
  });

  describe('边界情况', () => {
    it('应该处理空标题和消息', () => {
      const id = notificationManager.show({
        type: 'info',
        title: '',
        message: ''
      });
      
      expect(id).toBeTruthy();
      const notification = document.querySelector('.notification');
      expect(notification).toBeTruthy();
    });

    it('应该处理无效的通知类型', () => {
      const id = notificationManager.show({
        type: 'invalid',
        title: '测试',
        message: '测试消息'
      });
      
      expect(id).toBeTruthy();
      // 应该使用默认的info样式
      const notification = document.querySelector('.notification');
      expect(notification).toBeTruthy();
    });

    it('应该处理隐藏不存在的通知', () => {
      expect(() => {
        notificationManager.hide('non-existent-id');
      }).not.toThrow();
    });
  });

  describe('性能测试', () => {
    it('应该快速创建通知', () => {
      const startTime = Date.now();
      notificationManager.info('性能测试', '测试消息');
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(50);
    });

    it('应该处理大量通知', () => {
      const startTime = Date.now();
      
      for (let i = 0; i < 100; i++) {
        notificationManager.show({
          type: 'info',
          title: `通知${i}`,
          message: `消息${i}`,
          autoHide: false
        });
      }
      
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(1000);
      expect(notificationManager.activeNotifications.size).toBe(mockConfig.maxVisible);
      expect(notificationManager.notificationQueue.length).toBe(100 - mockConfig.maxVisible);
    });
  });
});
