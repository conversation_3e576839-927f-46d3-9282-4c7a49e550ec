// HTML到Markdown转换器单元测试
describe('HTMLToMarkdown', () => {
  let converter;

  beforeEach(() => {
    converter = new HTMLToMarkdown();
  });

  describe('基本HTML标签转换', () => {
    it('应该转换标题标签', () => {
      expect(converter.convert('<h1>标题1</h1>')).toBe('# 标题1');
      expect(converter.convert('<h2>标题2</h2>')).toBe('## 标题2');
      expect(converter.convert('<h3>标题3</h3>')).toBe('### 标题3');
      expect(converter.convert('<h4>标题4</h4>')).toBe('#### 标题4');
      expect(converter.convert('<h5>标题5</h5>')).toBe('##### 标题5');
      expect(converter.convert('<h6>标题6</h6>')).toBe('###### 标题6');
    });

    it('应该转换段落标签', () => {
      expect(converter.convert('<p>这是一个段落</p>')).toBe('这是一个段落');
      expect(converter.convert('<p>段落1</p><p>段落2</p>')).toBe('段落1\n\n段落2');
    });

    it('应该转换强调标签', () => {
      expect(converter.convert('<strong>粗体</strong>')).toBe('**粗体**');
      expect(converter.convert('<b>粗体</b>')).toBe('**粗体**');
      expect(converter.convert('<em>斜体</em>')).toBe('*斜体*');
      expect(converter.convert('<i>斜体</i>')).toBe('*斜体*');
    });

    it('应该转换链接标签', () => {
      expect(converter.convert('<a href="https://example.com">链接</a>'))
        .toBe('[链接](https://example.com)');
      
      expect(converter.convert('<a href="https://example.com" title="标题">链接</a>'))
        .toBe('[链接](https://example.com "标题")');
    });

    it('应该转换图片标签', () => {
      expect(converter.convert('<img src="image.jpg" alt="图片">'))
        .toBe('![图片](image.jpg)');
      
      expect(converter.convert('<img src="image.jpg" alt="图片" title="标题">'))
        .toBe('![图片](image.jpg "标题")');
    });

    it('应该转换代码标签', () => {
      expect(converter.convert('<code>代码</code>')).toBe('`代码`');
      expect(converter.convert('<pre><code>代码块</code></pre>')).toBe('```\n代码块\n```');
    });
  });

  describe('列表转换', () => {
    it('应该转换无序列表', () => {
      const html = '<ul><li>项目1</li><li>项目2</li><li>项目3</li></ul>';
      const expected = '- 项目1\n- 项目2\n- 项目3';
      expect(converter.convert(html)).toBe(expected);
    });

    it('应该转换有序列表', () => {
      const html = '<ol><li>项目1</li><li>项目2</li><li>项目3</li></ol>';
      const expected = '1. 项目1\n2. 项目2\n3. 项目3';
      expect(converter.convert(html)).toBe(expected);
    });

    it('应该转换嵌套列表', () => {
      const html = `
        <ul>
          <li>项目1
            <ul>
              <li>子项目1</li>
              <li>子项目2</li>
            </ul>
          </li>
          <li>项目2</li>
        </ul>
      `;
      const result = converter.convert(html);
      expect(result).toContain('- 项目1');
      expect(result).toContain('  - 子项目1');
      expect(result).toContain('  - 子项目2');
      expect(result).toContain('- 项目2');
    });
  });

  describe('表格转换', () => {
    it('应该转换简单表格', () => {
      const html = `
        <table>
          <thead>
            <tr><th>标题1</th><th>标题2</th></tr>
          </thead>
          <tbody>
            <tr><td>内容1</td><td>内容2</td></tr>
            <tr><td>内容3</td><td>内容4</td></tr>
          </tbody>
        </table>
      `;
      const result = converter.convert(html);
      expect(result).toContain('| 标题1 | 标题2 |');
      expect(result).toContain('|-------|-------|');
      expect(result).toContain('| 内容1 | 内容2 |');
      expect(result).toContain('| 内容3 | 内容4 |');
    });

    it('应该处理没有thead的表格', () => {
      const html = `
        <table>
          <tr><td>内容1</td><td>内容2</td></tr>
          <tr><td>内容3</td><td>内容4</td></tr>
        </table>
      `;
      const result = converter.convert(html);
      expect(result).toContain('| 内容1 | 内容2 |');
      expect(result).toContain('| 内容3 | 内容4 |');
    });
  });

  describe('引用转换', () => {
    it('应该转换blockquote', () => {
      expect(converter.convert('<blockquote>这是引用</blockquote>'))
        .toBe('> 这是引用');
    });

    it('应该转换嵌套引用', () => {
      const html = '<blockquote>外层引用<blockquote>内层引用</blockquote></blockquote>';
      const result = converter.convert(html);
      expect(result).toContain('> 外层引用');
      expect(result).toContain('> > 内层引用');
    });
  });

  describe('换行和分隔符', () => {
    it('应该转换br标签', () => {
      expect(converter.convert('第一行<br>第二行')).toBe('第一行\n第二行');
    });

    it('应该转换hr标签', () => {
      expect(converter.convert('<hr>')).toBe('---');
      expect(converter.convert('内容1<hr>内容2')).toBe('内容1\n\n---\n\n内容2');
    });
  });

  describe('复杂HTML处理', () => {
    it('应该处理嵌套标签', () => {
      const html = '<p><strong>粗体<em>斜体</em></strong>普通文本</p>';
      const result = converter.convert(html);
      expect(result).toContain('**粗体*斜体***');
      expect(result).toContain('普通文本');
    });

    it('应该处理混合内容', () => {
      const html = `
        <h1>标题</h1>
        <p>这是一个包含<strong>粗体</strong>和<a href="https://example.com">链接</a>的段落。</p>
        <ul>
          <li>列表项1</li>
          <li>列表项2</li>
        </ul>
        <blockquote>这是引用</blockquote>
      `;
      const result = converter.convert(html);
      expect(result).toContain('# 标题');
      expect(result).toContain('**粗体**');
      expect(result).toContain('[链接](https://example.com)');
      expect(result).toContain('- 列表项1');
      expect(result).toContain('> 这是引用');
    });
  });

  describe('特殊字符处理', () => {
    it('应该转义Markdown特殊字符', () => {
      expect(converter.convert('文本*星号*文本')).toBe('文本\\*星号\\*文本');
      expect(converter.convert('文本_下划线_文本')).toBe('文本\\_下划线\\_文本');
      expect(converter.convert('文本`反引号`文本')).toBe('文本\\`反引号\\`文本');
    });

    it('应该处理HTML实体', () => {
      expect(converter.convert('&lt;标签&gt;')).toBe('<标签>');
      expect(converter.convert('&amp;符号')).toBe('&符号');
      expect(converter.convert('&quot;引号&quot;')).toBe('"引号"');
      expect(converter.convert('&nbsp;')).toBe(' ');
    });
  });

  describe('边界情况', () => {
    it('应该处理空字符串', () => {
      expect(converter.convert('')).toBe('');
    });

    it('应该处理纯文本', () => {
      expect(converter.convert('纯文本内容')).toBe('纯文本内容');
    });

    it('应该处理无效HTML', () => {
      expect(converter.convert('<invalid>内容</invalid>')).toBe('内容');
    });

    it('应该处理未闭合标签', () => {
      expect(converter.convert('<p>段落内容')).toBe('段落内容');
    });

    it('应该处理空标签', () => {
      expect(converter.convert('<p></p>')).toBe('');
      expect(converter.convert('<strong></strong>')).toBe('');
    });
  });

  describe('格式检测', () => {
    it('应该检测HTML格式', () => {
      expect(converter.detectFormat('<p>HTML内容</p>')).toBe('html');
      expect(converter.detectFormat('<div>HTML内容</div>')).toBe('html');
      expect(converter.detectFormat('<span>HTML内容</span>')).toBe('html');
    });

    it('应该检测Markdown格式', () => {
      expect(converter.detectFormat('# Markdown标题')).toBe('markdown');
      expect(converter.detectFormat('**粗体文本**')).toBe('markdown');
      expect(converter.detectFormat('- 列表项')).toBe('markdown');
    });

    it('应该检测纯文本格式', () => {
      expect(converter.detectFormat('纯文本内容')).toBe('text');
      expect(converter.detectFormat('没有特殊格式的文本')).toBe('text');
    });
  });

  describe('性能测试', () => {
    it('应该快速处理小文档', () => {
      const html = '<p>简单内容</p>';
      const startTime = Date.now();
      const result = converter.convert(html);
      const endTime = Date.now();
      
      expect(result).toBe('简单内容');
      expect(endTime - startTime).toBeLessThan(10);
    });

    it('应该处理大文档', () => {
      const largeHtml = '<p>' + 'a'.repeat(10000) + '</p>';
      const startTime = Date.now();
      const result = converter.convert(largeHtml);
      const endTime = Date.now();
      
      expect(result.length).toBe(10000);
      expect(endTime - startTime).toBeLessThan(100);
    });
  });
});
