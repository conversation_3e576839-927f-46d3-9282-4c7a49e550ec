// AI服务管理器单元测试
describe('AIServiceManager', () => {
  let aiServiceManager;

  beforeEach(() => {
    aiServiceManager = new AIServiceManager();
  });

  describe('服务商配置', () => {
    it('应该返回所有可用的服务商', () => {
      const providers = aiServiceManager.getProviders();

      expect(providers).toHaveLength(4);
      expect(providers.map(p => p.id)).toContain('siliconflow');
      expect(providers.map(p => p.id)).toContain('openrouter');
      expect(providers.map(p => p.id)).toContain('deepseek');
      expect(providers.map(p => p.id)).toContain('moonshot');
    });

    it('应该返回正确的硅基流动配置', () => {
      const providers = aiServiceManager.getProviders();
      const siliconflow = providers.find(p => p.id === 'siliconflow');

      expect(siliconflow).toBeTruthy();
      expect(siliconflow.name).toBe('硅基流动');
      expect(siliconflow.baseURL).toBe('https://api.siliconflow.cn/v1');
      expect(siliconflow.models).toHaveProperty('Qwen/Qwen2.5-7B-Instruct');
    });

    it('应该返回正确的OpenRouter配置', () => {
      const providers = aiServiceManager.getProviders();
      const openrouter = providers.find(p => p.id === 'openrouter');

      expect(openrouter).toBeTruthy();
      expect(openrouter.name).toBe('OpenRouter');
      expect(openrouter.baseURL).toBe('https://openrouter.ai/api/v1');
      expect(openrouter.models).toHaveProperty('anthropic/claude-3-haiku');
    });

    it('应该返回正确的DeepSeek配置', () => {
      const providers = aiServiceManager.getProviders();
      const deepseek = providers.find(p => p.id === 'deepseek');

      expect(deepseek).toBeTruthy();
      expect(deepseek.name).toBe('DeepSeek');
      expect(deepseek.baseURL).toBe('https://api.deepseek.com/v1');
      expect(deepseek.models).toHaveProperty('deepseek-chat');
    });

    it('应该返回正确的Moonshot配置', () => {
      const providers = aiServiceManager.getProviders();
      const moonshot = providers.find(p => p.id === 'moonshot');

      expect(moonshot).toBeTruthy();
      expect(moonshot.name).toBe('Moonshot AI');
      expect(moonshot.baseURL).toBe('https://api.moonshot.cn/v1');
      expect(moonshot.models).toHaveProperty('moonshot-v1-8k');
    });
  });

  describe('API请求构建', () => {
    it('应该构建正确的请求配置', () => {
      const config = aiServiceManager.buildRequestConfig(
        'siliconflow',
        'test-api-key',
        'Qwen/Qwen2.5-7B-Instruct',
        'Test message'
      );

      expect(config.url).toBe('https://api.siliconflow.cn/v1/chat/completions');
      expect(config.method).toBe('POST');
      expect(config.headers).toHaveProperty('Authorization', 'Bearer test-api-key');
      expect(config.headers).toHaveProperty('Content-Type', 'application/json');

      const body = JSON.parse(config.body);
      expect(body.model).toBe('Qwen/Qwen2.5-7B-Instruct');
      expect(body.messages).toHaveLength(1);
      expect(body.messages[0].content).toBe('Test message');
    });

    it('应该为不同服务商使用正确的baseURL', () => {
      const configs = [
        { provider: 'siliconflow', expectedURL: 'https://api.siliconflow.cn/v1/chat/completions' },
        { provider: 'openrouter', expectedURL: 'https://openrouter.ai/api/v1/chat/completions' },
        { provider: 'deepseek', expectedURL: 'https://api.deepseek.com/v1/chat/completions' },
        { provider: 'moonshot', expectedURL: 'https://api.moonshot.cn/v1/chat/completions' }
      ];

      configs.forEach(({ provider, expectedURL }) => {
        const config = aiServiceManager.buildRequestConfig(
          provider,
          'test-key',
          'test-model',
          'test-message'
        );
        expect(config.url).toBe(expectedURL);
      });
    });

    it('应该设置正确的请求参数', () => {
      const config = aiServiceManager.buildRequestConfig(
        'siliconflow',
        'test-key',
        'test-model',
        'test-message'
      );

      const body = JSON.parse(config.body);
      expect(body.temperature).toBe(0.7);
      expect(body.max_tokens).toBe(1000);
      expect(body.stream).toBe(false);
    });
  });

  describe('API密钥验证', () => {
    let originalFetch;

    beforeEach(() => {
      originalFetch = global.fetch;
    });

    afterEach(() => {
      global.fetch = originalFetch;
    });

    it('应该验证有效的API密钥', async () => {
      // 模拟成功的API响应
      global.fetch = testFramework.createSpy(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: { content: 'Test response' }
            }]
          })
        })
      );

      const result = await aiServiceManager.validateApiKey(
        'siliconflow',
        'valid-key',
        'Qwen/Qwen2.5-7B-Instruct'
      );

      expect(result.valid).toBe(true);
      expect(result.message).toContain('验证成功');
      expect(result.model).toBe('Qwen/Qwen2.5-7B-Instruct');
    });

    it('应该检测无效的API密钥', async () => {
      // 模拟401错误响应
      global.fetch = testFramework.createSpy(() =>
        Promise.resolve({
          ok: false,
          status: 401,
          statusText: 'Unauthorized'
        })
      );

      const result = await aiServiceManager.validateApiKey(
        'siliconflow',
        'invalid-key',
        'test-model'
      );

      expect(result.valid).toBe(false);
      expect(result.message).toContain('API密钥无效');
    });

    it('应该处理网络错误', async () => {
      // 模拟网络错误
      global.fetch = testFramework.createSpy(() =>
        Promise.reject(new Error('Network error'))
      );

      const result = await aiServiceManager.validateApiKey(
        'siliconflow',
        'test-key',
        'test-model'
      );

      expect(result.valid).toBe(false);
      expect(result.message).toContain('网络连接失败');
    });

    it('应该处理超时错误', async () => {
      // 模拟超时错误
      global.fetch = testFramework.createSpy(() =>
        Promise.reject(new Error('AbortError'))
      );

      const result = await aiServiceManager.validateApiKey(
        'siliconflow',
        'test-key',
        'test-model'
      );

      expect(result.valid).toBe(false);
      expect(result.message).toContain('请求超时');
    });

    it('应该处理服务器错误', async () => {
      // 模拟500错误响应
      global.fetch = testFramework.createSpy(() =>
        Promise.resolve({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error'
        })
      );

      const result = await aiServiceManager.validateApiKey(
        'siliconflow',
        'test-key',
        'test-model'
      );

      expect(result.valid).toBe(false);
      expect(result.message).toContain('服务器错误');
    });
  });

  describe('模型配置', () => {
    it('应该返回服务商的所有模型', () => {
      const providers = aiServiceManager.getProviders();
      const siliconflow = providers.find(p => p.id === 'siliconflow');

      expect(Object.keys(siliconflow.models).length).toBeGreaterThan(0);
      expect(siliconflow.models).toHaveProperty('Qwen/Qwen2.5-7B-Instruct');
    });

    it('应该有默认模型配置', () => {
      const providers = aiServiceManager.getProviders();

      providers.forEach(provider => {
        expect(provider.defaultModel).toBeTruthy();
        expect(provider.models).toHaveProperty(provider.defaultModel);
      });
    });
  });

  describe('错误处理', () => {
    it('应该处理无效的服务商ID', () => {
      expect(() => {
        aiServiceManager.buildRequestConfig(
          'invalid-provider',
          'test-key',
          'test-model',
          'test-message'
        );
      }).toThrow('不支持的AI服务商');
    });

    it('应该处理空的API密钥', async () => {
      const result = await aiServiceManager.validateApiKey(
        'siliconflow',
        '',
        'test-model'
      );

      expect(result.valid).toBe(false);
      expect(result.message).toContain('API密钥不能为空');
    });

    it('应该处理空的模型名称', async () => {
      const result = await aiServiceManager.validateApiKey(
        'siliconflow',
        'test-key',
        ''
      );

      expect(result.valid).toBe(false);
      expect(result.message).toContain('模型名称不能为空');
    });
  });

  describe('请求头配置', () => {
    it('应该为OpenRouter设置正确的请求头', () => {
      const config = aiServiceManager.buildRequestConfig(
        'openrouter',
        'test-key',
        'test-model',
        'test-message'
      );

      expect(config.headers).toHaveProperty('HTTP-Referer', 'https://github.com/your-repo');
      expect(config.headers).toHaveProperty('X-Title', 'Flomo Chrome Extension');
    });

    it('应该为其他服务商设置标准请求头', () => {
      const providers = ['siliconflow', 'deepseek', 'moonshot'];

      providers.forEach(provider => {
        const config = aiServiceManager.buildRequestConfig(
          provider,
          'test-key',
          'test-model',
          'test-message'
        );

        expect(config.headers).toHaveProperty('Authorization', 'Bearer test-key');
        expect(config.headers).toHaveProperty('Content-Type', 'application/json');
        expect(config.headers).not.toHaveProperty('HTTP-Referer');
      });
    });
  });

  describe('边界情况处理', () => {
    it('应该处理空参数', () => {
      expect(() => {
        aiServiceManager.buildRequestConfig('', '', '', '');
      }).toThrow();
    });

    it('应该处理null参数', () => {
      expect(() => {
        aiServiceManager.buildRequestConfig(null, null, null, null);
      }).toThrow();
    });

    it('应该处理undefined参数', () => {
      expect(() => {
        aiServiceManager.buildRequestConfig(undefined, undefined, undefined, undefined);
      }).toThrow();
    });
  });

  describe('性能测试', () => {
    it('应该快速返回服务商列表', () => {
      const startTime = Date.now();
      const providers = aiServiceManager.getProviders();
      const endTime = Date.now();

      expect(providers).toHaveLength(4);
      expect(endTime - startTime).toBeLessThan(10); // 应该在10ms内完成
    });

    it('应该快速构建请求配置', () => {
      const startTime = Date.now();
      const config = aiServiceManager.buildRequestConfig(
        'siliconflow',
        'test-key',
        'test-model',
        'test-message'
      );
      const endTime = Date.now();

      expect(config).toBeTruthy();
      expect(endTime - startTime).toBeLessThan(5); // 应该在5ms内完成
    });
  });
});
