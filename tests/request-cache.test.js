// 请求缓存测试
describe('RequestCache', () => {
  let cache;

  beforeEach(() => {
    cache = new RequestCache({
      defaultTTL: 1000, // 1秒，便于测试
      maxEntries: 3,
      enableCompression: true,
      enableStats: true
    });
  });

  describe('generateCacheKey', () => {
    it('应该生成一致的缓存键', () => {
      const key1 = cache.generateCacheKey('testFunction', 'content', { option: 'value' });
      const key2 = cache.generateCacheKey('testFunction', 'content', { option: 'value' });
      
      expect(key1).toBe(key2);
    });

    it('应该为不同内容生成不同的键', () => {
      const key1 = cache.generateCacheKey('testFunction', 'content1', {});
      const key2 = cache.generateCacheKey('testFunction', 'content2', {});
      
      expect(key1).not.toBe(key2);
    });

    it('应该为不同选项生成不同的键', () => {
      const key1 = cache.generateCacheKey('testFunction', 'content', { option: 'value1' });
      const key2 = cache.generateCacheKey('testFunction', 'content', { option: 'value2' });
      
      expect(key1).not.toBe(key2);
    });
  });

  describe('hashString', () => {
    it('应该为相同字符串生成相同哈希', () => {
      const hash1 = cache.hashString('test string');
      const hash2 = cache.hashString('test string');
      
      expect(hash1).toBe(hash2);
    });

    it('应该为不同字符串生成不同哈希', () => {
      const hash1 = cache.hashString('string1');
      const hash2 = cache.hashString('string2');
      
      expect(hash1).not.toBe(hash2);
    });

    it('应该处理空字符串', () => {
      const hash = cache.hashString('');
      expect(hash).toBe('0');
    });
  });

  describe('compressData', () => {
    it('应该压缩数据', () => {
      const data = { key: 'value', longString: 'a'.repeat(100) };
      const compressed = cache.compressData(data);
      
      expect(compressed.compressed).toBe(true);
      expect(compressed.compressedSize).toBeLessThan(compressed.originalSize);
    });

    it('应该在压缩失败时返回原始数据', () => {
      // 模拟压缩失败
      const originalStringify = JSON.stringify;
      JSON.stringify = jest.fn().mockImplementation(() => {
        throw new Error('stringify failed');
      });
      
      const data = { key: 'value' };
      const result = cache.compressData(data);
      
      expect(result).toBe(data);
      
      JSON.stringify = originalStringify;
    });

    it('应该在禁用压缩时返回原始数据', () => {
      cache.config.enableCompression = false;
      const data = { key: 'value' };
      const result = cache.compressData(data);
      
      expect(result).toBe(data);
    });
  });

  describe('decompressData', () => {
    it('应该解压缩数据', () => {
      const originalData = { key: 'value' };
      const compressed = cache.compressData(originalData);
      const decompressed = cache.decompressData(compressed);
      
      expect(decompressed).toEqual(originalData);
    });

    it('应该处理未压缩的数据', () => {
      const data = { key: 'value' };
      const result = cache.decompressData(data);
      
      expect(result).toBe(data);
    });

    it('应该处理解压失败', () => {
      const invalidCompressed = { compressed: true, data: 'invalid json' };
      const result = cache.decompressData(invalidCompressed);
      
      expect(result).toBeNull();
    });
  });

  describe('set and get', () => {
    it('应该设置和获取缓存', async () => {
      const functionName = 'testFunction';
      const content = 'test content';
      const options = { option: 'value' };
      const data = { result: 'test result' };
      
      await cache.set(functionName, content, options, data);
      const retrieved = await cache.get(functionName, content, options);
      
      expect(retrieved).toEqual(data);
    });

    it('应该在缓存未命中时返回null', async () => {
      const result = await cache.get('nonexistent', 'content', {});
      expect(result).toBeNull();
    });

    it('应该在缓存过期时返回null', async () => {
      const functionName = 'testFunction';
      const content = 'test content';
      const data = { result: 'test result' };
      
      await cache.set(functionName, content, {}, data);
      
      // 等待缓存过期
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      const result = await cache.get(functionName, content, {});
      expect(result).toBeNull();
    });

    it('应该更新访问统计', async () => {
      const functionName = 'testFunction';
      const content = 'test content';
      const data = { result: 'test result' };
      
      await cache.set(functionName, content, {}, data);
      await cache.get(functionName, content, {});
      await cache.get(functionName, content, {});
      
      const stats = cache.getStats();
      expect(stats.hits).toBe(2);
      expect(stats.sets).toBe(1);
    });
  });

  describe('evictLeastRecentlyUsed', () => {
    it('应该淘汰最少使用的缓存项', async () => {
      // 填满缓存
      await cache.set('func1', 'content1', {}, { data: 1 });
      await cache.set('func2', 'content2', {}, { data: 2 });
      await cache.set('func3', 'content3', {}, { data: 3 });
      
      // 访问前两个，使第三个成为最少使用的
      await cache.get('func1', 'content1', {});
      await cache.get('func2', 'content2', {});
      
      // 添加新项应该淘汰第三个
      await cache.set('func4', 'content4', {}, { data: 4 });
      
      const result3 = await cache.get('func3', 'content3', {});
      const result4 = await cache.get('func4', 'content4', {});
      
      expect(result3).toBeNull();
      expect(result4).toEqual({ data: 4 });
    });
  });

  describe('cleanup', () => {
    it('应该清理过期的缓存项', async () => {
      await cache.set('func1', 'content1', {}, { data: 1 });
      await cache.set('func2', 'content2', {}, { data: 2 });
      
      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      const cleanedCount = cache.cleanup();
      expect(cleanedCount).toBe(2);
      expect(cache.cache.size).toBe(0);
    });
  });

  describe('clear', () => {
    it('应该清空所有缓存', async () => {
      await cache.set('func1', 'content1', {}, { data: 1 });
      await cache.set('func2', 'content2', {}, { data: 2 });
      
      cache.clear();
      
      expect(cache.cache.size).toBe(0);
    });
  });

  describe('clearFunction', () => {
    it('应该清空特定功能的缓存', async () => {
      await cache.set('func1', 'content1', {}, { data: 1 });
      await cache.set('func1', 'content2', {}, { data: 2 });
      await cache.set('func2', 'content1', {}, { data: 3 });
      
      const deletedCount = cache.clearFunction('func1');
      
      expect(deletedCount).toBe(2);
      
      const result1 = await cache.get('func1', 'content1', {});
      const result2 = await cache.get('func2', 'content1', {});
      
      expect(result1).toBeNull();
      expect(result2).toEqual({ data: 3 });
    });
  });

  describe('getStats', () => {
    it('应该返回正确的统计信息', async () => {
      await cache.set('func1', 'content1', {}, { data: 1 });
      await cache.get('func1', 'content1', {});
      await cache.get('nonexistent', 'content', {});
      
      const stats = cache.getStats();
      
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.sets).toBe(1);
      expect(stats.currentSize).toBe(1);
      expect(stats.hitRate).toBe('50.00%');
    });
  });

  describe('getCacheDetails', () => {
    it('应该返回缓存详情', async () => {
      await cache.set('func1', 'content1', {}, { data: 1 });
      await cache.set('func2', 'content2', {}, { data: 2 });
      
      const details = cache.getCacheDetails();
      
      expect(details).toHaveLength(2);
      expect(details[0]).toHaveProperty('functionName');
      expect(details[0]).toHaveProperty('createdAt');
      expect(details[0]).toHaveProperty('accessCount');
    });
  });

  describe('exportCache', () => {
    it('应该导出缓存数据', async () => {
      await cache.set('func1', 'content1', {}, { data: 1 });
      
      const exported = cache.exportCache();
      
      expect(exported).toHaveProperty('cache');
      expect(exported).toHaveProperty('stats');
      expect(exported).toHaveProperty('config');
      expect(exported.cache).toHaveLength(1);
    });
  });

  describe('功能特定的TTL', () => {
    it('应该使用功能特定的缓存时间', async () => {
      // 设置不同的TTL
      cache.config.functionTTL.shortLived = 100; // 0.1秒
      cache.config.functionTTL.longLived = 2000; // 2秒
      
      await cache.set('shortLived', 'content', {}, { data: 'short' });
      await cache.set('longLived', 'content', {}, { data: 'long' });
      
      // 等待短期缓存过期
      await new Promise(resolve => setTimeout(resolve, 150));
      
      const shortResult = await cache.get('shortLived', 'content', {});
      const longResult = await cache.get('longLived', 'content', {});
      
      expect(shortResult).toBeNull();
      expect(longResult).toEqual({ data: 'long' });
    });
  });
});
