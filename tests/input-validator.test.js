// 输入验证器测试
describe('InputValidator', () => {
  let validator;

  beforeEach(() => {
    validator = new InputValidator();
  });

  describe('validateAndCleanContent', () => {
    it('应该接受正常的文本内容', () => {
      const content = '这是一个正常的文本内容';
      const result = validator.validateAndCleanContent(content);
      expect(result).toBe(content);
    });

    it('应该拒绝过长的内容', () => {
      const longContent = 'A'.repeat(10001);
      expect(() => {
        validator.validateAndCleanContent(longContent);
      }).toThrow('内容过长');
    });

    it('应该清理控制字符', () => {
      const contentWithControlChars = '正常内容\x00\x01\x02异常字符';
      const result = validator.validateAndCleanContent(contentWithControlChars);
      expect(result).toBe('正常内容异常字符');
    });

    it('应该清理零宽字符', () => {
      const contentWithZeroWidth = '正常内容\u200B\u200C\u200D零宽字符';
      const result = validator.validateAndCleanContent(contentWithZeroWidth);
      expect(result).toBe('正常内容零宽字符');
    });

    it('应该统一换行符', () => {
      const contentWithCRLF = '第一行\r\n第二行\r\n第三行';
      const result = validator.validateAndCleanContent(contentWithCRLF);
      expect(result).toBe('第一行\n第二行\n第三行');
    });

    it('应该检测可疑脚本内容', () => {
      const suspiciousContent = '<script>alert("xss")</script>';
      expect(() => {
        validator.validateAndCleanContent(suspiciousContent);
      }).toThrow('内容包含不安全的脚本代码');
    });
  });

  describe('validateAndCleanHTML', () => {
    it('应该移除危险的script标签', () => {
      const htmlWithScript = '<div>正常内容</div><script>alert("xss")</script>';
      const result = validator.validateAndCleanHTML(htmlWithScript);
      expect(result).not.toContain('<script>');
      expect(result).toContain('<div>正常内容</div>');
    });

    it('应该移除危险的iframe标签', () => {
      const htmlWithIframe = '<p>内容</p><iframe src="javascript:alert(1)"></iframe>';
      const result = validator.validateAndCleanHTML(htmlWithIframe);
      expect(result).not.toContain('<iframe>');
      expect(result).toContain('<p>内容</p>');
    });

    it('应该移除危险的事件属性', () => {
      const htmlWithEvents = '<div onclick="alert(1)" onload="evil()">内容</div>';
      const result = validator.validateAndCleanHTML(htmlWithEvents);
      expect(result).not.toContain('onclick');
      expect(result).not.toContain('onload');
      expect(result).toContain('<div>内容</div>');
    });

    it('应该移除javascript协议的链接', () => {
      const htmlWithJSLink = '<a href="javascript:alert(1)">链接</a>';
      const result = validator.validateAndCleanHTML(htmlWithJSLink);
      expect(result).not.toContain('javascript:');
      expect(result).toContain('<a>链接</a>');
    });

    it('应该拒绝过长的HTML内容', () => {
      const longHTML = '<div>' + 'A'.repeat(20001) + '</div>';
      expect(() => {
        validator.validateAndCleanHTML(longHTML);
      }).toThrow('HTML内容过长');
    });
  });

  describe('validateUrl', () => {
    it('应该接受有效的HTTPS URL', () => {
      const validUrls = [
        'https://flomoapp.com/api',
        'https://api.siliconflow.cn/v1/chat',
        'https://openrouter.ai/api/v1/chat'
      ];

      validUrls.forEach(url => {
        const result = validator.validateUrl(url);
        expect(result).toBe(url);
      });
    });

    it('应该拒绝HTTP协议的URL', () => {
      const httpUrl = 'http://example.com/api';
      expect(() => {
        validator.validateUrl(httpUrl);
      }).toThrow('URL必须使用HTTPS协议');
    });

    it('应该拒绝不在白名单中的域名', () => {
      const maliciousUrl = 'https://evil.com/steal-data';
      expect(() => {
        validator.validateUrl(maliciousUrl);
      }).toThrow('URL不在允许的域名列表中');
    });

    it('应该拒绝无效的URL格式', () => {
      const invalidUrls = [
        'not-a-url',
        'ftp://example.com',
        'javascript:alert(1)',
        ''
      ];

      invalidUrls.forEach(url => {
        expect(() => {
          validator.validateUrl(url);
        }).toThrow();
      });
    });

    it('应该拒绝过长的URL', () => {
      const longUrl = 'https://flomoapp.com/' + 'a'.repeat(2000);
      expect(() => {
        validator.validateUrl(longUrl);
      }).toThrow('URL过长');
    });
  });

  describe('validateApiKey', () => {
    it('应该接受有效的API密钥格式', () => {
      const validKeys = [
        'sk-1234567890abcdef',
        'api_key_123456789',
        'Bearer.token.here'
      ];

      validKeys.forEach(key => {
        const result = validator.validateApiKey(key);
        expect(result).toBe(key);
      });
    });

    it('应该拒绝空的API密钥', () => {
      expect(() => {
        validator.validateApiKey('');
      }).toThrow('API密钥不能为空');
    });

    it('应该拒绝过长的API密钥', () => {
      const longKey = 'sk-' + 'a'.repeat(200);
      expect(() => {
        validator.validateApiKey(longKey);
      }).toThrow('API密钥过长');
    });

    it('应该拒绝包含无效字符的API密钥', () => {
      const invalidKeys = [
        'sk-key with spaces',
        'sk-key<script>',
        'sk-key"quotes"',
        'sk-key中文'
      ];

      invalidKeys.forEach(key => {
        expect(() => {
          validator.validateApiKey(key);
        }).toThrow('API密钥包含无效字符');
      });
    });
  });

  describe('validateProviderId', () => {
    it('应该接受支持的提供商ID', () => {
      const validProviders = ['siliconflow', 'openrouter', 'deepseek', 'moonshot'];
      
      validProviders.forEach(provider => {
        const result = validator.validateProviderId(provider);
        expect(result).toBe(provider);
      });
    });

    it('应该拒绝不支持的提供商ID', () => {
      const invalidProviders = ['unknown', 'malicious', '', 'openai'];
      
      invalidProviders.forEach(provider => {
        expect(() => {
          validator.validateProviderId(provider);
        }).toThrow('不支持的AI服务提供商');
      });
    });
  });

  describe('validateModelName', () => {
    it('应该接受有效的模型名称', () => {
      const validModels = [
        'gpt-3.5-turbo',
        'claude-3-sonnet',
        'deepseek-chat',
        'moonshot-v1-8k'
      ];

      validModels.forEach(model => {
        const result = validator.validateModelName(model);
        expect(result).toBe(model);
      });
    });

    it('应该拒绝空的模型名称', () => {
      expect(() => {
        validator.validateModelName('');
      }).toThrow('模型名称不能为空');
    });

    it('应该拒绝过长的模型名称', () => {
      const longModel = 'a'.repeat(101);
      expect(() => {
        validator.validateModelName(longModel);
      }).toThrow('模型名称过长');
    });

    it('应该拒绝包含无效字符的模型名称', () => {
      const invalidModels = [
        'model with spaces',
        'model<script>',
        'model"quotes"'
      ];

      invalidModels.forEach(model => {
        expect(() => {
          validator.validateModelName(model);
        }).toThrow('模型名称包含无效字符');
      });
    });
  });

  describe('containsSuspiciousContent', () => {
    it('应该检测脚本标签', () => {
      const suspiciousContent = '<script>alert("xss")</script>';
      expect(validator.containsSuspiciousContent(suspiciousContent)).toBe(true);
    });

    it('应该检测javascript协议', () => {
      const suspiciousContent = 'javascript:alert(1)';
      expect(validator.containsSuspiciousContent(suspiciousContent)).toBe(true);
    });

    it('应该检测事件处理器', () => {
      const suspiciousContent = '<img onerror="alert(1)">';
      expect(validator.containsSuspiciousContent(suspiciousContent)).toBe(true);
    });

    it('应该接受正常内容', () => {
      const normalContent = '这是正常的文本内容，没有任何可疑代码';
      expect(validator.containsSuspiciousContent(normalContent)).toBe(false);
    });
  });

  describe('validateObject', () => {
    it('应该验证对象的所有属性', () => {
      const obj = {
        content: '正常内容',
        url: 'https://flomoapp.com/api',
        provider: 'siliconflow'
      };

      const schema = {
        content: { type: 'content', required: true },
        url: { type: 'url', required: true },
        provider: { type: 'provider', required: true }
      };

      const result = validator.validateObject(obj, schema);
      expect(result.content).toBe(obj.content);
      expect(result.url).toBe(obj.url);
      expect(result.provider).toBe(obj.provider);
    });

    it('应该检测缺少必需字段', () => {
      const obj = {
        content: '内容'
      };

      const schema = {
        content: { type: 'content', required: true },
        url: { type: 'url', required: true }
      };

      expect(() => {
        validator.validateObject(obj, schema);
      }).toThrow('缺少必需字段: url');
    });

    it('应该处理验证错误', () => {
      const obj = {
        content: '<script>alert(1)</script>',
        provider: 'invalid'
      };

      const schema = {
        content: { type: 'content', required: true },
        provider: { type: 'provider', required: true }
      };

      expect(() => {
        validator.validateObject(obj, schema);
      }).toThrow('验证失败');
    });
  });
});
