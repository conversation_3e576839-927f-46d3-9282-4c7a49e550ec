// 网页内容引导系统
class ContentOnboarding {
  constructor() {
    this.isActive = false;
    this.tooltip = null;
    this.init();
  }

  async init() {
    // 监听来自扩展的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'showWebPageGuide') {
        this.showWebPageGuide();
        sendResponse({ success: true });
      }
      return true;
    });

    // 检查是否需要显示网页引导
    const result = await chrome.storage.sync.get(['hasSeenWebPageGuide', 'hasCompletedOnboarding']);

    // 如果用户完成了设置引导但没有看过网页引导，显示网页引导
    if (result.hasCompletedOnboarding && !result.hasSeenWebPageGuide) {
      setTimeout(() => {
        this.showWebPageGuide();
      }, 2000);
    }
  }

  showWebPageGuide() {
    if (this.isActive) return;

    this.isActive = true;
    this.createTooltip();
  }

  createTooltip() {
    // 创建引导提示框
    this.tooltip = document.createElement('div');
    this.tooltip.className = 'flomo-web-guide';
    this.tooltip.innerHTML = `
      <style>
        .flomo-web-guide {
          position: fixed;
          top: 20px;
          right: 20px;
          background: white;
          border-radius: 12px;
          padding: 20px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
          max-width: 320px;
          z-index: 999999;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
          border: 1px solid #e0e0e0;
          animation: slideInFromRight 0.3s ease;
        }
        
        @keyframes slideInFromRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        .flomo-web-guide h3 {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1d1d1f;
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .flomo-web-guide p {
          margin: 0 0 16px 0;
          font-size: 14px;
          line-height: 1.5;
          color: #6e6e73;
        }
        
        .flomo-web-guide .steps {
          margin: 16px 0;
        }
        
        .flomo-web-guide .step {
          display: flex;
          align-items: flex-start;
          gap: 8px;
          margin-bottom: 12px;
          font-size: 13px;
          line-height: 1.4;
        }
        
        .flomo-web-guide .step-number {
          background: #007aff;
          color: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 11px;
          font-weight: 600;
          flex-shrink: 0;
        }
        
        .flomo-web-guide .step-text {
          color: #1d1d1f;
        }
        
        .flomo-web-guide .buttons {
          display: flex;
          gap: 8px;
          justify-content: flex-end;
          margin-top: 16px;
        }
        
        .flomo-web-guide button {
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          font-size: 13px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }
        
        .flomo-web-guide .btn-secondary {
          background: #f2f2f7;
          color: #6e6e73;
        }
        
        .flomo-web-guide .btn-secondary:hover {
          background: #e5e5ea;
        }
        
        .flomo-web-guide .btn-primary {
          background: #007aff;
          color: white;
        }
        
        .flomo-web-guide .btn-primary:hover {
          background: #0070e0;
        }
        
        .flomo-web-guide .demo-text {
          background: #f8f9fa;
          border: 2px dashed #007aff;
          border-radius: 6px;
          padding: 12px;
          margin: 12px 0;
          font-size: 13px;
          color: #1d1d1f;
          cursor: pointer;
          transition: all 0.2s ease;
          user-select: text;
        }
        
        .flomo-web-guide .demo-text:hover {
          background: #e7f3ff;
          border-color: #0070e0;
        }
        
        .flomo-web-guide .demo-text.selected {
          background: #007aff;
          color: white;
          border-color: #0070e0;
        }
        
        .flomo-web-guide .close-btn {
          position: absolute;
          top: 8px;
          right: 8px;
          background: none;
          border: none;
          font-size: 18px;
          color: #6e6e73;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: background 0.2s ease;
        }
        
        .flomo-web-guide .close-btn:hover {
          background: #f2f2f7;
        }
      </style>
      
      <button class="close-btn" data-action="close">×</button>
      
      <h3>
        <span>🎯</span>
        如何使用 Flomo 扩展
      </h3>
      
      <p>在任何网页上选择文本，然后右键选择"保存到 Flomo"即可使用扩展。</p>
      
      <div class="steps">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-text">选择下面的示例文本</div>
        </div>
        <div class="step">
          <div class="step-number">2</div>
          <div class="step-text">右键点击选中的文本</div>
        </div>
        <div class="step">
          <div class="step-number">3</div>
          <div class="step-text">选择"保存到 Flomo"</div>
        </div>
      </div>
      
      <div class="demo-text" id="demo-text">
        这是一段示例文本，您可以选择这段文字来体验 Flomo 扩展的功能。选择后右键点击，选择"保存到 Flomo"即可打开编辑界面。
      </div>
      
      <div class="buttons">
        <button class="btn-secondary" data-action="later">稍后提醒</button>
        <button class="btn-primary" data-action="got-it">知道了</button>
      </div>
    `;

    // 不在这里添加到DOM，由showWebPageGuide方法处理
    this.bindEvents();

    // 自动选择示例文本以演示
    setTimeout(() => {
      this.selectDemoText();
    }, 1000);
  }

  bindEvents() {
    const buttons = this.tooltip.querySelectorAll('button');
    buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        const action = e.target.dataset.action;
        switch (action) {
          case 'close':
          case 'got-it':
            this.completeGuide();
            break;
          case 'later':
            this.dismissGuide();
            break;
        }
      });
    });

    // 监听示例文本的选择
    const demoText = this.tooltip.querySelector('#demo-text');
    demoText.addEventListener('mouseup', () => {
      const selection = window.getSelection();
      if (selection.toString().length > 0) {
        demoText.classList.add('selected');
        this.showContextMenuHint();
      }
    });

    // 点击其他地方取消选择
    document.addEventListener('click', (e) => {
      if (!this.tooltip.contains(e.target)) {
        demoText.classList.remove('selected');
        this.hideContextMenuHint();
      }
    });
  }

  selectDemoText() {
    const demoText = this.tooltip.querySelector('#demo-text');
    const range = document.createRange();
    range.selectNodeContents(demoText);

    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);

    demoText.classList.add('selected');

    // 显示右键提示
    setTimeout(() => {
      this.showContextMenuHint();
    }, 500);
  }

  showContextMenuHint() {
    // 检查是否已经有提示
    if (document.querySelector('.context-menu-hint')) return;

    const hint = document.createElement('div');
    hint.className = 'context-menu-hint';
    hint.innerHTML = `
      <style>
        .context-menu-hint {
          position: fixed;
          background: #34c759;
          color: white;
          padding: 8px 12px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 500;
          z-index: 1000000;
          animation: bounce 0.5s ease;
          pointer-events: none;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
        }
        
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
          40% { transform: translateY(-10px); }
          60% { transform: translateY(-5px); }
        }
      </style>
      👆 现在右键点击选中的文本
    `;

    // 定位到选中文本附近
    const demoText = this.tooltip.querySelector('#demo-text');
    const rect = demoText.getBoundingClientRect();
    hint.style.top = `${rect.bottom + 10}px`;
    hint.style.left = `${rect.left + rect.width / 2 - 80}px`;

    document.body.appendChild(hint);

    // 5秒后自动移除
    setTimeout(() => {
      this.hideContextMenuHint();
    }, 5000);
  }

  hideContextMenuHint() {
    const hint = document.querySelector('.context-menu-hint');
    if (hint) {
      hint.remove();
    }
  }

  async completeGuide() {
    // 标记为已看过网页引导
    await chrome.storage.sync.set({
      hasSeenWebPageGuide: true,
      webPageGuideCompletedAt: Date.now()
    });

    this.cleanup();
  }

  async dismissGuide() {
    // 设置稍后提醒（24小时后）
    await chrome.storage.sync.set({
      webPageGuideRemindAt: Date.now() + 24 * 60 * 60 * 1000
    });

    this.cleanup();
  }

  cleanup() {
    this.hideContextMenuHint();

    if (this.tooltip) {
      this.tooltip.style.animation = 'fadeInScale 0.3s ease reverse';
      setTimeout(() => {
        if (this.tooltip) {
          this.tooltip.remove();
          this.tooltip = null;
        }
      }, 300);
    }

    if (this.overlay) {
      this.overlay.style.animation = 'fadeIn 0.3s ease reverse';
      setTimeout(() => {
        if (this.overlay) {
          this.overlay.remove();
          this.overlay = null;
        }
      }, 300);
    }

    this.isActive = false;
  }

  hideWebPageGuide() {
    this.cleanup();
  }

  // 检查是否需要显示稍后提醒
  async checkReminder() {
    const result = await chrome.storage.sync.get(['webPageGuideRemindAt', 'hasSeenWebPageGuide']);

    if (result.webPageGuideRemindAt && !result.hasSeenWebPageGuide) {
      const now = Date.now();
      if (now >= result.webPageGuideRemindAt) {
        // 清除提醒时间
        await chrome.storage.sync.remove('webPageGuideRemindAt');
        // 显示引导
        setTimeout(() => {
          this.showWebPageGuide();
        }, 2000);
      }
    }
  }
}

// 初始化内容引导
const contentOnboarding = new ContentOnboarding();

// 页面加载完成后检查提醒
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    contentOnboarding.checkReminder();
  });
} else {
  contentOnboarding.checkReminder();
}
