// 测试配置和工具
const TestConfig = {
  // 测试环境配置
  environment: {
    timeout: 5000, // 默认测试超时时间
    retries: 0, // 失败重试次数
    bail: false, // 是否在第一个失败后停止
    verbose: true // 详细输出
  },

  // 模拟数据
  mockData: {
    // Chrome API 模拟
    chrome: {
      storage: {
        sync: {
          validConfig: {
            flomoApiUrl: 'https://flomoapp.com/mine/api',
            aiProvider: 'siliconflow',
            aiApiKeys: {
              siliconflow: 'sk-test-key-123'
            },
            aiModels: {
              siliconflow: 'Qwen/Qwen2.5-7B-Instruct'
            }
          },
          invalidConfig: {
            flomoApiUrl: 'invalid-url',
            aiProvider: 'unknown-provider'
          },
          emptyConfig: {}
        }
      },
      runtime: {
        validResponse: {
          success: true,
          providers: [{
            id: 'siliconflow',
            name: '硅基流动',
            models: {
              'Qwen/Qwen2.5-7B-Instruct': 'Qwen2.5-7B-Instruct (免费)'
            }
          }]
        }
      }
    },

    // AI API 响应模拟
    aiResponses: {
      tags: {
        success: {
          choices: [{
            message: {
              content: '#技术 #编程 #JavaScript #前端开发'
            }
          }]
        },
        error401: {
          error: {
            message: 'Invalid API key',
            type: 'invalid_request_error'
          }
        }
      },
      summary: {
        success: {
          choices: [{
            message: {
              content: '这是一篇关于JavaScript编程技术的文章，主要介绍了现代前端开发的最佳实践和工具使用方法。'
            }
          }]
        }
      },
      format: {
        success: {
          choices: [{
            message: {
              content: '# 整理后的标题\n\n这是整理后的内容，格式清晰，结构合理。\n\n## 主要要点\n\n- 要点一：重要信息\n- 要点二：关键概念\n- 要点三：实践建议'
            }
          }]
        }
      },
      translate: {
        success: {
          choices: [{
            message: {
              content: '**中文：** 你好，世界！这是一个测试。\n**English:** Hello, World! This is a test.'
            }
          }]
        }
      }
    },

    // HTML 测试数据
    htmlSamples: {
      simple: '<p>简单段落</p>',
      complex: `
        <div>
          <h1>主标题</h1>
          <p>这是一个包含<strong>粗体</strong>和<em>斜体</em>的段落。</p>
          <ul>
            <li>列表项1</li>
            <li>列表项2</li>
          </ul>
          <blockquote>这是引用内容</blockquote>
          <a href="https://example.com">链接</a>
        </div>
      `,
      table: `
        <table>
          <thead>
            <tr><th>列1</th><th>列2</th></tr>
          </thead>
          <tbody>
            <tr><td>数据1</td><td>数据2</td></tr>
            <tr><td>数据3</td><td>数据4</td></tr>
          </tbody>
        </table>
      `,
      nested: `
        <div>
          <h2>嵌套内容</h2>
          <p>段落中包含<code>内联代码</code>和<a href="#">链接</a>。</p>
          <pre><code>代码块内容</code></pre>
          <ul>
            <li>项目1
              <ul>
                <li>子项目1</li>
                <li>子项目2</li>
              </ul>
            </li>
            <li>项目2</li>
          </ul>
        </div>
      `
    }
  },

  // 测试工具函数
  utils: {
    // 创建延迟
    delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

    // 创建随机字符串
    randomString: (length = 10) => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    },

    // 创建测试用的DOM元素
    createElement: (tag, attributes = {}, content = '') => {
      const element = document.createElement(tag);
      Object.keys(attributes).forEach(key => {
        element.setAttribute(key, attributes[key]);
      });
      if (content) {
        element.innerHTML = content;
      }
      return element;
    },

    // 清理DOM
    cleanupDOM: () => {
      // 清理测试创建的元素
      const testElements = document.querySelectorAll('[data-test]');
      testElements.forEach(el => el.remove());
      
      // 清理通知容器
      const notificationContainer = document.getElementById('notification-container');
      if (notificationContainer) {
        notificationContainer.remove();
      }
    },

    // 模拟网络请求
    mockFetch: (response, options = {}) => {
      const { delay = 0, shouldFail = false, status = 200 } = options;
      
      return () => new Promise((resolve, reject) => {
        setTimeout(() => {
          if (shouldFail) {
            reject(new Error(response.error || 'Network error'));
          } else {
            resolve({
              ok: status >= 200 && status < 300,
              status,
              statusText: status === 200 ? 'OK' : 'Error',
              json: () => Promise.resolve(response)
            });
          }
        }, delay);
      });
    },

    // 模拟Chrome API
    mockChromeAPI: (config = {}) => {
      return {
        storage: {
          sync: {
            get: testFramework.createSpy((keys) => {
              const data = config.storageData || TestConfig.mockData.chrome.storage.sync.validConfig;
              if (Array.isArray(keys)) {
                const result = {};
                keys.forEach(key => {
                  if (data[key] !== undefined) {
                    result[key] = data[key];
                  }
                });
                return Promise.resolve(result);
              } else if (typeof keys === 'string') {
                return Promise.resolve({ [keys]: data[keys] });
              } else {
                return Promise.resolve(data);
              }
            }),
            set: testFramework.createSpy(() => Promise.resolve()),
            remove: testFramework.createSpy(() => Promise.resolve()),
            clear: testFramework.createSpy(() => Promise.resolve())
          },
          local: {
            get: testFramework.createSpy(() => Promise.resolve({})),
            set: testFramework.createSpy(() => Promise.resolve()),
            remove: testFramework.createSpy(() => Promise.resolve()),
            clear: testFramework.createSpy(() => Promise.resolve())
          }
        },
        runtime: {
          sendMessage: testFramework.createSpy(() => 
            Promise.resolve(config.runtimeResponse || TestConfig.mockData.chrome.runtime.validResponse)
          ),
          openOptionsPage: testFramework.createSpy(() => {}),
          getURL: testFramework.createSpy((path) => `chrome-extension://test-id/${path}`)
        },
        notifications: {
          create: testFramework.createSpy(() => {}),
          clear: testFramework.createSpy(() => {})
        },
        tabs: {
          query: testFramework.createSpy(() => Promise.resolve([{ id: 1, active: true }])),
          create: testFramework.createSpy(() => Promise.resolve({ id: 2 })),
          sendMessage: testFramework.createSpy(() => Promise.resolve())
        }
      };
    },

    // 验证函数调用
    expectCalled: (spy, times = 1) => {
      expect(spy._calls).toHaveLength(times);
    },

    expectCalledWith: (spy, ...args) => {
      expect(spy._calls.some(call => 
        JSON.stringify(call) === JSON.stringify(args)
      )).toBe(true);
    },

    // 性能测试工具
    measurePerformance: async (fn, iterations = 1) => {
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        await fn();
        const end = performance.now();
        times.push(end - start);
      }
      
      return {
        min: Math.min(...times),
        max: Math.max(...times),
        avg: times.reduce((a, b) => a + b, 0) / times.length,
        total: times.reduce((a, b) => a + b, 0)
      };
    }
  },

  // 断言扩展
  assertions: {
    // 检查是否为有效的HTML
    toBeValidHTML: (html) => {
      try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        return doc.documentElement.innerHTML.includes(html.replace(/\s+/g, ' ').trim());
      } catch {
        return false;
      }
    },

    // 检查是否为有效的Markdown
    toBeValidMarkdown: (markdown) => {
      // 简单的Markdown格式检查
      const markdownPatterns = [
        /^#{1,6}\s+/, // 标题
        /\*\*.*\*\*/, // 粗体
        /\*.*\*/, // 斜体
        /\[.*\]\(.*\)/, // 链接
        /^-\s+/, // 列表
        /^>\s+/ // 引用
      ];
      
      return markdownPatterns.some(pattern => pattern.test(markdown));
    },

    // 检查是否为有效的URL
    toBeValidURL: (url) => {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    }
  }
};

// 扩展测试框架的断言
if (typeof testFramework !== 'undefined') {
  const originalExpect = testFramework.expect;
  testFramework.expect = function(actual) {
    const assertions = originalExpect(actual);
    
    // 添加自定义断言
    assertions.toBeValidHTML = () => {
      if (!TestConfig.assertions.toBeValidHTML(actual)) {
        throw new Error(`Expected "${actual}" to be valid HTML`);
      }
    };
    
    assertions.toBeValidMarkdown = () => {
      if (!TestConfig.assertions.toBeValidMarkdown(actual)) {
        throw new Error(`Expected "${actual}" to be valid Markdown`);
      }
    };
    
    assertions.toBeValidURL = () => {
      if (!TestConfig.assertions.toBeValidURL(actual)) {
        throw new Error(`Expected "${actual}" to be a valid URL`);
      }
    };
    
    return assertions;
  };
}

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TestConfig;
} else if (typeof window !== 'undefined') {
  window.TestConfig = TestConfig;
}
