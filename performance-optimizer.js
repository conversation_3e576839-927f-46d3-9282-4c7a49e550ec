// 性能优化管理器
class PerformanceOptimizer {
  constructor() {
    this.cache = new Map();
    this.debounceTimers = new Map();
    this.requestQueue = new Map();
    this.memoryMonitor = new MemoryMonitor();
    this.performanceMetrics = new PerformanceMetrics();

    // 使用性能配置
    this.config = PerformanceConfig ? PerformanceConfig.getCurrentConfig() : {
      cacheExpiry: 30 * 60 * 1000,
      maxCacheSize: 100,
      debounceDelay: 300,
      maxConcurrentRequests: 3,
      memoryThreshold: 50 * 1024 * 1024
    };

    this.init();
  }

  init() {
    // 定期清理过期缓存
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 5 * 60 * 1000); // 每5分钟清理一次

    // 监听内存使用情况
    this.memoryMonitor.start();
  }

  // AI请求结果缓存
  getCachedResult(key, params) {
    const cacheKey = this.generateCacheKey(key, params);
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.config.cacheExpiry) {
      console.log('🎯 缓存命中:', cacheKey);
      return cached.data;
    }

    if (cached) {
      // 过期缓存，删除
      this.cache.delete(cacheKey);
    }

    return null;
  }

  setCachedResult(key, params, data) {
    const cacheKey = this.generateCacheKey(key, params);

    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxCacheSize) {
      this.evictOldestCache();
    }

    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      accessCount: 1
    });

    console.log('💾 缓存保存:', cacheKey);
  }

  generateCacheKey(key, params) {
    // 生成基于内容和参数的缓存键
    const content = params.content || '';
    const options = JSON.stringify(params.options || {});
    const contentHash = this.simpleHash(content);
    return `${key}_${contentHash}_${this.simpleHash(options)}`;
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  evictOldestCache() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, value] of this.cache) {
      if (value.timestamp < oldestTime) {
        oldestTime = value.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log('🗑️ 清理最旧缓存:', oldestKey);
    }
  }

  cleanupExpiredCache() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, value] of this.cache) {
      if (now - value.timestamp > this.config.cacheExpiry) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));

    if (expiredKeys.length > 0) {
      console.log(`🧹 清理过期缓存: ${expiredKeys.length} 条`);
    }
  }

  // 防抖处理
  debounce(key, fn, delay = this.config.debounceDelay) {
    return (...args) => {
      // 清除之前的定时器
      if (this.debounceTimers.has(key)) {
        clearTimeout(this.debounceTimers.get(key));
      }

      // 设置新的定时器
      const timer = setTimeout(() => {
        this.debounceTimers.delete(key);
        fn.apply(this, args);
      }, delay);

      this.debounceTimers.set(key, timer);
    };
  }

  // 请求队列管理
  async queueRequest(key, requestFn) {
    // 检查是否已有相同请求在进行
    if (this.requestQueue.has(key)) {
      console.log('⏳ 等待现有请求完成:', key);
      return await this.requestQueue.get(key);
    }

    // 检查并发请求数量
    if (this.requestQueue.size >= this.config.maxConcurrentRequests) {
      console.log('🚦 请求队列已满，等待...');
      await this.waitForQueueSpace();
    }

    // 创建请求Promise
    const requestPromise = this.executeRequest(requestFn);
    this.requestQueue.set(key, requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      this.requestQueue.delete(key);
    }
  }

  async executeRequest(requestFn) {
    const startTime = performance.now();
    try {
      const result = await requestFn();
      const duration = performance.now() - startTime;
      console.log(`⚡ 请求完成，耗时: ${duration.toFixed(2)}ms`);
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      console.log(`❌ 请求失败，耗时: ${duration.toFixed(2)}ms`);
      throw error;
    }
  }

  async waitForQueueSpace() {
    return new Promise((resolve) => {
      const checkQueue = () => {
        if (this.requestQueue.size < this.config.maxConcurrentRequests) {
          resolve();
        } else {
          setTimeout(checkQueue, 100);
        }
      };
      checkQueue();
    });
  }

  // 批量处理优化
  createBatchProcessor(processFn, batchSize = 5, delay = 100) {
    let batch = [];
    let timer = null;

    return (item) => {
      batch.push(item);

      if (batch.length >= batchSize) {
        this.processBatch(batch, processFn);
        batch = [];
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
      } else {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          if (batch.length > 0) {
            this.processBatch(batch, processFn);
            batch = [];
          }
          timer = null;
        }, delay);
      }
    };
  }

  async processBatch(items, processFn) {
    try {
      console.log(`📦 批量处理 ${items.length} 个项目`);
      await processFn(items);
    } catch (error) {
      console.error('批量处理失败:', error);
    }
  }

  // 获取性能统计
  getPerformanceStats() {
    return {
      cache: {
        size: this.cache.size,
        maxSize: this.config.maxCacheSize,
        hitRate: this.calculateCacheHitRate()
      },
      requests: {
        active: this.requestQueue.size,
        maxConcurrent: this.config.maxConcurrentRequests
      },
      memory: this.memoryMonitor.getStats(),
      debounce: {
        activeTimers: this.debounceTimers.size
      }
    };
  }

  calculateCacheHitRate() {
    let totalAccess = 0;
    let totalHits = 0;

    for (const [key, value] of this.cache) {
      totalAccess += value.accessCount;
      totalHits += value.accessCount - 1; // 第一次是miss，后续是hit
    }

    return totalAccess > 0 ? (totalHits / totalAccess * 100).toFixed(2) : 0;
  }

  // 清理所有缓存和定时器
  cleanup() {
    this.cache.clear();

    for (const timer of this.debounceTimers.values()) {
      clearTimeout(timer);
    }
    this.debounceTimers.clear();

    this.memoryMonitor.stop();

    console.log('🧹 性能优化器已清理');
  }
}

// 内存监控器
class MemoryMonitor {
  constructor() {
    this.stats = {
      peak: 0,
      current: 0,
      samples: []
    };
    this.monitoring = false;
    this.interval = null;
  }

  start() {
    if (this.monitoring) return;

    this.monitoring = true;
    this.interval = setInterval(() => {
      this.collectMemoryStats();
    }, 10000); // 每10秒采样一次

    console.log('📊 内存监控已启动');
  }

  stop() {
    if (!this.monitoring) return;

    this.monitoring = false;
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }

    console.log('📊 内存监控已停止');
  }

  collectMemoryStats() {
    if (typeof performance !== 'undefined' && performance.memory) {
      const memory = performance.memory;
      const current = memory.usedJSHeapSize;

      this.stats.current = current;
      this.stats.peak = Math.max(this.stats.peak, current);

      // 保持最近100个样本
      this.stats.samples.push({
        timestamp: Date.now(),
        used: current,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      });

      if (this.stats.samples.length > 100) {
        this.stats.samples.shift();
      }

      // 检查内存使用警告
      if (current > 50 * 1024 * 1024) { // 50MB
        console.warn('⚠️ 内存使用较高:', this.formatBytes(current));
      }
    }
  }

  getStats() {
    return {
      current: this.formatBytes(this.stats.current),
      peak: this.formatBytes(this.stats.peak),
      samples: this.stats.samples.length,
      trend: this.calculateTrend()
    };
  }

  calculateTrend() {
    if (this.stats.samples.length < 2) return 'stable';

    const recent = this.stats.samples.slice(-10);
    const first = recent[0].used;
    const last = recent[recent.length - 1].used;
    const change = (last - first) / first;

    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 性能指标收集器
class PerformanceMetrics {
  constructor() {
    this.metrics = {
      responseTime: [],
      cacheHitRate: [],
      memoryUsage: [],
      requestCount: 0,
      errorCount: 0
    };
    this.startTime = Date.now();
  }

  recordResponseTime(duration) {
    this.metrics.responseTime.push({
      timestamp: Date.now(),
      duration
    });

    // 保持最近100条记录
    if (this.metrics.responseTime.length > 100) {
      this.metrics.responseTime.shift();
    }
  }

  recordCacheHit(hit) {
    this.metrics.cacheHitRate.push({
      timestamp: Date.now(),
      hit
    });

    if (this.metrics.cacheHitRate.length > 100) {
      this.metrics.cacheHitRate.shift();
    }
  }

  recordMemoryUsage(usage) {
    this.metrics.memoryUsage.push({
      timestamp: Date.now(),
      usage
    });

    if (this.metrics.memoryUsage.length > 100) {
      this.metrics.memoryUsage.shift();
    }
  }

  incrementRequestCount() {
    this.metrics.requestCount++;
  }

  incrementErrorCount() {
    this.metrics.errorCount++;
  }

  getAverageResponseTime() {
    if (this.metrics.responseTime.length === 0) return 0;

    const total = this.metrics.responseTime.reduce((sum, item) => sum + item.duration, 0);
    return total / this.metrics.responseTime.length;
  }

  getCacheHitRate() {
    if (this.metrics.cacheHitRate.length === 0) return 0;

    const hits = this.metrics.cacheHitRate.filter(item => item.hit).length;
    return (hits / this.metrics.cacheHitRate.length * 100).toFixed(2);
  }

  getMetricsSummary() {
    return {
      uptime: Date.now() - this.startTime,
      averageResponseTime: this.getAverageResponseTime(),
      cacheHitRate: this.getCacheHitRate(),
      requestCount: this.metrics.requestCount,
      errorCount: this.metrics.errorCount,
      errorRate: this.metrics.requestCount > 0 ?
        (this.metrics.errorCount / this.metrics.requestCount * 100).toFixed(2) : 0
    };
  }
}

// 创建全局性能优化器实例
const performanceOptimizer = new PerformanceOptimizer();

// 添加到全局作用域
if (typeof window !== 'undefined') {
  window.performanceOptimizer = performanceOptimizer;
}
