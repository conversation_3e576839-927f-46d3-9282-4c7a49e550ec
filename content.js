
// 内容脚本 - 在网页上下文中运行
// 提供页面信息获取和内容处理功能

// 获取当前选中的文本内容（包含格式信息）
function getSelectedContent() {
  const selection = window.getSelection();
  if (selection.rangeCount === 0) {
    return null;
  }

  const range = selection.getRangeAt(0);
  const selectedText = selection.toString().trim();

  if (!selectedText) {
    return null;
  }

  // 获取选中内容的HTML（保留完整格式）
  const container = document.createElement('div');
  const clonedContent = range.cloneContents();
  container.appendChild(clonedContent);

  // 清理和标准化HTML
  const cleanedHtml = cleanupHtml(container);
  const selectedHtml = cleanedHtml.innerHTML;

  // 获取选中内容周围的上下文
  const contextBefore = getContextText(range.startContainer, range.startOffset, true);
  const contextAfter = getContextText(range.endContainer, range.endOffset, false);

  // 检测内容类型和格式
  const contentAnalysis = analyzeContent(cleanedHtml, selectedText);

  return {
    text: selectedText,
    html: selectedHtml,
    contextBefore: contextBefore,
    contextAfter: contextAfter,
    length: selectedText.length,
    hasFormatting: contentAnalysis.hasFormatting,
    contentType: contentAnalysis.contentType,
    formatElements: contentAnalysis.formatElements
  };
}

// 清理和标准化HTML内容
function cleanupHtml(container) {
  // 移除不必要的属性和样式
  const elementsToClean = container.querySelectorAll('*');
  elementsToClean.forEach(element => {
    // 保留重要的格式属性
    const allowedAttributes = ['href', 'src', 'alt', 'title'];
    const attributes = Array.from(element.attributes);

    attributes.forEach(attr => {
      if (!allowedAttributes.includes(attr.name)) {
        element.removeAttribute(attr.name);
      }
    });

    // 标准化标签名
    const tagName = element.tagName.toLowerCase();
    if (tagName === 'b') {
      const strong = document.createElement('strong');
      strong.innerHTML = element.innerHTML;
      element.parentNode.replaceChild(strong, element);
    } else if (tagName === 'i') {
      const em = document.createElement('em');
      em.innerHTML = element.innerHTML;
      element.parentNode.replaceChild(em, element);
    }
  });

  return container;
}

// 分析内容格式和类型
function analyzeContent(htmlContent, textContent) {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;

  const formatElements = {
    bold: tempDiv.querySelectorAll('strong, b').length > 0,
    italic: tempDiv.querySelectorAll('em, i').length > 0,
    links: tempDiv.querySelectorAll('a').length > 0,
    lists: tempDiv.querySelectorAll('ul, ol, li').length > 0,
    headings: tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6').length > 0,
    code: tempDiv.querySelectorAll('code, pre').length > 0,
    blockquotes: tempDiv.querySelectorAll('blockquote').length > 0,
    images: tempDiv.querySelectorAll('img').length > 0,
    tables: tempDiv.querySelectorAll('table, tr, td, th').length > 0
  };

  const hasFormatting = Object.values(formatElements).some(hasElement => hasElement);

  // 确定内容类型
  let contentType = 'text';
  if (formatElements.code) contentType = 'code';
  else if (formatElements.tables) contentType = 'table';
  else if (formatElements.lists) contentType = 'list';
  else if (formatElements.headings) contentType = 'article';
  else if (hasFormatting) contentType = 'formatted';

  return {
    hasFormatting,
    contentType,
    formatElements
  };
}

// 获取上下文文本
function getContextText(node, offset, isBefore) {
  try {
    let textNode = node;
    if (node.nodeType !== Node.TEXT_NODE) {
      textNode = isBefore ? node.previousSibling : node.nextSibling;
      if (!textNode || textNode.nodeType !== Node.TEXT_NODE) {
        return '';
      }
    }

    const text = textNode.textContent;
    if (isBefore) {
      return text.substring(Math.max(0, offset - 50), offset);
    } else {
      return text.substring(offset, Math.min(text.length, offset + 50));
    }
  } catch (error) {
    return '';
  }
}

// 获取页面元数据
function getPageMetadata() {
  // 获取页面标题
  const title = document.title;

  // 获取页面URL
  const url = window.location.href;

  // 获取页面描述
  const metaDescription = document.querySelector('meta[name="description"]');
  const description = metaDescription ? metaDescription.content : '';

  // 获取页面关键词
  const metaKeywords = document.querySelector('meta[name="keywords"]');
  const keywords = metaKeywords ? metaKeywords.content : '';

  // 获取作者信息
  const metaAuthor = document.querySelector('meta[name="author"]');
  const author = metaAuthor ? metaAuthor.content : '';

  // 获取发布时间
  const metaPublished = document.querySelector('meta[property="article:published_time"]') ||
                       document.querySelector('meta[name="date"]');
  const publishedTime = metaPublished ? metaPublished.content : '';

  return {
    title,
    url,
    description,
    keywords,
    author,
    publishedTime,
    domain: window.location.hostname,
    timestamp: new Date().toISOString()
  };
}

// 监听来自扩展的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'getSelectedContent') {
    const selectedContent = getSelectedContent();
    const pageMetadata = getPageMetadata();

    sendResponse({
      success: true,
      data: {
        content: selectedContent,
        metadata: pageMetadata
      }
    });
  }

  return true; // 保持消息通道开放
});

// 页面加载完成后的初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initialize);
} else {
  initialize();
}

function initialize() {
  // 可以在这里添加页面初始化逻辑
  console.log('Flomo 扩展内容脚本已加载');
}
