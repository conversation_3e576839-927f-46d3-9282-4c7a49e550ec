// 侧边栏JavaScript功能
class FlomoSidePanel {
  constructor() {
    this.contentData = null;
    this.isLoading = false;
    this.currentFormat = 'auto'; // auto, plain, markdown
    this.aiResults = {};
    this.addedTags = new Set(); // 跟踪已添加的标签
    this.init();
  }

  async init() {
    this.bindEvents();
    await this.loadPendingContent();
  }

  bindEvents() {
    // 保存按钮事件
    document.getElementById('save-btn').addEventListener('click', () => {
      this.saveContent();
    });

    // 取消按钮事件
    document.getElementById('cancel-btn').addEventListener('click', () => {
      this.cancelEdit();
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        if (e.key === 's') {
          e.preventDefault();
          this.saveContent();
        } else if (e.key === 'Escape') {
          e.preventDefault();
          this.cancelEdit();
        }
      }
    });
  }

  // 绑定动态事件（在内容渲染后调用）
  bindDynamicEvents() {
    // 文本区域变化事件
    const textarea = document.getElementById('content-textarea');
    if (textarea) {
      textarea.addEventListener('input', () => {
        this.updateCharCount();
        this.saveToLocalStorage();
      });
    }

    // 格式选择事件
    const formatOptions = document.querySelectorAll('.format-option');
    formatOptions.forEach(option => {
      option.addEventListener('click', () => {
        this.selectFormat(option.dataset.format);
      });
    });

    // AI功能事件
    const aiFunctions = document.querySelectorAll('.ai-function');
    aiFunctions.forEach(func => {
      func.addEventListener('click', () => {
        this.processAIFunction(func.dataset.function);
      });
    });

    // 流式输出开关事件
    const streamingToggle = document.getElementById('streaming-enabled');
    if (streamingToggle) {
      streamingToggle.addEventListener('change', (e) => {
        this.setStreamingEnabled(e.target.checked);
      });

      // 初始化开关状态
      this.initStreamingToggle();
    }

    // 取消流式输出按钮事件
    const cancelStreamingBtn = document.getElementById('cancel-streaming');
    if (cancelStreamingBtn) {
      cancelStreamingBtn.addEventListener('click', () => {
        this.cancelStreaming();
      });
    }

    // AI结果应用事件
    document.addEventListener('click', (e) => {
      // 检查是否点击了AI应用按钮或其子元素
      const applyBtn = e.target.closest('.ai-apply-btn');
      if (applyBtn) {
        console.log('🔘 点击了AI应用按钮', { type: applyBtn.dataset.type, content: applyBtn.dataset.content });
        this.applyAIResult(applyBtn.dataset.type, applyBtn.dataset.content);
        return;
      }

      // 单个标签点击事件
      if (e.target.classList.contains('ai-tag') && !e.target.classList.contains('added')) {
        const tagName = e.target.dataset.tag;
        if (tagName) {
          this.addSingleTag(tagName);
        }
        return;
      }

      // 标签删除事件（双击或点击删除按钮）
      if (e.target.classList.contains('ai-tag') && e.target.classList.contains('added')) {
        const tagName = e.target.dataset.tag;
        if (tagName) {
          this.removeSingleTag(tagName);
        }
        return;
      }
    });
  }

  async loadPendingContent() {
    try {
      // 从本地存储获取待处理的内容
      const result = await chrome.storage.local.get('pendingContent');

      if (result.pendingContent) {
        // 检查内容是否过期（超过1小时）
        const now = Date.now();
        const oneHour = 60 * 60 * 1000;

        if (now - result.pendingContent.timestamp > oneHour) {
          // 内容已过期，清理并显示空状态
          await chrome.storage.local.remove('pendingContent');
          this.showEmptyState();
          this.showError('内容已过期，请重新选择文本。');
          return;
        }

        // 检查是否需要配置API
        if (result.pendingContent.needsApiConfig) {
          this.showApiConfigNeeded(result.pendingContent.selectedText);
          return;
        }

        // 检查是否有错误
        if (result.pendingContent.error) {
          this.showError(result.pendingContent.error);
          this.contentData = result.pendingContent;
          this.renderContent();
          return;
        }

        this.contentData = result.pendingContent;
        this.renderContent();
      } else {
        this.showEmptyState();
      }
    } catch (error) {
      // 使用统一错误处理器
      const errorResult = errorHandler.handleError(error, {
        operation: 'loadPendingContent'
      }, { showToUser: false });

      this.showError('加载内容失败，请重试。');
      this.showEmptyState();
    }
  }

  renderContent() {
    const mainContent = document.getElementById('main-content');

    if (!this.contentData) {
      this.showEmptyState();
      return;
    }

    const { selectedText, pageTitle, pageUrl, timestamp, html, hasFormatting } = this.contentData;

    // 创建元数据卡片
    const metadataCard = this.createMetadataCard(pageTitle, pageUrl, timestamp);

    // 创建格式选择区域
    const formatSection = this.createFormatSection(hasFormatting, html, selectedText);

    // 创建AI功能区域
    const aiSection = this.createAISection();

    // 创建编辑区域
    const editSection = this.createEditSection(selectedText);

    mainContent.innerHTML = '';
    mainContent.appendChild(metadataCard);
    mainContent.appendChild(formatSection);
    mainContent.appendChild(aiSection);
    mainContent.appendChild(editSection);

    // 绑定动态事件
    this.bindDynamicEvents();

    // 更新字符计数
    this.updateCharCount();

    // 聚焦到文本区域并初始化标签状态
    setTimeout(() => {
      const textarea = document.getElementById('content-textarea');
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(textarea.value.length, textarea.value.length);
        // 初始化标签状态
        this.initializeTagStates();
      }
    }, 100);
  }

  createMetadataCard(pageTitle, pageUrl, timestamp) {
    const card = document.createElement('div');
    card.className = 'metadata-card';

    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    card.innerHTML = `
      <div class="metadata-title">页面信息</div>
      <div class="metadata-item">
        <div class="metadata-label">标题</div>
        <div class="metadata-value">${this.escapeHtml(pageTitle)}</div>
      </div>
      <div class="metadata-item">
        <div class="metadata-label">网址</div>
        <a href="${pageUrl}" target="_blank" class="metadata-value url">${this.escapeHtml(pageUrl)}</a>
      </div>
      <div class="metadata-item">
        <div class="metadata-label">选择时间</div>
        <div class="metadata-value">${formatDate(timestamp)}</div>
      </div>
    `;

    return card;
  }

  createFormatSection(hasFormatting, html, selectedText) {
    const section = document.createElement('div');
    section.className = 'format-section';

    let formatPreview = '';
    if (hasFormatting && html) {
      const preview = htmlToMarkdown.getFormattingPreview(html);
      formatPreview = `检测到格式：${preview.summary}`;
    } else {
      formatPreview = '纯文本内容，无特殊格式';
    }

    section.innerHTML = `
      <div class="format-title">格式选择</div>
      <div class="format-options">
        <div class="format-option ${this.currentFormat === 'auto' ? 'active' : ''}" data-format="auto">
          智能选择
        </div>
        <div class="format-option ${this.currentFormat === 'markdown' ? 'active' : ''}" data-format="markdown">
          保留格式
        </div>
        <div class="format-option ${this.currentFormat === 'plain' ? 'active' : ''}" data-format="plain">
          纯文本
        </div>
      </div>
      <div class="format-preview">${formatPreview}</div>
    `;

    return section;
  }

  createAISection() {
    const section = document.createElement('div');
    section.className = 'ai-section';

    const functions = aiFunctions.getFunctionDescriptions();
    const functionButtons = Object.keys(functions).map(key => {
      const func = functions[key];
      return `
        <div class="ai-function" data-function="${key}">
          <div class="ai-function-icon">${func.icon}</div>
          <div class="ai-function-name">${func.name}</div>
        </div>
      `;
    }).join('');

    section.innerHTML = `
      <div class="ai-title">
        🤖 AI智能处理
        <div class="ai-controls">
          <label class="streaming-toggle">
            <input type="checkbox" id="streaming-enabled" checked>
            <span class="toggle-slider"></span>
            <span class="toggle-label">流式输出</span>
          </label>
        </div>
      </div>
      <div class="ai-functions">
        ${functionButtons}
      </div>
      <div class="ai-progress" id="ai-progress">
        <div class="loading-spinner"></div>
        <div>AI处理中...</div>
        <button class="cancel-streaming-btn" id="cancel-streaming" style="display: none;">取消</button>
      </div>
      <div class="ai-results" id="ai-results"></div>
    `;

    return section;
  }

  createEditSection(selectedText) {
    const section = document.createElement('div');
    section.className = 'edit-section';

    section.innerHTML = `
      <div class="edit-title">编辑内容</div>
      <div class="textarea-container">
        <textarea id="content-textarea" placeholder="在这里编辑要保存到 Flomo 的内容...">${this.escapeHtml(selectedText)}</textarea>
        <div class="char-count" id="char-count">0 字符</div>
      </div>
    `;

    return section;
  }

  showEmptyState() {
    const mainContent = document.getElementById('main-content');
    mainContent.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">📝</div>
        <div class="empty-state-title">没有待保存的内容</div>
        <div class="empty-state-description">
          请在网页上选择文本，然后右键选择"保存到 Flomo"
        </div>
      </div>
    `;
  }

  showApiConfigNeeded(selectedText) {
    const mainContent = document.getElementById('main-content');
    mainContent.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">⚙️</div>
        <div class="empty-state-title">需要配置 API 地址</div>
        <div class="empty-state-description">
          请先配置您的 Flomo API 地址才能保存内容
        </div>
        <button id="open-settings-btn" class="btn btn-primary" style="margin-top: 16px; max-width: 200px;">
          打开设置页面
        </button>
      </div>
    `;

    // 如果有选中的文本，临时存储它
    if (selectedText) {
      this.contentData = {
        selectedText: selectedText,
        pageTitle: '未知页面',
        pageUrl: '未知地址',
        timestamp: Date.now()
      };
    }

    // 绑定设置按钮事件
    document.getElementById('open-settings-btn').addEventListener('click', () => {
      chrome.runtime.openOptionsPage();
    });

    this.showError('请先在设置中配置您的 Flomo API 地址');
  }

  updateCharCount() {
    const textarea = document.getElementById('content-textarea');
    const charCount = document.getElementById('char-count');

    if (textarea && charCount) {
      const count = textarea.value.length;
      charCount.textContent = `${count} 字符`;
    }
  }

  async saveToLocalStorage() {
    const textarea = document.getElementById('content-textarea');
    if (textarea && this.contentData) {
      this.contentData.editedText = textarea.value;
      try {
        await chrome.storage.local.set({ 'pendingContent': this.contentData });
      } catch (error) {
        console.error('Error saving to local storage:', error);
      }
    }
  }

  async saveContent() {
    if (this.isLoading) return;

    const textarea = document.getElementById('content-textarea');
    const content = textarea ? textarea.value.trim() : '';

    // 使用输入验证器进行内容验证
    try {
      if (!content) {
        throw new Error('请输入要保存的内容');
      }

      // 验证和清理内容
      const validatedContent = inputValidator.sanitizeInput(content, 'content');

      // 如果内容被清理过，更新文本区域
      if (validatedContent !== content) {
        textarea.value = validatedContent;
        this.updateCharCount();
        this.saveToLocalStorage();
      }
    } catch (error) {
      this.showError(error.message);
      textarea?.focus();
      return;
    }

    // 检查API配置
    try {
      const apiConfig = await chrome.storage.sync.get('flomoApiUrl');
      if (!apiConfig.flomoApiUrl) {
        this.showError('请先配置 Flomo API 地址');
        // 打开设置页面
        setTimeout(() => {
          chrome.runtime.openOptionsPage();
        }, 2000);
        return;
      }
    } catch (error) {
      this.showError('无法检查API配置，请重试');
      return;
    }

    this.setLoading(true);

    try {
      // 构建要保存的完整内容
      const fullContent = this.buildFullContent(content);

      // 发送消息给后台脚本保存内容
      const response = await chrome.runtime.sendMessage({
        action: 'saveToFlomo',
        content: fullContent
      });

      if (response && response.success) {
        this.showSuccess('内容已成功保存到 Flomo！');

        // 清理临时数据
        await chrome.storage.local.remove('pendingContent');

        // 延迟关闭侧边栏
        setTimeout(() => {
          window.close();
        }, 2000);
      } else {
        const errorMessage = response?.error || '保存失败，请重试';
        throw new Error(errorMessage);
      }
    } catch (error) {
      // 使用统一错误处理器
      const errorResult = errorHandler.handleError(error, {
        operation: 'saveContent',
        contentLength: content?.length
      }, { showToUser: false });

      // 显示用户友好的错误消息
      this.showError(`保存失败：${errorResult.error.message || '未知错误'}`);
    } finally {
      this.setLoading(false);
    }
  }

  buildFullContent(editedContent) {
    if (!this.contentData) return editedContent;

    const { pageTitle, pageUrl } = this.contentData;

    // 构建包含来源信息的完整内容
    let fullContent = editedContent;

    // 如果内容不包含URL，添加来源信息
    if (!editedContent.includes(pageUrl)) {
      fullContent += `\n\n来源：${pageTitle}\n${pageUrl}`;
    }

    return fullContent;
  }

  async cancelEdit() {
    try {
      // 清理临时数据
      await chrome.storage.local.remove('pendingContent');

      // 关闭侧边栏
      window.close();
    } catch (error) {
      console.error('Error canceling edit:', error);
      window.close();
    }
  }

  setLoading(loading) {
    this.isLoading = loading;
    const saveBtn = document.getElementById('save-btn');
    const btnText = saveBtn.querySelector('.btn-text');
    const spinner = saveBtn.querySelector('.loading-spinner');

    if (loading) {
      saveBtn.disabled = true;
      btnText.textContent = '保存中...';
      spinner.style.display = 'block';
    } else {
      saveBtn.disabled = false;
      btnText.textContent = '保存到 Flomo';
      spinner.style.display = 'none';
    }
  }

  showSuccess(message) {
    this.showMessage(message, 'success');
  }

  showError(message) {
    this.showMessage(message, 'error');
  }

  showMessage(message, type) {
    const statusElement = document.getElementById('status-message');
    statusElement.textContent = message;
    statusElement.className = `status-message ${type}`;
    statusElement.style.display = 'block';

    // 自动隐藏消息
    setTimeout(() => {
      statusElement.style.display = 'none';
    }, 5000);
  }

  // 选择格式
  selectFormat(format) {
    this.currentFormat = format;

    // 更新UI
    document.querySelectorAll('.format-option').forEach(option => {
      option.classList.toggle('active', option.dataset.format === format);
    });

    // 更新内容
    this.updateContentByFormat();
  }

  // 根据格式更新内容
  updateContentByFormat() {
    const textarea = document.getElementById('content-textarea');
    if (!textarea || !this.contentData) return;

    let content = '';
    const { selectedText, html, hasFormatting } = this.contentData;

    switch (this.currentFormat) {
      case 'plain':
        content = selectedText;
        break;
      case 'markdown':
        if (hasFormatting && html) {
          content = htmlToMarkdown.convert(html);
        } else {
          content = selectedText;
        }
        break;
      case 'auto':
      default:
        if (hasFormatting && html) {
          content = htmlToMarkdown.convert(html);
        } else {
          content = selectedText;
        }
        break;
    }

    textarea.value = content;
    this.updateCharCount();
    this.saveToLocalStorage();
  }

  // 处理AI功能
  async processAIFunction(functionName) {
    console.log('🤖 开始处理AI功能', { functionName });

    const textarea = document.getElementById('content-textarea');
    if (!textarea) {
      console.error('❌ 找不到文本区域元素');
      return;
    }

    const content = textarea.value.trim();
    console.log('📝 处理内容', {
      contentLength: content.length,
      hasContent: !!content
    });

    // 使用输入验证器验证AI处理内容
    try {
      if (!content) {
        throw new Error('请先输入内容');
      }

      // 验证内容长度和安全性
      const validatedContent = inputValidator.sanitizeInput(content, 'content');

      // 检查内容长度（AI处理的特殊要求）
      if (validatedContent.length < 10) {
        throw new Error('内容太短，请输入至少10个字符');
      }

      // 如果内容被清理过，更新文本区域
      if (validatedContent !== content) {
        textarea.value = validatedContent;
        this.updateCharCount();
        this.saveToLocalStorage();
      }
    } catch (error) {
      console.warn('⚠️ 内容验证失败:', error.message);
      this.showError(error.message);
      return;
    }

    // 检查AI配置
    try {
      console.log('🔧 检查AI配置...');
      const config = await chrome.storage.sync.get(['aiProvider', 'aiModel', 'aiApiKeys']);
      console.log('📦 获取到的配置', {
        aiProvider: config.aiProvider,
        aiModel: config.aiModel,
        hasApiKeys: !!config.aiApiKeys,
        apiKeysKeys: config.aiApiKeys ? Object.keys(config.aiApiKeys) : []
      });

      if (!config.aiProvider) {
        console.error('❌ 未配置AI服务提供商');
        this.showError('请先在设置中配置AI服务提供商');
        setTimeout(() => {
          chrome.runtime.openOptionsPage();
        }, 2000);
        return;
      }

      const apiKeys = config.aiApiKeys || {};
      if (!apiKeys[config.aiProvider]) {
        console.error('❌ 未配置API密钥:', config.aiProvider);
        this.showError(`请先在设置中配置 ${config.aiProvider} 的API密钥`);
        setTimeout(() => {
          chrome.runtime.openOptionsPage();
        }, 2000);
        return;
      }

      console.log('✅ AI配置检查通过');
    } catch (error) {
      console.error('❌ 获取AI配置失败:', error);
      this.showError('无法获取AI配置，请重试');
      return;
    }

    // 显示处理状态
    await this.showAIProgress(functionName);

    try {
      console.log(`🚀 开始执行AI功能: ${functionName}`);

      // 检查是否启用流式输出 (可以通过设置或用户偏好控制)
      const useStreaming = await this.shouldUseStreaming(functionName);
      console.log(`🌊 流式输出设置: ${useStreaming}`);

      if (useStreaming) {
        // 执行流式AI功能
        await this.processStreamingAIFunction(functionName, content);
      } else {
        // 执行传统AI功能
        await this.processNormalAIFunction(functionName, content);
      }
    } catch (error) {
      // 使用统一错误处理器
      const errorResult = errorHandler.handleError(error, {
        operation: 'processAIFunction',
        functionName,
        contentLength: content?.length
      }, { showToUser: false });

      // 显示用户友好的错误消息
      this.showError(`AI处理失败：${errorResult.error.message || '未知错误'}`);
    } finally {
      this.hideAIProgress();
      console.log('🏁 AI功能处理结束');
    }
  }

  // 初始化流式输出开关状态
  async initStreamingToggle() {
    try {
      const result = await chrome.storage.sync.get('streamingEnabled');
      const streamingEnabled = result.streamingEnabled !== false; // 默认启用

      const toggle = document.getElementById('streaming-enabled');
      if (toggle) {
        toggle.checked = streamingEnabled;
      }
    } catch (error) {
      console.warn('⚠️ 初始化流式开关状态失败:', error);
    }
  }

  // 设置流式输出开关状态
  async setStreamingEnabled(enabled) {
    try {
      await chrome.storage.sync.set({ streamingEnabled: enabled });
      console.log(`🌊 流式输出已${enabled ? '启用' : '禁用'}`);

      // 显示状态提示
      this.showSuccess(`流式输出已${enabled ? '启用' : '禁用'}`);
    } catch (error) {
      console.error('❌ 保存流式设置失败:', error);
      this.showError('保存设置失败');
    }
  }

  // 检查是否应该使用流式输出
  async shouldUseStreaming(functionName) {
    try {
      // 从UI开关获取当前状态
      const toggle = document.getElementById('streaming-enabled');
      const streamingEnabled = toggle ? toggle.checked : true;

      // 某些功能可能更适合流式输出
      const streamingFriendlyFunctions = ['summary', 'format', 'translate'];
      const isFunctionStreamingFriendly = streamingFriendlyFunctions.includes(functionName);

      return streamingEnabled && isFunctionStreamingFriendly;
    } catch (error) {
      console.warn('⚠️ 获取流式设置失败，使用默认设置:', error);
      return true; // 默认启用流式输出
    }
  }

  // 取消流式输出
  cancelStreaming() {
    console.log('🛑 用户取消流式输出');

    // 设置取消标志
    this.streamingCancelled = true;

    // 隐藏取消按钮
    const cancelBtn = document.getElementById('cancel-streaming');
    if (cancelBtn) {
      cancelBtn.style.display = 'none';
    }

    // 清理流式结果
    this.cleanupStreamingResults();

    // 隐藏进度
    this.hideAIProgress();

    this.showError('AI处理已取消');
  }

  // 清理流式结果
  cleanupStreamingResults() {
    const streamingContainers = document.querySelectorAll('.ai-result-item.streaming');
    streamingContainers.forEach(container => {
      container.remove();
    });
  }

  // 处理流式AI功能
  async processStreamingAIFunction(functionName, content) {
    console.log(`🌊 开始流式处理: ${functionName}`);

    // 重置取消标志
    this.streamingCancelled = false;

    // 显示取消按钮
    const cancelBtn = document.getElementById('cancel-streaming');
    if (cancelBtn) {
      cancelBtn.style.display = 'inline-block';
    }

    // 初始化流式结果显示
    this.initStreamingResult(functionName);

    const streamOptions = {
      stream: true,
      onChunk: (chunk, currentContent, extraData) => {
        // 检查是否已取消
        if (this.streamingCancelled) {
          return false; // 返回false表示停止处理
        }
        this.updateStreamingResult(functionName, chunk, currentContent, extraData);
      }
    };

    // 使用错误处理器的重试机制
    const result = await errorHandler.handleNetworkError(
      null,
      `AI_${functionName}_streaming`,
      async () => {
        switch (functionName) {
          case 'tags':
            console.log('🏷️ 执行流式标签生成...');
            return await aiFunctions.generateTags(content, streamOptions);
          case 'summary':
            console.log('📝 执行流式摘要生成...');
            return await aiFunctions.generateSummary(content, streamOptions);
          case 'format':
            console.log('📋 执行流式格式整理...');
            return await aiFunctions.formatContent(content, streamOptions);
          case 'translate':
            console.log('🌐 执行流式中英对照翻译...');
            return await aiFunctions.translateContent(content, streamOptions);
          default:
            console.error('❌ 不支持的流式AI功能:', functionName);
            throw new Error('不支持的AI功能');
        }
      },
      { functionName, contentLength: content.length, isStreaming: true }
    );

    console.log('📊 流式AI功能执行结果', {
      functionName,
      success: result.success,
      hasContent: !!(result.tags || result.summary || result.formatted || result.translation),
      usage: result.usage
    });

    if (result.success) {
      this.aiResults[functionName] = result;
      this.finalizeStreamingResult(functionName, result);
      this.showSuccess(`${aiFunctions.getFunctionDescriptions()[functionName].name}完成`);
      console.log('✅ 流式AI功能处理成功');
    } else {
      console.error('❌ 流式AI功能处理失败:', result.error);
      throw new Error(result.error || 'AI处理失败');
    }
  }

  // 处理传统AI功能
  async processNormalAIFunction(functionName, content) {
    console.log(`📝 开始传统处理: ${functionName}`);

    // 使用错误处理器的重试机制
    const result = await errorHandler.handleNetworkError(
      null,
      `AI_${functionName}`,
      async () => {
        switch (functionName) {
          case 'tags':
            console.log('🏷️ 执行标签生成...');
            return await aiFunctions.generateTags(content);
          case 'summary':
            console.log('📝 执行摘要生成...');
            return await aiFunctions.generateSummary(content);
          case 'format':
            console.log('📋 执行格式整理...');
            return await aiFunctions.formatContent(content);
          case 'translate':
            console.log('🌐 执行中英对照翻译...');
            return await aiFunctions.translateContent(content);
          default:
            console.error('❌ 不支持的AI功能:', functionName);
            throw new Error('不支持的AI功能');
        }
      },
      { functionName, contentLength: content.length }
    );

    console.log('📊 AI功能执行结果', {
      functionName,
      success: result.success,
      hasContent: !!(result.tags || result.summary || result.formatted || result.translation),
      usage: result.usage
    });

    if (result.success) {
      this.aiResults[functionName] = result;
      this.showAIResult(functionName, result);
      this.showSuccess(`${aiFunctions.getFunctionDescriptions()[functionName].name}完成`);
      console.log('✅ AI功能处理成功');
    } else {
      console.error('❌ AI功能处理失败:', result.error);
      throw new Error(result.error || 'AI处理失败');
    }
  }

  // 初始化流式结果显示
  initStreamingResult(functionName) {
    const resultsContainer = document.getElementById('ai-results');
    if (!resultsContainer) return;

    const functionDesc = aiFunctions.getFunctionDescriptions()[functionName];

    // 创建流式结果容器
    const streamingContainer = document.createElement('div');
    streamingContainer.className = 'ai-result-item streaming';
    streamingContainer.id = `streaming-${functionName}`;

    streamingContainer.innerHTML = `
      <div class="ai-result-title">
        ${functionDesc.icon} ${functionDesc.name}
        <span class="streaming-indicator">正在生成中...</span>
      </div>
      <div class="streaming-content" id="streaming-content-${functionName}">
        <div class="typing-cursor">|</div>
      </div>
    `;

    resultsContainer.appendChild(streamingContainer);
    resultsContainer.style.display = 'block';
  }

  // 更新流式结果
  updateStreamingResult(functionName, chunk, currentContent, extraData) {
    const contentElement = document.getElementById(`streaming-content-${functionName}`);
    if (!contentElement) return;

    // 根据不同功能类型处理显示
    switch (functionName) {
      case 'tags':
        this.updateStreamingTags(contentElement, currentContent, extraData);
        break;
      case 'summary':
      case 'format':
        this.updateStreamingText(contentElement, currentContent);
        break;
      case 'translate':
        this.updateStreamingTranslation(contentElement, currentContent, extraData);
        break;
    }
  }

  // 更新流式文本内容
  updateStreamingText(contentElement, currentContent) {
    // 移除光标
    const cursor = contentElement.querySelector('.typing-cursor');
    if (cursor) cursor.remove();

    // 更新内容
    contentElement.innerHTML = `
      <div class="streaming-text">${this.escapeHtml(currentContent)}</div>
      <div class="typing-cursor">|</div>
    `;

    // 滚动到底部
    contentElement.scrollTop = contentElement.scrollHeight;
  }

  // 更新流式标签
  updateStreamingTags(contentElement, currentContent, partialTags) {
    if (!partialTags || partialTags.length === 0) {
      this.updateStreamingText(contentElement, currentContent);
      return;
    }

    // 移除光标
    const cursor = contentElement.querySelector('.typing-cursor');
    if (cursor) cursor.remove();

    // 显示部分解析的标签
    const tagsHtml = partialTags.map(tag =>
      `<span class="ai-tag streaming-tag">${tag}</span>`
    ).join('');

    contentElement.innerHTML = `
      <div class="streaming-tags">${tagsHtml}</div>
      <div class="streaming-raw-text">${this.escapeHtml(currentContent)}</div>
      <div class="typing-cursor">|</div>
    `;
  }

  // 更新流式翻译
  updateStreamingTranslation(contentElement, currentContent, partialTranslation) {
    // 移除光标
    const cursor = contentElement.querySelector('.typing-cursor');
    if (cursor) cursor.remove();

    if (partialTranslation && partialTranslation.original && partialTranslation.translated) {
      // 显示结构化的翻译结果
      contentElement.innerHTML = `
        <div class="streaming-translation">
          <div class="translation-section">
            <div class="translation-label">原文</div>
            <div class="translation-content original">${this.escapeHtml(partialTranslation.original)}</div>
          </div>
          <div class="translation-section">
            <div class="translation-label">译文</div>
            <div class="translation-content translated">${this.escapeHtml(partialTranslation.translated)}</div>
          </div>
        </div>
        <div class="typing-cursor">|</div>
      `;
    } else {
      // 显示原始内容
      this.updateStreamingText(contentElement, currentContent);
    }
  }

  // 完成流式结果显示
  finalizeStreamingResult(functionName, result) {
    const streamingContainer = document.getElementById(`streaming-${functionName}`);
    if (!streamingContainer) return;

    // 移除流式指示器和光标
    const indicator = streamingContainer.querySelector('.streaming-indicator');
    const cursor = streamingContainer.querySelector('.typing-cursor');
    if (indicator) indicator.remove();
    if (cursor) cursor.remove();

    // 移除流式类名
    streamingContainer.classList.remove('streaming');

    // 用最终结果替换流式内容
    const contentElement = streamingContainer.querySelector('.streaming-content');
    if (contentElement) {
      // 使用现有的showAIResult逻辑生成最终HTML
      const tempContainer = document.createElement('div');
      this.renderFinalResult(tempContainer, functionName, result);

      // 替换内容
      streamingContainer.innerHTML = tempContainer.innerHTML;
    }

    // 如果是标签结果，初始化标签状态
    if (functionName === 'tags') {
      setTimeout(() => {
        this.initializeTagStates();
      }, 50);
    }
  }

  // 渲染最终结果 (复用showAIResult的逻辑)
  renderFinalResult(container, functionName, result) {
    const functionDesc = aiFunctions.getFunctionDescriptions()[functionName];
    let resultHtml = '';

    switch (functionName) {
      case 'tags':
        if (result.tags && result.tags.length > 0) {
          const tagsHtml = result.tags.map(tag => {
            const isAdded = this.addedTags.has(tag);
            const className = isAdded ? 'ai-tag added' : 'ai-tag';
            const title = isAdded ? '点击移除此标签' : '点击添加此标签';
            const icon = isAdded ? '✓ ' : '';
            return `<span class="${className}" data-tag="${tag}" title="${title}">${icon}${tag}</span>`;
          }).join('');

          resultHtml = `
            <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
            <div class="ai-tags-container">
              <div class="ai-tags-title">
                <span>点击单个标签添加/移除，或使用下方按钮添加全部：</span>
                <div class="ai-tags-legend">
                  <span class="legend-item">
                    <span class="ai-tag demo">未添加</span> 点击添加
                  </span>
                  <span class="legend-item">
                    <span class="ai-tag demo added">✓ 已添加</span> 点击移除
                  </span>
                </div>
              </div>
              <div class="ai-tags">${tagsHtml}</div>
            </div>
            <button class="ai-apply-btn" data-type="tags" data-content="${result.tags.join(', ')}">
              <span>新增全部标签</span>
              <span style="font-size: 11px; opacity: 0.8;">(${result.tags.length}个)</span>
            </button>
          `;
        }
        break;
      case 'summary':
        if (result.summary) {
          resultHtml = `
            <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
            <div class="ai-result-content">${this.escapeHtml(result.summary)}</div>
            <button class="ai-apply-btn" data-type="replace" data-content="${this.escapeHtml(result.summary)}">使用摘要</button>
          `;
        }
        break;
      case 'format':
        if (result.formatted) {
          resultHtml = `
            <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
            <div class="ai-result-content">${this.escapeHtml(result.formatted.substring(0, 200))}${result.formatted.length > 200 ? '...' : ''}</div>
            <button class="ai-apply-btn" data-type="replace" data-content="${this.escapeHtml(result.formatted)}">应用优化</button>
          `;
        }
        break;
      case 'translate':
        if (result.translation) {
          const translation = result.translation;
          const originalLang = result.originalLanguage === 'zh' ? '中文' : '英文';
          const targetLang = result.targetLanguage === 'zh' ? '中文' : '英文';

          resultHtml = `
            <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
            <div class="translation-container">
              <div class="translation-pair">
                <div class="translation-section">
                  <div class="translation-label">原文 (${originalLang})</div>
                  <div class="translation-content original">${this.escapeHtml(translation.original)}</div>
                </div>
                <div class="translation-section">
                  <div class="translation-label">译文 (${targetLang})</div>
                  <div class="translation-content translated">${this.escapeHtml(translation.translated)}</div>
                </div>
              </div>
            </div>
            <div class="translation-actions">
              <button class="ai-apply-btn" data-type="replace" data-content="${this.escapeHtml(translation.translated)}">使用译文</button>
              <button class="ai-apply-btn secondary" data-type="replace" data-content="${this.escapeHtml(translation.original + '\n\n' + translation.translated)}">保持对照格式</button>
            </div>
          `;
        }
        break;
    }

    container.innerHTML = `<div class="ai-result-item">${resultHtml}</div>`;
  }

  // 显示AI处理进度
  async showAIProgress(functionName) {
    const progress = document.getElementById('ai-progress');
    const functions = document.querySelectorAll('.ai-function');

    if (progress) {
      progress.style.display = 'block';
      const functionDesc = aiFunctions.getFunctionDescriptions()[functionName];

      // 检查是否使用流式输出来决定是否显示取消按钮
      const isStreaming = await this.shouldUseStreaming(functionName);
      const cancelButton = isStreaming ?
        '<button class="cancel-streaming-btn" id="cancel-streaming">取消</button>' : '';

      progress.innerHTML = `
        <div class="loading-spinner"></div>
        <div>正在${functionDesc.name}中...</div>
        ${cancelButton}
      `;

      // 重新绑定取消按钮事件
      if (isStreaming) {
        const cancelBtn = document.getElementById('cancel-streaming');
        if (cancelBtn) {
          cancelBtn.addEventListener('click', () => {
            this.cancelStreaming();
          });
        }
      }
    }

    // 标记正在处理的功能
    functions.forEach(func => {
      if (func.dataset.function === functionName) {
        func.classList.add('processing');
      }
    });
  }

  // 隐藏AI处理进度
  hideAIProgress() {
    const progress = document.getElementById('ai-progress');
    const functions = document.querySelectorAll('.ai-function');

    if (progress) {
      progress.style.display = 'none';
    }

    functions.forEach(func => {
      func.classList.remove('processing');
    });
  }

  // 显示AI结果
  showAIResult(functionName, result) {
    const resultsContainer = document.getElementById('ai-results');
    if (!resultsContainer) return;

    const functionDesc = aiFunctions.getFunctionDescriptions()[functionName];
    let resultHtml = '';

    switch (functionName) {
      case 'tags':
        if (result.tags && result.tags.length > 0) {
          const tagsHtml = result.tags.map(tag => {
            const isAdded = this.addedTags.has(tag);
            const className = isAdded ? 'ai-tag added' : 'ai-tag';
            const title = isAdded ? '点击移除此标签' : '点击添加此标签';
            const icon = isAdded ? '✓ ' : '';
            return `<span class="${className}" data-tag="${tag}" title="${title}">${icon}${tag}</span>`;
          }).join('');

          resultHtml = `
            <div class="ai-result-item">
              <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
              <div class="ai-tags-container">
                <div class="ai-tags-title">
                  <span>点击单个标签添加/移除，或使用下方按钮添加全部：</span>
                  <div class="ai-tags-legend">
                    <span class="legend-item">
                      <span class="ai-tag demo">未添加</span> 点击添加
                    </span>
                    <span class="legend-item">
                      <span class="ai-tag demo added">✓ 已添加</span> 点击移除
                    </span>
                  </div>
                </div>
                <div class="ai-tags">${tagsHtml}</div>
              </div>
              <button class="ai-apply-btn" data-type="tags" data-content="${result.tags.join(', ')}">
                <span>新增全部标签</span>
                <span style="font-size: 11px; opacity: 0.8;">(${result.tags.length}个)</span>
              </button>
            </div>
          `;
        }
        break;
      case 'summary':
        if (result.summary) {
          resultHtml = `
            <div class="ai-result-item">
              <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
              <div class="ai-result-content">${this.escapeHtml(result.summary)}</div>
              <button class="ai-apply-btn" data-type="replace" data-content="${this.escapeHtml(result.summary)}">使用摘要</button>
            </div>
          `;
        }
        break;
      case 'format':
        if (result.formatted) {
          resultHtml = `
            <div class="ai-result-item">
              <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
              <div class="ai-result-content">${this.escapeHtml(result.formatted.substring(0, 200))}${result.formatted.length > 200 ? '...' : ''}</div>
              <button class="ai-apply-btn" data-type="replace" data-content="${this.escapeHtml(result.formatted)}">应用优化</button>
            </div>
          `;
        }
        break;
      case 'translate':
        if (result.translation) {
          const translation = result.translation;
          const originalLang = result.originalLanguage === 'zh' ? '中文' : '英文';
          const targetLang = result.targetLanguage === 'zh' ? '中文' : '英文';

          resultHtml = `
            <div class="ai-result-item">
              <div class="ai-result-title">${functionDesc.icon} ${functionDesc.name}</div>
              <div class="translation-container">
                <div class="translation-pair">
                  <div class="translation-section">
                    <div class="translation-label">原文 (${originalLang})</div>
                    <div class="translation-content original">${this.escapeHtml(translation.original)}</div>
                  </div>
                  <div class="translation-section">
                    <div class="translation-label">译文 (${targetLang})</div>
                    <div class="translation-content translated">${this.escapeHtml(translation.translated)}</div>
                  </div>
                </div>
              </div>
              <div class="translation-actions">
                <button class="ai-apply-btn" data-type="replace" data-content="${this.escapeHtml(translation.translated)}">使用译文</button>
                <button class="ai-apply-btn secondary" data-type="replace" data-content="${this.escapeHtml(translation.original + '\n\n' + translation.translated)}">保持对照格式</button>
              </div>
            </div>
          `;
        }
        break;
    }

    if (resultHtml) {
      resultsContainer.innerHTML += resultHtml;
      resultsContainer.style.display = 'block';

      // 如果是标签结果，初始化标签状态
      if (functionName === 'tags') {
        setTimeout(() => {
          this.initializeTagStates();
        }, 50);
      }
    }
  }

  // 应用AI结果
  applyAIResult(type, content) {
    const textarea = document.getElementById('content-textarea');
    if (!textarea) return;

    const decodedContent = this.decodeHtml(content);

    switch (type) {
      case 'replace':
        textarea.value = decodedContent;
        this.updateCharCount();
        this.saveToLocalStorage();
        this.showSuccess('AI结果已应用');
        break;
      case 'tags':
        // 使用新的标签添加逻辑
        const allTags = decodedContent.split(', ').map(tag => tag.trim());
        this.addAllRemainingTags(allTags);
        break;
    }
  }

  // 解码HTML实体
  decodeHtml(html) {
    const txt = document.createElement('textarea');
    txt.innerHTML = html;
    return txt.value;
  }

  // 检测文本中已存在的标签
  detectExistingTags(content) {
    const tagRegex = /#([^\s#]+)/g;
    const existingTags = new Set();
    let match;

    while ((match = tagRegex.exec(content)) !== null) {
      existingTags.add(match[1]);
    }

    console.log('🔍 检测到已存在的标签:', Array.from(existingTags));
    return existingTags;
  }

  // 添加单个标签到内容
  addSingleTag(tagName) {
    const textarea = document.getElementById('content-textarea');
    if (!textarea) return;

    // 检查标签是否已存在
    const currentContent = textarea.value;
    const existingTags = this.detectExistingTags(currentContent);

    if (existingTags.has(tagName) || this.addedTags.has(tagName)) {
      console.log('⚠️ 标签已存在，跳过添加:', tagName);
      return;
    }

    // 添加标签到内容末尾
    const tagText = `#${tagName}`;
    const newContent = currentContent.trim() + (currentContent.trim() ? ' ' : '') + tagText;

    textarea.value = newContent;
    this.addedTags.add(tagName);

    // 更新字符计数和保存到本地存储
    this.updateCharCount();
    this.saveToLocalStorage();

    // 更新标签UI状态
    this.updateTagUI(tagName, true);

    console.log('✅ 添加单个标签:', tagName);
  }

  // 移除单个标签
  removeSingleTag(tagName) {
    const textarea = document.getElementById('content-textarea');
    if (!textarea) return;

    const currentContent = textarea.value;
    const tagPattern = new RegExp(`#${tagName}(?=\\s|$)`, 'g');

    // 移除标签文本
    let newContent = currentContent.replace(tagPattern, '').replace(/\s+/g, ' ').trim();

    textarea.value = newContent;

    // 从已添加标签集合中移除
    this.addedTags.delete(tagName);

    // 更新字符计数和保存到本地存储
    this.updateCharCount();
    this.saveToLocalStorage();

    // 更新标签UI状态
    this.updateTagUI(tagName, false);

    // 更新"新增全部标签"按钮状态
    this.updateAddAllButtonState();

    console.log('✅ 移除单个标签:', tagName);
  }

  // 添加所有未添加的标签
  addAllRemainingTags(allTags) {
    const textarea = document.getElementById('content-textarea');
    if (!textarea) return;

    const currentContent = textarea.value;
    const existingTags = this.detectExistingTags(currentContent);

    // 找出尚未添加的标签
    const remainingTags = allTags.filter(tag =>
      !existingTags.has(tag) && !this.addedTags.has(tag)
    );

    if (remainingTags.length === 0) {
      console.log('ℹ️ 所有标签都已添加');
      return;
    }

    // 添加剩余标签
    const tagTexts = remainingTags.map(tag => `#${tag}`);
    const newContent = currentContent.trim() + (currentContent.trim() ? ' ' : '') + tagTexts.join(' ');

    textarea.value = newContent;

    // 更新已添加标签集合
    remainingTags.forEach(tag => this.addedTags.add(tag));

    // 更新字符计数和保存到本地存储
    this.updateCharCount();
    this.saveToLocalStorage();

    // 更新所有标签UI状态
    remainingTags.forEach(tag => this.updateTagUI(tag, true));

    console.log('✅ 添加剩余标签:', remainingTags);
  }

  // 更新标签UI状态
  updateTagUI(tagName, isAdded) {
    const tagElements = document.querySelectorAll(`.ai-tag[data-tag="${tagName}"]:not(.demo)`);
    tagElements.forEach(element => {
      if (isAdded) {
        element.classList.add('added');
        element.title = '点击移除此标签';
        element.textContent = `✓ ${tagName}`;
      } else {
        element.classList.remove('added');
        element.title = '点击添加此标签';
        element.textContent = tagName;
      }
    });

    // 更新"新增全部标签"按钮状态
    this.updateAddAllButtonState();
  }

  // 更新"新增全部标签"按钮状态
  updateAddAllButtonState() {
    const addAllBtn = document.querySelector('.ai-apply-btn[data-type="tags"]');
    if (!addAllBtn) return;

    const allTagElements = document.querySelectorAll('.ai-tag:not(.added)');
    const hasRemainingTags = allTagElements.length > 0;

    if (hasRemainingTags) {
      addAllBtn.disabled = false;
      addAllBtn.innerHTML = `
        <span>新增全部标签</span>
        <span style="font-size: 11px; opacity: 0.8;">(${allTagElements.length}个)</span>
      `;
    } else {
      addAllBtn.disabled = true;
      addAllBtn.innerHTML = `
        <span>✓ 所有标签已添加</span>
      `;
    }
  }

  // 初始化标签状态（在渲染内容时调用）
  initializeTagStates() {
    const textarea = document.getElementById('content-textarea');
    if (!textarea) return;

    // 重置已添加标签集合
    this.addedTags.clear();

    // 检测当前内容中的标签
    const existingTags = this.detectExistingTags(textarea.value);
    existingTags.forEach(tag => this.addedTags.add(tag));

    // 更新所有标签UI状态
    const allTagElements = document.querySelectorAll('.ai-tag');
    allTagElements.forEach(element => {
      const tagName = element.dataset.tag;
      if (tagName && this.addedTags.has(tagName)) {
        this.updateTagUI(tagName, true);
      }
    });

    console.log('🔄 初始化标签状态:', Array.from(this.addedTags));
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // 显示缓存统计信息
  showCacheStats() {
    if (typeof requestCache === 'undefined') {
      console.log('缓存系统未启用');
      this.showInfo('缓存系统未启用');
      return;
    }

    const stats = requestCache.getStats();
    console.log('📊 缓存统计信息:', stats);

    // 在界面上显示缓存统计
    const message = `缓存统计 - 命中率: ${stats.hitRate} | 当前: ${stats.currentSize}/${stats.maxSize} | 节省: ${stats.compressionSavedKB}KB`;
    this.showInfo(message);
  }

  // 清理缓存
  clearCache(functionName = null) {
    if (typeof requestCache === 'undefined') {
      this.showError('缓存系统未启用');
      return;
    }

    if (functionName) {
      const cleared = requestCache.clearFunction(functionName);
      this.showSuccess(`已清理 ${functionName} 缓存 (${cleared} 项)`);
    } else {
      requestCache.clear();
      this.showSuccess('已清空所有缓存');
    }
  }

  // 预热常用缓存
  async warmupCache() {
    if (typeof requestCache === 'undefined') {
      this.showError('缓存系统未启用');
      return;
    }

    this.showInfo('正在预热缓存...');

    try {
      // 预热一些常见的请求
      const commonRequests = [
        { functionName: 'generateTags', content: '这是一个测试内容' },
        { functionName: 'generateSummary', content: '这是一个测试内容用于生成摘要' }
      ];

      await requestCache.warmup(commonRequests);
      this.showSuccess('缓存预热完成');
    } catch (error) {
      this.showError('缓存预热失败: ' + error.message);
    }
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new FlomoSidePanel();
});
