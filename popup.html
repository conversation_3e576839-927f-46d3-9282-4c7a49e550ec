<!DOCTYPE html>
<html>

<head>
  <title data-i18n="settingsTitle"></title>
  <meta charset="UTF-8">
  <style>
    :root {
      /* Chrome Extension Design System Colors */
      --primary-bg: #ffffff;
      --secondary-bg: #f8f9fa;
      --surface-bg: #ffffff;
      --text-primary: #202124;
      --text-secondary: #5f6368;
      --text-tertiary: #80868b;
      --accent-primary: #1a73e8;
      --accent-hover: #1557b0;
      --accent-pressed: #1143a3;
      --border-light: #e8eaed;
      --border-medium: #dadce0;
      --success-color: #137333;
      --success-bg: #e6f4ea;
      --error-color: #d93025;
      --error-bg: #fce8e6;
      --warning-color: #ea8600;
      --warning-bg: #fef7e0;
      --focus-ring: rgba(26, 115, 232, 0.24);
      --shadow-1: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
      --shadow-2: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: 'Google Sans', 'Segoe UI', Tahoma, sans-serif;
      font-size: 14px;
      line-height: 1.5;
      background-color: var(--primary-bg);
      color: var(--text-primary);
      margin: 0;
      width: 360px;
      min-height: 400px;
    }

    .container {
      padding: 20px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* Header */
    .header {
      text-align: center;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--border-light);
      margin-bottom: 4px;
    }

    h1 {
      font-size: 20px;
      font-weight: 500;
      margin: 0;
      color: var(--text-primary);
      letter-spacing: 0.25px;
    }

    /* Sections */
    .section {
      background: var(--surface-bg);
      border-radius: 12px;
      padding: 16px;
      border: 1px solid var(--border-light);
      transition: all 0.2s ease;
    }

    .section:hover {
      border-color: var(--border-medium);
      box-shadow: var(--shadow-1);
    }

    .section-title {
      font-size: 16px;
      font-weight: 500;
      margin: 0 0 16px 0;
      color: var(--text-primary);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .section-title::before {
      content: '';
      width: 4px;
      height: 16px;
      background: var(--accent-primary);
      border-radius: 2px;
    }

    /* Form Elements */
    .input-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 16px;
    }

    .input-group:last-child {
      margin-bottom: 0;
    }

    label {
      font-size: 13px;
      font-weight: 500;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    input[type="text"],
    input[type="password"],
    select {
      background-color: var(--surface-bg);
      border: 1px solid var(--border-medium);
      border-radius: 8px;
      padding: 12px;
      font-size: 14px;
      font-family: inherit;
      width: 100%;
      transition: all 0.2s ease;
      outline: none;
    }

    input[type="text"]:focus,
    input[type="password"]:focus,
    select:focus {
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 3px var(--focus-ring);
    }

    input[type="text"]:hover,
    input[type="password"]:hover,
    select:hover {
      border-color: var(--text-secondary);
    }

    /* Buttons */
    .btn {
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      font-family: inherit;
      cursor: pointer;
      transition: all 0.2s ease;
      outline: none;
      position: relative;
      overflow: hidden;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: currentColor;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .btn:hover::before {
      opacity: 0.08;
    }

    .btn:active::before {
      opacity: 0.12;
    }

    .btn-primary {
      background-color: var(--accent-primary);
      color: white;
      padding: 12px 24px;
      width: 100%;
      margin-top: 8px;
    }

    .btn-primary:hover {
      background-color: var(--accent-hover);
      box-shadow: var(--shadow-1);
    }

    .btn-primary:active {
      background-color: var(--accent-pressed);
    }

    .btn-secondary {
      background-color: var(--secondary-bg);
      color: var(--accent-primary);
      border: 1px solid var(--border-medium);
      padding: 8px 16px;
      font-size: 13px;
      margin-top: 8px;
    }

    .btn-secondary:hover {
      background-color: var(--border-light);
      border-color: var(--accent-primary);
    }

    .btn-secondary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background-color: var(--secondary-bg);
      color: var(--text-tertiary);
    }

    /* Info Text Styles */
    .info-text {
      color: var(--text-secondary);
      font-size: 13px;
      line-height: 1.6;
    }

    .info-text ul {
      margin: 8px 0;
      padding-left: 20px;
    }

    .info-text li {
      margin: 4px 0;
    }

    /* Status Messages */
    .status {
      text-align: center;
      font-size: 13px;
      font-weight: 500;
      padding: 12px 16px;
      border-radius: 8px;
      display: none;
      margin-top: 16px;
      animation: slideIn 0.3s ease;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-8px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .status.success {
      color: var(--success-color);
      background-color: var(--success-bg);
      border: 1px solid rgba(19, 115, 51, 0.2);
    }

    .status.error {
      color: var(--error-color);
      background-color: var(--error-bg);
      border: 1px solid rgba(217, 48, 37, 0.2);
    }

    /* Help Buttons */
    .help-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .help-buttons .btn {
      flex: 1;
      min-width: 140px;
      font-size: 13px;
      padding: 10px 12px;
    }

    /* Responsive Design */
    @media (max-width: 380px) {
      body {
        width: 320px;
      }

      .container {
        padding: 16px;
      }

      .section {
        padding: 12px;
      }

      .help-buttons .btn {
        min-width: 120px;
        font-size: 12px;
      }
    }

    /* Loading States */
    .loading {
      position: relative;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* Focus Management */
    .focus-visible {
      outline: 2px solid var(--accent-primary);
      outline-offset: 2px;
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <h1 data-i18n="settingsTitle">Flomo Settings</h1>
    </div>

    <!-- Flomo配置 -->
    <div class="section">
      <div class="section-title">Flomo 配置</div>
      <div class="input-group">
        <label for="api-url" data-i18n="apiUrlLabel">Your Flomo API URL</label>
        <input type="text" id="api-url" data-i18n-placeholder="apiUrlPlaceholder">
      </div>
    </div>

    <!-- AI配置已预设，无需用户配置 -->
    <div class="section">
      <div class="section-title">AI 智能处理</div>
      <div class="info-text">
        <p>✅ AI 功能已预配置，包括：</p>
        <ul>
          <li>🏷️ 智能标签生成</li>
          <li>🌐 中英对照翻译</li>
        </ul>
        <p>您可以直接在侧边栏中使用这些功能。</p>
      </div>
    </div>

    <!-- Save Button -->
    <button class="btn btn-primary" id="save-button" data-i18n="saveButton">Save</button>

    <!-- Status Message -->
    <div id="status-message" class="status"></div>
  </div>
  <script src="input-validator.js"></script>
  <script src="secure-storage.js"></script>
  <script src="error-config.js"></script>
  <script src="notification-manager.js"></script>
  <script src="error-handler.js"></script>
  <script src="onboarding.js"></script>
  <script src="onboarding-debug.js"></script>
  <script src="popup.js"></script>
</body>

</html>