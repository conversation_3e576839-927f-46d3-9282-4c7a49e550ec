// Service Worker专用输入验证器
class ServiceWorkerInputValidator {
  constructor() {
    this.maxContentLength = 10000; // 最大内容长度
    this.maxTitleLength = 200;     // 最大标题长度
    this.maxUrlLength = 2000;      // 最大URL长度

    // 危险标签列表（用于HTML清理）
    this.dangerousTags = [
      'script', 'iframe', 'object', 'embed', 'form', 'input',
      'button', 'select', 'textarea', 'link', 'meta', 'style'
    ];

    // 危险属性列表
    this.dangerousAttributes = [
      'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout',
      'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
      'javascript:', 'vbscript:', 'data:', 'file:'
    ];
  }

  // 验证和清理输入内容
  sanitizeInput(input, type = 'content') {
    if (!input || typeof input !== 'string') {
      throw new Error('输入内容不能为空');
    }

    // 移除首尾空白
    input = input.trim();

    if (!input) {
      throw new Error('输入内容不能为空');
    }

    switch (type) {
      case 'content':
        return this.sanitizeContent(input);
      case 'title':
        return this.sanitizeTitle(input);
      case 'url':
        return this.sanitizeUrl(input);
      case 'html':
        return this.sanitizeHtml(input);
      default:
        return this.sanitizeContent(input);
    }
  }

  // 清理普通文本内容
  sanitizeContent(content) {
    // 长度验证
    if (content.length > this.maxContentLength) {
      throw new Error(`内容过长，最大允许${this.maxContentLength}个字符`);
    }

    // 移除控制字符（保留换行和制表符）
    content = content.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

    // 规范化换行符
    content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // 限制连续换行
    content = content.replace(/\n{4,}/g, '\n\n\n');

    return content;
  }

  // 清理标题
  sanitizeTitle(title) {
    if (title.length > this.maxTitleLength) {
      throw new Error(`标题过长，最大允许${this.maxTitleLength}个字符`);
    }

    // 移除换行符和控制字符
    title = title.replace(/[\r\n\x00-\x1F\x7F]/g, ' ');

    // 合并多个空格
    title = title.replace(/\s+/g, ' ').trim();

    return title;
  }

  // 清理URL
  sanitizeUrl(url) {
    if (url.length > this.maxUrlLength) {
      throw new Error(`URL过长，最大允许${this.maxUrlLength}个字符`);
    }

    // 基本URL格式验证
    try {
      new URL(url);
    } catch (error) {
      throw new Error('URL格式无效');
    }

    // 检查协议安全性
    const allowedProtocols = ['http:', 'https:', 'ftp:', 'ftps:'];
    const urlObj = new URL(url);

    if (!allowedProtocols.includes(urlObj.protocol)) {
      throw new Error('不支持的URL协议');
    }

    return url;
  }

  // 清理HTML内容（Service Worker版本 - 使用正则表达式替代DOM操作）
  sanitizeHtml(html) {
    // 长度验证
    if (html.length > this.maxContentLength * 2) { // HTML允许更长
      throw new Error('HTML内容过长');
    }

    // 移除危险标签（使用正则表达式）
    this.dangerousTags.forEach(tag => {
      const regex = new RegExp(`<${tag}[^>]*>.*?<\/${tag}>`, 'gis');
      html = html.replace(regex, '');

      // 移除自闭合标签
      const selfClosingRegex = new RegExp(`<${tag}[^>]*\/?>`, 'gi');
      html = html.replace(selfClosingRegex, '');
    });

    // 移除危险属性
    this.dangerousAttributes.forEach(attr => {
      if (attr.endsWith(':')) {
        // 协议类型的属性
        const regex = new RegExp(`\\s+\\w+\\s*=\\s*["']?${attr.replace(':', '\\:')}[^"'\\s>]*["']?`, 'gi');
        html = html.replace(regex, '');
      } else {
        // 事件处理器属性
        const regex = new RegExp(`\\s+${attr}\\s*=\\s*["'][^"']*["']`, 'gi');
        html = html.replace(regex, '');
      }
    });

    // 移除注释
    html = html.replace(/<!--[\s\S]*?-->/g, '');

    // 移除CDATA
    html = html.replace(/<!\[CDATA\[[\s\S]*?\]\]>/g, '');

    return html;
  }

  // 验证API密钥格式
  validateApiKey(apiKey, provider = 'unknown') {
    if (!apiKey || typeof apiKey !== 'string') {
      throw new Error('API密钥不能为空');
    }

    apiKey = apiKey.trim();

    if (!apiKey) {
      throw new Error('API密钥不能为空');
    }

    // 基本长度检查
    if (apiKey.length < 10) {
      throw new Error('API密钥长度过短');
    }

    if (apiKey.length > 200) {
      throw new Error('API密钥长度过长');
    }

    // 检查是否包含明显的占位符
    const placeholders = ['your-api-key', 'api-key-here', 'replace-me', 'xxx', '***'];
    const lowerKey = apiKey.toLowerCase();

    for (const placeholder of placeholders) {
      if (lowerKey.includes(placeholder)) {
        throw new Error('请输入有效的API密钥');
      }
    }

    return apiKey;
  }

  // 验证提供商ID
  validateProviderId(providerId) {
    if (typeof providerId !== 'string') {
      throw new Error('提供商ID必须是字符串类型');
    }

    const allowedProviders = ['siliconflow', 'openrouter', 'deepseek', 'moonshot'];
    if (!allowedProviders.includes(providerId)) {
      throw new Error('不支持的AI服务提供商');
    }

    return providerId;
  }

  // 验证URL格式
  validateUrl(url) {
    if (!url || typeof url !== 'string') {
      throw new Error('URL不能为空');
    }

    url = url.trim();

    if (!url) {
      throw new Error('URL不能为空');
    }

    try {
      const urlObj = new URL(url);

      // 检查协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        throw new Error('URL必须使用HTTP或HTTPS协议');
      }

      return url;
    } catch (error) {
      throw new Error('URL格式无效');
    }
  }

  // 验证JSON格式
  validateJson(jsonString) {
    if (!jsonString || typeof jsonString !== 'string') {
      throw new Error('JSON内容不能为空');
    }

    try {
      const parsed = JSON.parse(jsonString);
      return parsed;
    } catch (error) {
      throw new Error('JSON格式无效: ' + error.message);
    }
  }

  // 清理文件名
  sanitizeFilename(filename) {
    if (!filename || typeof filename !== 'string') {
      throw new Error('文件名不能为空');
    }

    // 移除危险字符
    filename = filename.replace(/[<>:"/\\|?*\x00-\x1F]/g, '');

    // 移除首尾的点和空格
    filename = filename.replace(/^[\s.]+|[\s.]+$/g, '');

    if (!filename) {
      throw new Error('文件名无效');
    }

    // 限制长度
    if (filename.length > 255) {
      filename = filename.substring(0, 255);
    }

    return filename;
  }
}

// 创建Service Worker输入验证器实例
const inputValidator = new ServiceWorkerInputValidator();

// 导出到全局作用域
if (typeof self !== 'undefined') {
  self.inputValidator = inputValidator;
}
