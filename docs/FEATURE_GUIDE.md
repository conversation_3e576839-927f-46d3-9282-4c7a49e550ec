# 功能使用指南

## 🎯 新功能概览

Chrome扩展"Save to Flomo"现已升级到v2.0.0，新增了强大的富文本格式保留和AI智能处理功能。

## 🎨 富文本格式保留功能

### 支持的格式类型

- **文本格式**：粗体、斜体、下划线、删除线
- **标题**：H1-H6各级标题
- **链接**：自动保留链接地址和显示文本
- **列表**：有序列表和无序列表
- **引用**：块引用格式
- **代码**：行内代码和代码块
- **图片**：图片链接和alt文本
- **表格**：完整的表格结构

### 使用方法

1. **选择富文本内容**：在网页上选择包含格式的文本
2. **打开侧边栏**：右键选择"保存到 Flomo"
3. **查看格式检测**：侧边栏会显示检测到的格式类型
4. **选择格式处理方式**：
   - **智能选择**：自动判断是否保留格式
   - **保留格式**：转换为Markdown格式
   - **纯文本**：移除所有格式
5. **预览转换结果**：在编辑区域查看转换后的内容

### 格式转换示例

**原始HTML：**
```html
<h2>标题</h2>
<p>这是<strong>粗体</strong>和<em>斜体</em>文本。</p>
<ul>
  <li>列表项1</li>
  <li>包含<a href="https://example.com">链接</a>的项目</li>
</ul>
```

**转换为Markdown：**
```markdown
## 标题

这是**粗体**和*斜体*文本。

- 列表项1
- 包含[链接](https://example.com)的项目
```

## 🤖 AI智能处理功能

### 支持的AI服务商

1. **硅基流动 (SiliconFlow)**
   - 模型：Qwen2-7B、Qwen2-72B、GLM-4-9B等
   - 特点：高性价比，中文支持优秀

2. **OpenRouter**
   - 模型：Claude 3、GPT-4o、Llama 3.1等
   - 特点：模型选择丰富，质量稳定

3. **DeepSeek**
   - 模型：DeepSeek Chat、DeepSeek Coder
   - 特点：代码处理能力强

4. **Moonshot AI**
   - 模型：Moonshot v1 8K/32K/128K
   - 特点：长文本处理能力强

### AI功能详解

#### 🏷️ 智能标签生成
- **功能**：根据内容自动生成3-8个相关标签
- **适用场景**：文章分类、内容整理
- **使用技巧**：内容越具体，标签越精准

#### 📝 内容摘要
- **功能**：为长文本生成简洁摘要
- **适用场景**：长文章、报告、新闻
- **摘要长度**：约为原文的20-30%

#### ✨ 结构优化
- **功能**：改进文本结构和可读性
- **优化内容**：段落结构、逻辑顺序、表达清晰度
- **适用场景**：文章润色、内容重组

#### 🔤 语言优化
- **功能**：改进语法和表达方式
- **优化内容**：语法错误、用词准确性、表达流畅度
- **适用场景**：文本校对、表达改进

#### 📋 格式整理
- **功能**：自动整理文档格式和结构
- **整理内容**：标题层级、段落分隔、列表格式
- **适用场景**：文档规范化、格式统一

### AI功能使用流程

1. **配置AI服务**
   - 打开扩展设置页面
   - 选择AI服务提供商
   - 选择合适的模型
   - 输入API密钥
   - 测试连接确保配置正确

2. **使用AI功能**
   - 在侧边栏中输入或编辑内容
   - 点击需要的AI功能按钮
   - 等待AI处理完成
   - 查看处理结果
   - 选择应用或继续编辑

3. **结果应用**
   - **标签生成**：自动添加到内容末尾
   - **其他功能**：替换当前内容
   - 可以多次使用不同AI功能
   - 支持撤销和重新编辑

## 🔧 配置建议

### AI服务商选择建议

- **日常使用**：推荐硅基流动，性价比高
- **高质量需求**：推荐OpenRouter的Claude模型
- **代码内容**：推荐DeepSeek Coder
- **长文本处理**：推荐Moonshot AI的32K模型

### 模型选择建议

- **标签生成**：使用7B-8B模型即可
- **内容摘要**：推荐使用更大的模型
- **语言优化**：Claude模型表现优秀
- **结构优化**：GPT-4o或Qwen2-72B

## 🚀 最佳实践

### 富文本处理
1. 选择内容时尽量包含完整的格式结构
2. 对于复杂表格，建议选择"保留格式"
3. 纯文本内容可以选择"智能选择"模式

### AI功能使用
1. 内容长度控制在10-5000字符之间效果最佳
2. 可以组合使用多个AI功能
3. 先使用结构优化，再使用语言优化
4. 标签生成建议在内容确定后使用

### 性能优化
1. 避免频繁切换AI服务商
2. 合理使用AI功能，避免过度依赖
3. 定期清理浏览器缓存和扩展数据

## 🔍 故障排除

### 富文本问题
- **格式丢失**：检查原始内容是否包含HTML格式
- **转换错误**：尝试选择"纯文本"模式
- **显示异常**：刷新页面重新选择内容

### AI功能问题
- **无法使用**：检查API密钥配置
- **处理失败**：检查网络连接和API配额
- **结果异常**：尝试更换模型或重新处理

### 性能问题
- **响应缓慢**：检查网络连接
- **内存占用高**：重启浏览器
- **扩展崩溃**：重新加载扩展

## 📞 技术支持

如果遇到问题，请按以下步骤排查：

1. 检查Chrome版本（需要114+）
2. 验证扩展权限设置
3. 测试网络连接
4. 查看浏览器控制台错误信息
5. 重新加载扩展

更多帮助请参考README.md和CHANGELOG.md文档。
