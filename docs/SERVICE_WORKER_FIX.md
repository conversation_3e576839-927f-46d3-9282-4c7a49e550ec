# Service Worker 兼容性修复

## 🐛 问题描述

在Chrome扩展的Service Worker环境中出现以下错误：
- `Service worker registration failed. Status code: 15`
- `Uncaught ReferenceError: window is not defined`

## 🔍 问题分析

### 根本原因
1. **环境差异**：Service Worker运行在独立的全局作用域中，没有`window`、`document`等DOM对象
2. **代码兼容性**：原有的错误处理器和其他模块直接使用了`window`对象
3. **依赖冲突**：某些模块在Service Worker环境中不应该被加载

### 具体问题
1. `error-handler.js`中直接使用`window.addEventListener`
2. `error-handler.js`中使用`window.location.href`
3. 在Service Worker中加载了依赖DOM的模块

## ✅ 解决方案

### 1. 修复错误处理器兼容性

**文件：`error-handler.js`**

```javascript
// 修复前
window.addEventListener('error', (event) => {
  this.logError('JavaScript Error', event.error);
});

// 修复后
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    this.logError('JavaScript Error', event.error);
  });
} else if (typeof self !== 'undefined') {
  // Service Worker环境
  self.addEventListener('error', (event) => {
    this.logError('Service Worker Error', event.error);
  });
}
```

### 2. 创建Service Worker专用错误处理器

**新文件：`error-handler-sw.js`**

- 专为Service Worker环境设计
- 移除所有DOM依赖
- 保持与原错误处理器相同的API
- 添加Service Worker特有的错误处理

### 3. 更新background.js依赖

**文件：`background.js`**

```javascript
// 修复前
importScripts('error-handler.js');

// 修复后
importScripts('error-handler-sw.js');
```

### 4. 环境检测和兼容性处理

在所有可能在Service Worker中运行的模块中添加环境检测：

```javascript
// 安全的全局对象访问
const globalScope = typeof window !== 'undefined' ? window : 
                   typeof self !== 'undefined' ? self : 
                   typeof global !== 'undefined' ? global : {};

// 安全的URL获取
const currentUrl = typeof window !== 'undefined' ? window.location.href : 
                   'Service Worker Context';

// 安全的用户代理获取
const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : 
                  'Service Worker';
```

## 🔧 修复的文件

### 新增文件
1. **`error-handler-sw.js`** - Service Worker专用错误处理器
2. **`service-worker-test.html`** - Service Worker兼容性测试页面
3. **`SERVICE_WORKER_FIX.md`** - 修复文档

### 修改文件
1. **`error-handler.js`** - 添加环境兼容性检查
2. **`background.js`** - 更新依赖引用

## 🧪 测试验证

### 测试工具
使用`service-worker-test.html`进行兼容性测试：

1. **错误处理器测试**
   - 测试普通环境下的错误处理器
   - 测试Service Worker环境下的错误处理器

2. **AI服务管理器测试**
   - 验证在Service Worker中的正常工作
   - 测试API验证功能

3. **Chrome扩展API测试**
   - 测试存储API
   - 测试运行时API

4. **兼容性检查**
   - 检查必需的API可用性
   - 模拟Service Worker环境

### 测试步骤
1. 打开`service-worker-test.html`
2. 点击"检查兼容性"按钮
3. 运行各项测试
4. 查看测试日志确认无错误

## 📊 修复效果

### 修复前
- ❌ Service Worker注册失败
- ❌ 控制台出现`window is not defined`错误
- ❌ 错误处理器无法在Service Worker中工作
- ❌ 扩展功能受影响

### 修复后
- ✅ Service Worker正常注册
- ✅ 无JavaScript错误
- ✅ 错误处理器在所有环境中正常工作
- ✅ 扩展功能完全正常

## 🔮 预防措施

### 1. 代码规范
- 在可能在Service Worker中运行的代码中避免直接使用DOM对象
- 使用环境检测进行条件性功能调用
- 为不同环境提供适配版本

### 2. 测试策略
- 定期运行Service Worker兼容性测试
- 在开发新功能时考虑Service Worker环境
- 使用自动化测试检测兼容性问题

### 3. 架构设计
- 将DOM相关功能与核心逻辑分离
- 使用依赖注入模式处理环境差异
- 提供统一的API接口屏蔽环境差异

## 🛠️ 最佳实践

### 1. 环境检测模式
```javascript
// 推荐的环境检测模式
const isServiceWorker = typeof window === 'undefined' && typeof self !== 'undefined';
const isWebPage = typeof window !== 'undefined';
const isNode = typeof global !== 'undefined' && typeof window === 'undefined';

if (isServiceWorker) {
  // Service Worker特定代码
} else if (isWebPage) {
  // 网页环境特定代码
}
```

### 2. 安全的API调用
```javascript
// 安全的API调用模式
const safeAPI = {
  addEventListener: (type, handler) => {
    if (typeof window !== 'undefined') {
      window.addEventListener(type, handler);
    } else if (typeof self !== 'undefined') {
      self.addEventListener(type, handler);
    }
  },
  
  getLocation: () => {
    return typeof window !== 'undefined' ? window.location.href : 'Unknown';
  }
};
```

### 3. 模块化设计
```javascript
// 推荐的模块化设计
class UniversalErrorHandler {
  constructor(environment = 'auto') {
    this.environment = environment === 'auto' ? this.detectEnvironment() : environment;
    this.init();
  }
  
  detectEnvironment() {
    if (typeof window !== 'undefined') return 'web';
    if (typeof self !== 'undefined') return 'worker';
    return 'node';
  }
  
  init() {
    switch (this.environment) {
      case 'web':
        this.initWebEnvironment();
        break;
      case 'worker':
        this.initWorkerEnvironment();
        break;
    }
  }
}
```

## 📝 总结

通过以上修复，Chrome扩展现在完全兼容Service Worker环境，解决了所有相关的JavaScript错误。修复方案：

1. **保持向后兼容**：原有功能在网页环境中正常工作
2. **添加Service Worker支持**：新增专用模块处理Service Worker环境
3. **统一API接口**：提供一致的错误处理接口
4. **完善测试覆盖**：确保在所有环境中都能正常工作

这个修复为Chrome扩展提供了稳定可靠的跨环境支持，为后续功能开发奠定了坚实基础。
