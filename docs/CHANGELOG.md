# 更新日志

## v2.1.0 (2024-07-17) - AI功能重构版本

### 🔄 AI功能重构

#### 功能精简优化
1. **从5个功能精简为4个核心功能**
   - 消除功能重复和用户困惑
   - 提供更清晰的功能定位

2. **功能重组**
   - ✅ 保留：🏷️ 智能标签生成
   - ✅ 保留：📝 内容摘要
   - 🔄 合并：📋 格式整理（结构优化+语言优化+格式整理）
   - ✨ 新增：🌐 中英对照翻译

#### 新增功能：中英对照
1. **智能语言检测**
   - 自动识别中文或英文内容
   - 基于字符比例的准确检测算法

2. **高质量翻译**
   - 生成自然流畅的翻译结果
   - 支持中文→英文和英文→中文双向翻译

3. **对照显示格式**
   - 清晰的原文/译文对照布局
   - 响应式设计：桌面端左右对照，移动端上下对照
   - 支持使用译文或保持对照格式

#### 功能增强：格式整理
1. **三合一综合优化**
   - 结构优化：调整段落结构、改进逻辑顺序
   - 语言优化：修正语法错误、改进用词表达
   - 格式整理：添加标题、段落分隔、列表格式

2. **一次性完成全面优化**
   - 减少用户操作步骤
   - 提供更全面的文本改进

### 🎨 UI/UX 改进

#### 布局优化
1. **2x2网格布局**
   - 从自适应网格改为固定2x2布局
   - 更加整齐美观的视觉效果

2. **视觉区分增强**
   - 每个功能有独特的悬停颜色
   - 🏷️ 智能标签：橙色 (#ff6b35)
   - 📝 内容摘要：青色 (#4ecdc4)
   - 📋 格式整理：蓝色 (#45b7d1)
   - 🌐 中英对照：绿色 (#96ceb4)

3. **交互体验提升**
   - 增强的悬停动画效果
   - 处理中状态的旋转加载动画
   - 更大的按钮尺寸和更好的点击体验

#### 响应式设计
1. **移动端优化**
   - 紧凑的2x2布局
   - 适配小屏幕的按钮尺寸
   - 优化的间距和字体大小

2. **翻译对照适配**
   - 大屏幕：左右对照显示
   - 小屏幕：上下对照显示

### 🔧 技术改进

#### 代码重构
1. **功能模块化**
   - 移除重复的AI功能实现
   - 统一的错误处理和日志记录
   - 更清晰的代码结构

2. **新增核心方法**
   ```javascript
   - translateContent()      // 中英对照翻译
   - detectLanguage()        // 语言检测
   - parseTranslationResult() // 翻译结果解析
   ```

3. **提示词优化**
   - 格式整理功能的综合提示词
   - 中英对照的专业翻译提示词
   - 更准确的AI指令设计

#### 性能优化
1. **减少API调用**
   - 格式整理一次完成多项优化
   - 避免重复的功能调用

2. **更好的错误处理**
   - 翻译结果的多重解析方案
   - 语言检测的容错机制

### 📁 文件变更

#### 修改的文件
- `ai-functions.js` - 重构AI功能实现，新增中英对照
- `src/modules/sidepanel/sidepanel.js` - 更新AI功能调用逻辑
- `src/modules/sidepanel/sidepanel.html` - 新增翻译对照样式，优化布局
- `README.md` - 更新功能说明

#### 新增的文件
- `test-ai-refactor.html` - AI功能重构测试页面
- `AI_REFACTOR_GUIDE.md` - 详细的重构指南

### 🧪 测试验证

#### 功能测试
- ✅ 智能标签：保持所有原有功能
- ✅ 内容摘要：质量和性能一致
- ✅ 格式整理：提供更全面的优化
- ✅ 中英对照：准确的语言检测和翻译

#### UI测试
- ✅ 2x2网格布局正常显示
- ✅ 不同功能的颜色区分清晰
- ✅ 响应式布局适配良好
- ✅ 翻译对照格式美观

#### 兼容性测试
- ✅ 向后兼容，无破坏性变更
- ✅ 与现有功能无冲突
- ✅ 错误处理机制完善

---

## v2.0.0 (2024-07-17) - 富文本与AI功能版本

### ✨ 新增功能

#### 🎨 富文本格式保留功能
1. **HTML格式检测**
   - 自动检测选中内容的HTML格式
   - 识别粗体、斜体、链接、列表、标题等元素
   - 提供格式统计和预览

2. **智能格式转换**
   - HTML到Markdown的高质量转换
   - 支持常见HTML标签的转换
   - 保留链接、图片、表格等复杂格式

3. **格式选择控件**
   - 智能选择：自动判断是否保留格式
   - 保留格式：转换为Markdown格式
   - 纯文本：移除所有格式

#### 🤖 AI智能处理功能
1. **多服务商支持**
   - 硅基流动 (SiliconFlow)
   - OpenRouter
   - DeepSeek
   - Moonshot AI

2. **AI功能模块**
   - 🏷️ 智能标签生成：根据内容自动生成3-8个相关标签
   - 📝 内容摘要：为长文本生成简洁摘要
   - ✨ 结构优化：改进文本结构和可读性
   - 🔤 语言优化：改进语法和表达方式
   - 📋 格式整理：自动整理文档格式和结构

3. **AI配置管理**
   - 服务商选择和模型配置
   - API密钥安全存储
   - 连接测试和验证
   - 智能模型推荐

### 🔧 技术改进

1. **新增模块**
   - `html-to-markdown.js` - HTML到Markdown转换引擎
   - `ai-service-manager.js` - AI服务统一管理
   - `ai-functions.js` - AI功能实现

2. **界面增强**
   - 格式选择区域
   - AI功能面板
   - 结果预览和应用
   - 进度指示和状态反馈

3. **错误处理优化**
   - AI服务错误处理
   - 网络连接检测
   - 用户友好的错误提示
   - 配置验证和引导

### 📁 文件变更

#### 新增文件
- `html-to-markdown.js` - HTML转换引擎
- `ai-service-manager.js` - AI服务管理
- `ai-functions.js` - AI功能实现

#### 修改文件
- `manifest.json` - 添加AI服务域名权限
- `content.js` - 增强内容获取，支持HTML格式
- `background.js` - 支持富文本数据传递
- `src/modules/sidepanel/sidepanel.html` - 新增格式选择和AI功能界面
- `src/modules/sidepanel/sidepanel.js` - 实现格式转换和AI处理逻辑
- `popup.html` - 添加AI配置界面
- `popup.js` - 实现AI配置管理
- `test.html` - 添加富文本和AI功能测试
- `README.md` - 更新功能说明和使用指南

---

## v1.1.1 (2024-07-17) - 错误修复版本

### 🐛 修复的问题

1. **选项页面错误**
   - 修复了 "Could not create an options page" 错误
   - 在 manifest.json 中添加了 `options_page` 配置

2. **侧边栏打开错误**
   - 修复了 "sidePanel.open() may only be called in response to a user gesture" 错误
   - 重构了 background.js 中的事件处理逻辑
   - 确保侧边栏在用户手势的直接响应中打开

3. **图标下载错误**
   - 修复了 "Unable to download all specified images" 错误
   - 创建了有效的 icon.png 文件
   - 在 manifest.json 中添加了完整的图标配置

### 🔧 技术改进

1. **错误处理增强**
   - 改进了 API 配置检查逻辑
   - 添加了更友好的错误提示
   - 增强了侧边栏的状态管理

2. **用户体验优化**
   - 添加了 API 配置引导界面
   - 改进了加载状态和错误反馈
   - 优化了内容持久化机制

3. **代码结构优化**
   - 分离了异步处理逻辑
   - 改进了事件处理流程
   - 增强了错误边界处理

### 📁 文件变更

#### 修改的文件
- `manifest.json` - 添加权限和图标配置
- `background.js` - 重构事件处理逻辑
- `src/modules/sidepanel/sidepanel.js` - 增强错误处理和状态管理
- `README.md` - 更新文档和故障排除指南

#### 新增的文件
- `icon.png` - 扩展图标文件
- `CHANGELOG.md` - 更新日志文件

#### 删除的文件
- `create_icon.html` - 临时文件
- `create_simple_icon.py` - 临时文件
- `generate_icon.js` - 临时文件
- `icon.svg` - 临时文件

### 🧪 测试验证

所有修复都已通过以下测试：

1. ✅ 扩展加载测试 - 无错误信息
2. ✅ 选项页面测试 - 正常打开和保存
3. ✅ 侧边栏功能测试 - 正常打开和操作
4. ✅ 图标显示测试 - 正常显示
5. ✅ 错误处理测试 - 友好的错误提示
6. ✅ 内容保存测试 - 正常保存到 Flomo

### 🚀 部署说明

1. 确保使用 Chrome 114 或更高版本
2. 在 `chrome://extensions/` 中重新加载扩展
3. 测试基本功能确保正常工作
4. 如遇问题请参考 README.md 中的故障排除指南

---

## v1.1.0 (2024-07-17) - 侧边栏功能版本

### ✨ 新功能

1. **侧边栏编辑界面**
   - 替换即时保存为侧边栏编辑模式
   - 支持内容预览和编辑
   - 显示页面元数据信息

2. **增强的用户体验**
   - 响应式设计适配不同屏幕
   - 加载状态和进度提示
   - 完善的错误处理机制

3. **智能内容管理**
   - 内容持久化支持
   - 自动添加来源信息
   - 字符计数和验证

### 🔧 技术实现

1. **Chrome Side Panel API**
   - 使用最新的侧边栏 API
   - 优化用户交互体验

2. **模块化架构**
   - 分离关注点
   - 改进代码组织

3. **安全性增强**
   - HTTPS 强制验证
   - 输入内容验证
   - 请求超时处理

---

## v1.0.0 (初始版本)

### 🎉 基础功能

1. **右键菜单保存**
   - 选择文本后右键保存
   - 直接发送到 Flomo API

2. **设置界面**
   - API 地址配置
   - 多语言支持

3. **基础通知**
   - 成功/失败提示
   - 简单错误处理
