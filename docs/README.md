# 项目文档索引

本目录包含了 Chrome 插件保存到 Flomo 项目的所有文档。

## 📋 文档列表

### 核心指南
- [需求说明.md](./需求说明.md) - 项目需求和功能说明
- [FEATURE_GUIDE.md](./FEATURE_GUIDE.md) - 功能开发指南
- [TESTING_GUIDE.md](./TESTING_GUIDE.md) - 测试指南
- [DEBUG_GUIDE.md](./DEBUG_GUIDE.md) - 调试指南
- [ONBOARDING_GUIDE.md](./ONBOARDING_GUIDE.md) - 新手引导指南

### 技术文档
- [AI_REFACTOR_GUIDE.md](./AI_REFACTOR_GUIDE.md) - AI 功能重构指南
- [ERROR_HANDLING_GUIDE.md](./ERROR_HANDLING_GUIDE.md) - 错误处理指南
- [SERVICE_WORKER_FIX.md](./SERVICE_WORKER_FIX.md) - Service Worker 修复文档

### 更新记录
- [CHANGELOG.md](./CHANGELOG.md) - 变更日志
- [TAGS_FEATURE_UPDATE.md](./TAGS_FEATURE_UPDATE.md) - 标签功能更新记录
- [OPTIMIZATION_SUMMARY.md](./OPTIMIZATION_SUMMARY.md) - 优化总结

### 问题修复
- [ERROR_FIXES.md](./ERROR_FIXES.md) - 错误修复记录
- [POPUP_FIXES_SUMMARY.md](./POPUP_FIXES_SUMMARY.md) - 弹窗修复总结
- [TAGS_FIXES_SUMMARY.md](./TAGS_FIXES_SUMMARY.md) - 标签功能修复总结

## 📁 文档组织结构

```
docs/
├── README.md                    # 本文档索引
├── 需求说明.md                  # 项目需求
├── FEATURE_GUIDE.md            # 功能指南
├── TESTING_GUIDE.md            # 测试指南
├── DEBUG_GUIDE.md              # 调试指南
├── ONBOARDING_GUIDE.md         # 新手引导
├── AI_REFACTOR_GUIDE.md        # AI 重构指南
├── ERROR_HANDLING_GUIDE.md     # 错误处理指南
├── SERVICE_WORKER_FIX.md       # Service Worker 修复
├── CHANGELOG.md                # 变更日志
├── TAGS_FEATURE_UPDATE.md      # 标签功能更新
├── OPTIMIZATION_SUMMARY.md     # 优化总结
├── ERROR_FIXES.md              # 错误修复
├── POPUP_FIXES_SUMMARY.md      # 弹窗修复
└── TAGS_FIXES_SUMMARY.md       # 标签修复
```

## 🔍 如何使用这些文档

1. **新开发者**: 从 `需求说明.md` 和 `ONBOARDING_GUIDE.md` 开始
2. **功能开发**: 参考 `FEATURE_GUIDE.md` 和相关技术文档
3. **问题排查**: 查看 `DEBUG_GUIDE.md` 和相关修复文档
4. **测试相关**: 参考 `TESTING_GUIDE.md`
5. **了解历史**: 查看 `CHANGELOG.md` 和各种更新记录

## 📝 文档维护

- 所有新的文档都应该放在此目录下
- 更新文档时请同步更新本索引文件
- 保持文档命名的一致性和清晰性
