# 新手引导系统说明

## 🎯 功能概述

新手引导系统为 Flomo Chrome 扩展提供了完整的用户引导体验，帮助新用户快速了解和使用扩展的各项功能。

## 📋 引导流程

### 1. 设置页面引导（popup.html）

**触发条件：**
- 首次安装扩展
- 扩展版本更新
- 用户手动触发

**引导步骤：**
1. **欢迎介绍** - 展示扩展的核心功能
2. **Flomo API 配置** - 指导用户配置必要的 API 地址
3. **AI 功能配置** - 介绍可选的 AI 功能配置
4. **保存配置** - 指导完成设置保存
5. **使用指南** - 说明如何在网页上使用扩展

### 2. 侧边栏引导（sidepanel.html）

**触发条件：**
- 首次打开侧边栏编辑界面

**引导步骤：**
1. **编辑界面介绍** - 说明侧边栏的作用
2. **格式选择** - 介绍富文本格式处理选项
3. **AI 智能处理** - 展示 AI 功能面板
4. **内容编辑** - 指导如何编辑内容
5. **保存操作** - 说明保存流程

### 3. 网页引导（content-onboarding.js）

**触发条件：**
- 完成设置引导后首次访问网页
- 用户设置稍后提醒（24小时后）

**引导内容：**
- 如何选择网页文本
- 如何使用右键菜单
- 实际操作演示

## 🔧 技术实现

### 核心组件

1. **OnboardingManager** (`onboarding.js`)
   - 管理设置页面和侧边栏的引导流程
   - 处理引导状态和步骤切换
   - 提供可视化的引导界面

2. **ContentOnboarding** (`content-onboarding.js`)
   - 管理网页内容的引导提示
   - 提供交互式的使用演示
   - 处理引导的显示和隐藏

3. **OnboardingDebug** (`onboarding-debug.js`)
   - 提供开发和测试工具
   - 支持引导状态的重置和调试
   - 快捷键和控制台命令支持

### 状态管理

引导系统使用 Chrome Storage API 管理状态：

```javascript
// 设置引导状态
{
  hasCompletedOnboarding: boolean,    // 是否完成设置引导
  onboardingVersion: string,          // 引导版本号
  onboardingCompletedAt: timestamp    // 完成时间
}

// 网页引导状态
{
  hasSeenWebPageGuide: boolean,       // 是否看过网页引导
  webPageGuideCompletedAt: timestamp, // 完成时间
  webPageGuideRemindAt: timestamp     // 提醒时间
}
```

## 🎨 界面设计

### 设计原则
- **非侵入性** - 不影响用户的正常浏览体验
- **渐进式** - 分步骤逐步介绍功能
- **可跳过** - 用户可以随时跳过或稍后查看
- **视觉友好** - 使用现代化的 UI 设计

### 视觉元素
- **遮罩层** - 突出引导内容
- **高亮框** - 标识目标元素
- **进度指示** - 显示当前步骤
- **动画效果** - 提升用户体验

## 🚀 使用方法

### 用户操作

1. **首次使用**
   - 安装扩展后自动触发设置引导
   - 按照步骤完成基础配置
   - 在网页上体验使用引导

2. **重新查看**
   - 在设置页面点击"重新查看引导"
   - 使用调试工具手动触发

### 开发调试

1. **调试面板**
   - 按 `Ctrl+Shift+O` 打开调试面板
   - 提供重置、启动等调试功能

2. **控制台命令**
   ```javascript
   // 重置所有引导状态
   onboardingDebug.resetAll()
   
   // 启动设置引导
   onboardingDebug.startPopupGuide()
   
   // 启动网页引导
   onboardingDebug.startWebGuide()
   
   // 查看当前状态
   onboardingDebug.showStatus()
   ```

## 📱 响应式支持

引导系统支持不同屏幕尺寸：
- **桌面端** - 完整的引导体验
- **小屏幕** - 自适应布局调整
- **移动端** - 简化的引导流程

## 🔄 版本管理

引导系统支持版本控制：
- 每个版本有独立的引导流程
- 版本更新时可重新触发引导
- 向后兼容旧版本用户

## 🎯 最佳实践

### 内容编写
1. **简洁明了** - 每步说明控制在 50 字以内
2. **操作导向** - 重点说明用户需要做什么
3. **价值突出** - 强调功能的实际价值

### 交互设计
1. **渐进披露** - 不要一次展示太多信息
2. **即时反馈** - 用户操作后立即给出反馈
3. **容错处理** - 处理用户的误操作

### 性能优化
1. **延迟加载** - 引导资源按需加载
2. **内存管理** - 及时清理不需要的 DOM 元素
3. **事件处理** - 避免内存泄漏

## 🐛 故障排除

### 常见问题

1. **引导不显示**
   - 检查存储状态是否正确
   - 确认页面加载完成
   - 查看控制台错误信息

2. **引导卡住**
   - 使用调试工具重置状态
   - 检查目标元素是否存在
   - 刷新页面重新开始

3. **样式异常**
   - 检查 CSS 冲突
   - 确认 z-index 层级
   - 验证响应式适配

### 调试方法

1. **开启调试模式**
   ```javascript
   // 在控制台中启用详细日志
   localStorage.setItem('onboarding-debug', 'true')
   ```

2. **查看状态信息**
   ```javascript
   // 查看当前引导状态
   chrome.storage.sync.get(null, console.log)
   ```

3. **手动控制**
   ```javascript
   // 手动控制引导步骤
   onboardingManager.showStep(2)
   ```

## 📈 数据统计

引导系统可以收集以下数据（可选）：
- 引导完成率
- 各步骤停留时间
- 跳过率统计
- 用户反馈

## 🔮 未来规划

1. **个性化引导** - 根据用户行为定制引导内容
2. **多语言支持** - 支持更多语言的引导
3. **智能提示** - 基于使用情况的智能提示
4. **视频引导** - 集成视频教程
5. **社区反馈** - 收集用户反馈优化引导体验
