# 错误处理系统说明

## 🎯 系统概述

增强的错误处理系统为 Flomo Chrome 扩展提供了全面的错误管理、自动重试、用户友好的错误提示和配置验证功能。

## 🏗️ 系统架构

### 核心组件

1. **ErrorHandler** (`error-handler.js`) - 核心错误处理器
2. **ErrorConfig** (`error-config.js`) - 错误处理配置
3. **NotificationManager** (`notification-manager.js`) - 通知管理系统
4. **错误测试页面** (`error-test.html`) - 测试和调试工具

### 功能模块

- **自动重试机制** - 智能重试失败的网络请求
- **错误分类系统** - 自动识别和分类不同类型的错误
- **用户友好提示** - 将技术错误转换为用户可理解的消息
- **配置验证** - 自动检测和提示配置问题
- **通知系统** - 优雅的错误和状态通知

## 🔧 配置说明

### 重试配置
```javascript
retry: {
  maxAttempts: 3,           // 最大重试次数
  baseDelay: 1000,          // 基础延迟时间(ms)
  maxDelay: 30000,          // 最大延迟时间(ms)
  backoffMultiplier: 2,     // 指数退避倍数
  jitter: true              // 是否添加随机抖动
}
```

### 超时配置
```javascript
timeout: {
  default: 30000,   // 默认超时时间
  ai: 60000,        // AI请求超时时间
  flomo: 15000,     // Flomo保存超时时间
  config: 5000      // 配置验证超时时间
}
```

### 错误分类规则
```javascript
errorCategories: {
  nonRetryable: ['api key', 'unauthorized', '401', '403', '404'],
  authentication: ['api key', 'unauthorized', '401', '403'],
  network: ['network', 'connection', 'fetch', 'cors'],
  timeout: ['timeout', 'abort', 'cancelled'],
  rateLimit: ['429', 'rate limit', 'too many requests'],
  serverError: ['500', '502', '503', '504']
}
```

## 🚀 使用方法

### 基本用法

```javascript
// 使用错误处理器包装网络请求
try {
  const result = await errorHandler.handleNetworkError(
    null,                    // 初始错误(null表示首次尝试)
    'operationName',         // 操作名称
    () => performRequest(),  // 实际执行的函数
    { context: 'data' }      // 上下文信息
  );
  console.log('操作成功:', result);
} catch (error) {
  console.error('操作失败:', error.message);
}
```

### 配置验证

```javascript
// 验证当前配置
const issues = await errorHandler.validateConfiguration();
if (issues.length > 0) {
  errorHandler.showConfigurationIssues(issues);
}
```

### 通知系统

```javascript
// 显示不同类型的通知
notificationManager.success('成功', '操作完成');
notificationManager.error('错误', '操作失败', [
  { id: 'retry', label: '重试', primary: true, handler: () => retry() }
]);
notificationManager.warning('警告', '请检查配置');
notificationManager.info('信息', '正在处理...');
```

## 📊 错误分类

### 不可重试错误
- **认证错误** (401, 403) - API密钥无效或权限不足
- **请求错误** (400, 404) - 请求格式错误或资源不存在
- **配置错误** - API地址格式错误、参数无效等

### 可重试错误
- **网络错误** - 连接失败、DNS解析失败等
- **超时错误** - 请求超时、操作被取消等
- **服务器错误** (500, 502, 503, 504) - 服务器临时不可用
- **频率限制** (429) - 请求过于频繁

## 🔄 重试策略

### 指数退避算法
```
延迟时间 = 基础延迟 × (退避倍数 ^ 重试次数)
```

### 抖动机制
为避免雷群效应，在计算出的延迟时间基础上添加随机抖动：
```
最终延迟 = 计算延迟 ± (计算延迟 × 10% × 随机数)
```

### 重试决策
1. 检查错误类型是否可重试
2. 检查是否达到最大重试次数
3. 计算下次重试的延迟时间
4. 显示重试通知给用户
5. 执行重试或返回最终错误

## 💬 用户友好错误消息

### 消息转换规则

| 技术错误 | 用户友好消息 | 建议操作 |
|---------|-------------|---------|
| `Failed to fetch` | 网络连接失败，请检查网络设置 | 检查网络连接、刷新页面 |
| `HTTP 401` | API密钥无效或已过期 | 检查API配置、更新密钥 |
| `HTTP 429` | 请求过于频繁，请稍后重试 | 等待几分钟、减少操作频率 |
| `HTTP 500` | 服务器错误，请稍后重试 | 稍后重试、切换服务商 |
| `AbortError` | 请求超时，请稍后重试 | 检查网络、稍后重试 |

### 错误消息结构
```javascript
{
  title: '错误标题',
  message: '详细错误描述',
  suggestions: ['建议1', '建议2', '建议3'],
  actions: ['action1', 'action2'],
  originalError: '原始错误信息',
  category: '错误分类'
}
```

## 🔍 配置验证

### 验证项目

1. **Flomo API配置**
   - 检查API地址是否配置
   - 验证URL格式是否正确
   - 确认使用HTTPS协议

2. **AI服务配置**
   - 检查AI服务商选择
   - 验证API密钥是否配置
   - 确认模型配置正确

3. **存储权限**
   - 测试本地存储访问
   - 验证同步存储权限

4. **网络权限**
   - 检查扩展权限配置
   - 验证域名访问权限

### 问题等级

- **错误级别** - 阻止功能正常使用的问题
- **警告级别** - 可能影响体验但不阻止使用的问题

## 📈 监控和统计

### 错误统计
```javascript
const stats = errorHandler.getErrorStats();
console.log('错误统计:', {
  total: stats.total,           // 总错误数
  recent: stats.recent,         // 最近一小时错误数
  byType: stats.byType,         // 按类型分组
  retryOperations: stats.retryOperations  // 当前重试操作
});
```

### 错误历史
- 自动记录所有错误信息
- 包含错误堆栈、上下文信息
- 限制历史记录大小避免内存泄漏

## 🧪 测试和调试

### 错误测试页面
访问 `error-test.html` 可以：
- 模拟各种错误情况
- 测试重试机制
- 验证错误消息转换
- 查看错误统计信息

### 调试工具
```javascript
// 重置错误历史
errorHandler.clearErrorHistory();

// 查看当前配置
console.log(errorHandler.config);

// 手动触发配置验证
errorHandler.validateConfiguration().then(console.log);

// 测试错误分析
const analysis = errorHandler.analyzeNetworkError(new Error('test'));
console.log(analysis);
```

## 🔧 开发模式

### 启用开发模式
```javascript
ErrorConfig.development.enabled = true;
```

### 开发模式功能
- 详细的调试日志
- 跳过重试延迟（可选）
- 显示调试信息
- 模拟错误功能

## 📱 移动端适配

### 响应式通知
- 小屏幕设备自动调整通知布局
- 触摸友好的交互设计
- 适配不同屏幕尺寸

### 性能优化
- 限制同时显示的通知数量
- 自动清理过期的错误记录
- 优化动画性能

## 🔒 安全考虑

### 敏感信息保护
- 错误日志中不记录API密钥
- 用户数据不上传到外部服务
- 本地存储加密敏感配置

### 隐私保护
- 错误统计仅本地存储
- 不收集用户个人信息
- 遵循最小权限原则

## 🚀 最佳实践

### 错误处理
1. 总是使用错误处理器包装网络请求
2. 提供有意义的操作名称和上下文
3. 根据错误类型选择合适的处理策略
4. 给用户提供明确的解决建议

### 配置管理
1. 定期验证配置有效性
2. 提供配置修复的快捷方式
3. 在配置变更后重新验证
4. 备份重要配置信息

### 用户体验
1. 错误消息要简洁明了
2. 提供具体的解决步骤
3. 避免技术术语
4. 及时反馈操作状态

## 🔮 未来规划

1. **智能错误预测** - 基于历史数据预测可能的错误
2. **自动配置修复** - 自动检测并修复常见配置问题
3. **错误报告系统** - 可选的错误报告和分析功能
4. **多语言支持** - 支持更多语言的错误消息
5. **离线模式** - 网络断开时的降级处理
