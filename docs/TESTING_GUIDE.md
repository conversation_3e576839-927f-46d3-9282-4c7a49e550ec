# 单元测试指南

## 🎯 测试系统概述

本项目实现了完整的单元测试系统，包括自定义测试框架、测试覆盖率分析和自动化测试运行器。

## 🏗️ 测试架构

### 核心组件

1. **TestFramework** (`test-framework.js`) - 轻量级测试框架
2. **TestConfig** (`test-config.js`) - 测试配置和工具函数
3. **TestCoverage** (`test-coverage.js`) - 覆盖率分析工具
4. **TestRunner** (`test-runner.html`) - 可视化测试运行器

### 测试文件结构
```
tests/
├── error-handler.test.js      # 错误处理器测试
├── ai-service-manager.test.js # AI服务管理器测试
├── ai-functions.test.js       # AI功能测试
├── html-to-markdown.test.js   # HTML转换器测试
└── notification-manager.test.js # 通知管理器测试
```

## 🧪 测试框架特性

### 基本语法
```javascript
describe('测试套件名称', () => {
  beforeEach(() => {
    // 每个测试前执行
  });

  afterEach(() => {
    // 每个测试后执行
  });

  it('应该执行某个功能', () => {
    const result = someFunction();
    expect(result).toBe(expectedValue);
  });

  xit('跳过的测试', () => {
    // 这个测试会被跳过
  });
});
```

### 支持的断言
```javascript
expect(actual).toBe(expected)              // 严格相等
expect(actual).toEqual(expected)           // 深度相等
expect(actual).toBeTruthy()                // 真值
expect(actual).toBeFalsy()                 // 假值
expect(actual).toBeNull()                  // null
expect(actual).toBeUndefined()             // undefined
expect(actual).toContain(item)             // 包含元素
expect(actual).toThrow(errorMessage)       // 抛出异常
expect(actual).toHaveProperty(property)    // 有属性
expect(actual).toHaveLength(length)        // 长度
expect(actual).toBeValidHTML()             // 有效HTML (自定义)
expect(actual).toBeValidMarkdown()         // 有效Markdown (自定义)
expect(actual).toBeValidURL()              // 有效URL (自定义)
```

### 钩子函数
```javascript
beforeAll(() => {})    // 所有测试前执行一次
afterAll(() => {})     // 所有测试后执行一次
beforeEach(() => {})   // 每个测试前执行
afterEach(() => {})    // 每个测试后执行
```

## 🔧 测试工具和模拟

### Chrome API 模拟
```javascript
const mockChrome = TestConfig.utils.mockChromeAPI({
  storageData: {
    flomoApiUrl: 'https://test.com/api',
    aiProvider: 'siliconflow'
  }
});

global.chrome = mockChrome;
```

### 网络请求模拟
```javascript
global.fetch = TestConfig.utils.mockFetch({
  choices: [{ message: { content: 'AI响应' } }]
}, {
  delay: 100,
  shouldFail: false,
  status: 200
});
```

### Spy 函数
```javascript
const spy = testFramework.createSpy();
const mockObj = testFramework.createMock({
  method1: () => 'original',
  method2: () => 'original'
});

// 验证调用
TestConfig.utils.expectCalled(spy, 2);
TestConfig.utils.expectCalledWith(spy, 'arg1', 'arg2');
```

## 📊 测试覆盖率

### 覆盖率类型
- **函数覆盖率** - 被调用的函数比例
- **行覆盖率** - 被执行的代码行比例
- **分支覆盖率** - 被执行的分支比例

### 覆盖率标准
- **高覆盖率** (≥80%) - 绿色显示
- **中等覆盖率** (60-79%) - 黄色显示
- **低覆盖率** (<60%) - 红色显示

### 生成覆盖率报告
```javascript
// 生成JSON报告
const report = testCoverage.generateReport();

// 导出HTML报告
testCoverage.exportReport('html');

// 重置覆盖率数据
testCoverage.reset();
```

## 🚀 运行测试

### 使用测试运行器
1. 打开 `test-runner.html`
2. 点击"运行所有测试"或选择特定测试套件
3. 查看测试结果和覆盖率报告

### 命令行运行（如果有Node.js环境）
```bash
# 运行所有测试
node test-runner.js

# 运行特定测试文件
node tests/error-handler.test.js
```

## 📝 编写测试的最佳实践

### 1. 测试命名
```javascript
// ✅ 好的命名
it('应该在API密钥无效时抛出错误', () => {});

// ❌ 不好的命名
it('测试错误', () => {});
```

### 2. 测试结构 (AAA模式)
```javascript
it('应该计算正确的延迟时间', () => {
  // Arrange - 准备
  const baseDelay = 100;
  const attempt = 2;
  
  // Act - 执行
  const delay = calculateDelay(baseDelay, attempt);
  
  // Assert - 断言
  expect(delay).toBe(400);
});
```

### 3. 测试隔离
```javascript
describe('UserService', () => {
  let userService;
  
  beforeEach(() => {
    // 每个测试都使用新的实例
    userService = new UserService();
  });
  
  afterEach(() => {
    // 清理副作用
    TestConfig.utils.cleanupDOM();
  });
});
```

### 4. 边界条件测试
```javascript
describe('输入验证', () => {
  it('应该处理空字符串', () => {
    expect(() => validate('')).toThrow('不能为空');
  });
  
  it('应该处理null值', () => {
    expect(() => validate(null)).toThrow('不能为空');
  });
  
  it('应该处理超长字符串', () => {
    const longString = 'a'.repeat(10000);
    expect(() => validate(longString)).toThrow('过长');
  });
});
```

### 5. 异步测试
```javascript
it('应该处理异步操作', async () => {
  const result = await asyncFunction();
  expect(result).toBe('expected');
});

it('应该处理Promise拒绝', async () => {
  try {
    await failingAsyncFunction();
    expect(true).toBe(false); // 不应该到达这里
  } catch (error) {
    expect(error.message).toContain('expected error');
  }
});
```

## 🔍 调试测试

### 1. 使用控制台输出
```javascript
it('调试测试', () => {
  const result = complexFunction();
  console.log('调试信息:', result);
  expect(result).toBeTruthy();
});
```

### 2. 使用断点
在浏览器开发者工具中设置断点，逐步调试测试代码。

### 3. 跳过测试
```javascript
xit('暂时跳过的测试', () => {
  // 这个测试会被跳过
});
```

## 📈 性能测试

### 测量执行时间
```javascript
it('应该快速执行', async () => {
  const performance = await TestConfig.utils.measurePerformance(
    () => expensiveFunction(),
    10 // 执行10次
  );
  
  expect(performance.avg).toBeLessThan(100); // 平均时间小于100ms
});
```

## 🐛 常见问题和解决方案

### 1. 异步测试超时
```javascript
// 增加超时时间
it('长时间运行的测试', async () => {
  // 测试代码
}, 10000); // 10秒超时
```

### 2. DOM 清理
```javascript
afterEach(() => {
  // 清理测试创建的DOM元素
  TestConfig.utils.cleanupDOM();
});
```

### 3. 模拟函数未被调用
```javascript
it('应该调用模拟函数', () => {
  const mockFn = testFramework.createSpy();
  
  // 执行代码
  someFunction(mockFn);
  
  // 验证调用
  expect(mockFn._calls.length).toBeGreaterThan(0);
});
```

## 📊 测试报告

### HTML 报告包含
- 测试结果汇总
- 详细的测试用例结果
- 错误信息和堆栈跟踪
- 执行时间统计
- 覆盖率可视化

### 覆盖率报告包含
- 整体覆盖率统计
- 文件级别覆盖率
- 函数级别覆盖率
- 未覆盖代码标识

## 🔮 未来改进

1. **集成测试** - 添加组件间集成测试
2. **E2E测试** - 端到端用户流程测试
3. **性能基准** - 性能回归检测
4. **自动化CI** - 持续集成自动测试
5. **测试数据生成** - 自动生成测试数据
6. **快照测试** - UI组件快照对比

## 📚 参考资源

- [Jest文档](https://jestjs.io/docs/getting-started) - 主流测试框架参考
- [测试最佳实践](https://github.com/goldbergyoni/javascript-testing-best-practices)
- [Chrome扩展测试指南](https://developer.chrome.com/docs/extensions/mv3/tut_debugging/)

通过完善的测试系统，我们确保了代码质量和功能稳定性，为持续开发和维护提供了坚实的基础。
