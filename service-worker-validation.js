// Service Worker环境验证脚本
// 用于检查所有必要的变量和函数是否已正确定义

console.log('🔍 开始Service Worker环境验证...');

// 检查全局变量是否已定义
const requiredGlobals = [
  'ErrorConfig',
  'inputValidator',
  'secureStorage', 
  'SecureStorage',
  'requestCache',
  'RequestCache',
  'notificationManager',
  'aiServiceManager',
  'AIServiceManager',
  'errorHandler'
];

let allDefined = true;
const results = [];

requiredGlobals.forEach(varName => {
  const isDefined = typeof self[varName] !== 'undefined';
  results.push({
    name: varName,
    defined: isDefined,
    type: isDefined ? typeof self[varName] : 'undefined'
  });
  
  if (!isDefined) {
    allDefined = false;
    console.error(`❌ ${varName} 未定义`);
  } else {
    console.log(`✅ ${varName} 已定义 (${typeof self[varName]})`);
  }
});

// 检查关键方法是否存在
const methodChecks = [
  { obj: 'inputValidator', method: 'sanitizeInput' },
  { obj: 'secureStorage', method: 'setApiKey' },
  { obj: 'requestCache', method: 'get' },
  { obj: 'notificationManager', method: 'showSuccessChrome' },
  { obj: 'aiServiceManager', method: 'getProviders' },
  { obj: 'errorHandler', method: 'handleError' }
];

methodChecks.forEach(check => {
  if (typeof self[check.obj] !== 'undefined' && 
      typeof self[check.obj][check.method] === 'function') {
    console.log(`✅ ${check.obj}.${check.method}() 方法可用`);
  } else {
    console.error(`❌ ${check.obj}.${check.method}() 方法不可用`);
    allDefined = false;
  }
});

// 检查Chrome API是否可用
const chromeAPIs = [
  'chrome.runtime',
  'chrome.storage',
  'chrome.notifications',
  'chrome.contextMenus',
  'chrome.sidePanel'
];

chromeAPIs.forEach(api => {
  const parts = api.split('.');
  let obj = self;
  let available = true;
  
  for (const part of parts) {
    if (obj && typeof obj[part] !== 'undefined') {
      obj = obj[part];
    } else {
      available = false;
      break;
    }
  }
  
  if (available) {
    console.log(`✅ ${api} API 可用`);
  } else {
    console.warn(`⚠️ ${api} API 不可用`);
  }
});

// 输出验证结果
console.log('\n📊 验证结果汇总:');
console.table(results);

if (allDefined) {
  console.log('🎉 所有必要的变量和方法都已正确定义！');
  console.log('✅ Service Worker 环境验证通过');
} else {
  console.error('❌ Service Worker 环境验证失败');
  console.error('请检查上述未定义的变量和方法');
}

// 导出验证结果
self.serviceWorkerValidation = {
  allDefined,
  results,
  timestamp: new Date().toISOString()
};
