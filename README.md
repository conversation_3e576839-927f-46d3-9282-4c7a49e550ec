# Save to Flomo Chrome Extension

一个简单易用的 Chrome 扩展，让您可以快速将网页上的文本内容保存到 Flomo。

## ✨ 新功能：侧边栏编辑

### 🎯 功能特点

#### 📝 核心功能
- **侧边栏编辑**：选择文本后打开侧边栏进行编辑，而不是立即保存
- **内容预览**：在保存前可以预览和修改选中的内容
- **页面信息**：自动显示来源页面的标题和URL
- **智能保存**：自动添加来源信息到保存的内容中

#### 🎨 富文本格式支持
- **格式保留**：自动检测并保留HTML格式（粗体、斜体、链接、列表等）
- **Markdown转换**：将HTML格式智能转换为Markdown格式
- **格式选择**：用户可选择保留格式、转为纯文本或智能选择
- **格式预览**：显示检测到的格式类型和数量

#### 🤖 AI智能处理（4个核心功能）
- **多服务商支持**：集成硅基流动、OpenRouter、DeepSeek、Moonshot AI等
- **🏷️ 智能标签生成**：根据内容自动生成相关标签
  - 可点击标签：每个生成的标签都可以单独点击添加
  - 智能去重：自动检测已存在标签，避免重复添加
  - 批量添加：一键添加所有未添加的标签
  - 视觉反馈：已添加标签变灰显示，实时计数
- **📝 内容摘要**：为长文本生成简洁摘要
- **📋 格式整理**：全面优化文本（结构+语言+格式三合一）
  - 结构优化：调整段落结构、改进逻辑顺序
  - 语言优化：修正语法错误、改进用词表达
  - 格式整理：添加标题、段落分隔、列表格式
- **🌐 中英对照**：智能翻译并提供中英文对照
  - 自动语言检测：智能识别中文或英文内容
  - 高质量翻译：生成自然流畅的翻译结果
  - 对照显示：清晰的左右或上下对照布局
  - 灵活应用：可选择使用译文或保持对照格式

#### 🔧 技术特性
- **状态管理**：支持内容持久化，切换标签页不会丢失编辑内容
- **加载状态**：保存和AI处理过程中显示加载动画和状态提示
- **错误处理**：完善的错误提示和处理机制
- **多语言**：支持中英文界面
- **响应式设计**：适配不同屏幕尺寸

### 🚀 使用方法

1. **安装扩展**
   - 在 Chrome 中加载解压的扩展程序
   - 确保扩展已启用

2. **基础配置**
   - 点击扩展图标打开设置页面
   - 输入您的 Flomo API 地址
   - 点击保存

3. **AI功能配置（可选）**
   - 在设置页面选择AI服务提供商
   - 选择合适的AI模型
   - 输入对应的API密钥
   - 点击"测试连接"验证配置
   - 保存设置

4. **保存内容**
   - 在任意网页上选择要保存的文本
   - 右键点击选中的文本
   - 选择"保存到 Flomo"
   - 在打开的侧边栏中：
     - 选择格式类型（智能选择/保留格式/纯文本）
     - 编辑内容
     - 可选使用AI功能优化内容
     - 点击"保存到 Flomo"按钮

### 🎮 快捷键

- `Ctrl/Cmd + S`：保存内容到 Flomo
- `Escape`：取消编辑并关闭侧边栏

### 📁 文件结构

```
chrome-plugin-save-to-flomo/
├── manifest.json          # 扩展清单文件
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── popup.html            # 设置页面HTML
├── popup.js              # 设置页面JavaScript
├── src/                   # 源代码目录
│   ├── sidepanel.css     # 侧边栏样式文件
│   └── modules/          # 模块目录
│       └── sidepanel/     # 侧边栏模块目录
│           ├── sidepanel.html # 侧边栏HTML
│           ├── sidepanel.js      # 侧边栏主模块
│           ├── ui-renderer.js    # UI渲染模块
│           ├── event-handler.js  # 事件处理模块
│           ├── ai-service.js     # AI服务模块
│           └── data-manager.js    # 数据管理模块
│           ├── ui-renderer.js # UI渲染模块
│           ├── event-handler.js # 事件处理模块
│           ├── ai-service.js  # AI服务模块
│           └── data-manager.js # 数据管理模块
├── html-to-markdown.js   # HTML到Markdown转换引擎
├── ai-service-manager.js # AI服务管理模块
├── ai-functions.js       # AI功能实现
├── icon.png              # 扩展图标
├── test.html             # 测试页面
├── docs/                 # 📚 项目文档目录
│   ├── README.md         # 文档索引
│   ├── 需求说明.md       # 项目需求说明
│   ├── CHANGELOG.md      # 更新日志
│   ├── FEATURE_GUIDE.md  # 功能开发指南
│   ├── TESTING_GUIDE.md  # 测试指南
│   └── ...               # 其他技术文档
└── README.md             # 项目说明文档
```

### 🔧 技术实现

#### 核心功能

1. **Context Menu API**：右键菜单集成
2. **Side Panel API**：Chrome 114+ 的侧边栏功能
3. **Storage API**：配置和临时数据存储
4. **Scripting API**：页面信息获取
5. **Notifications API**：操作结果通知

#### 数据流程

```
用户选择文本 → 右键菜单 → 获取页面信息 → 存储临时数据 → 打开侧边栏 → 编辑内容 → 保存到 Flomo
```

#### 错误处理

- API 配置验证
- 网络请求超时处理
- 内容格式验证
- 用户友好的错误提示

### 🛠️ 开发和测试

#### 本地开发

1. 克隆或下载项目
2. 在 Chrome 中打开 `chrome://extensions/`
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹

#### 测试

1. 打开 `test.html` 文件
2. 按照页面说明进行功能测试
3. 测试各种文本内容和错误场景

### 📋 权限说明

- `contextMenus`：创建右键菜单
- `storage`：存储配置和临时数据
- `activeTab`：获取当前标签页信息
- `scripting`：执行内容脚本
- `sidePanel`：使用侧边栏功能
- `notifications`：显示操作结果通知
- `alarms`：定期清理过期数据

### 🔒 隐私和安全

- 所有数据仅在本地存储，不会上传到第三方服务器
- API 请求直接发送到用户配置的 Flomo 地址
- 临时数据会在1小时后自动清理
- 支持 HTTPS 协议确保传输安全

### 🔧 修复的问题

#### v1.1.1 修复内容
- ✅ 修复了 "Could not create an options page" 错误
- ✅ 修复了 "sidePanel.open() may only be called in response to a user gesture" 错误
- ✅ 修复了 "Unable to download all specified images" 错误
- ✅ 改进了错误处理和用户体验

### 🧪 测试步骤

#### 基本功能测试
1. **加载扩展**
   - 在 Chrome 中打开 `chrome://extensions/`
   - 启用开发者模式
   - 点击"加载已解压的扩展程序"
   - 选择项目文件夹

2. **配置测试**
   - 点击扩展图标
   - 输入 Flomo API 地址
   - 点击保存，确认无错误

3. **侧边栏功能测试**
   - 打开 `test.html` 文件
   - 选择任意文本
   - 右键选择"保存到 Flomo"
   - 确认侧边栏正常打开
   - 验证内容可编辑
   - 测试保存和取消功能

### 🐛 故障排除

#### 常见问题

1. **右键菜单没有显示**
   - 确保已选择文本
   - 检查扩展是否已启用
   - 刷新页面后重试

2. **侧边栏无法打开**
   - 确保使用 Chrome 114 或更高版本
   - 检查扩展权限设置
   - 重新加载扩展

3. **保存失败**
   - 检查 API 地址配置是否正确
   - 确保网络连接正常
   - 验证 Flomo API 地址的有效性
   - 检查控制台是否有错误信息

4. **内容丢失**
   - 临时内容会在1小时后自动清理
   - 建议及时保存编辑的内容
   - 检查浏览器存储权限

### 📝 更新日志

#### v2.0.0 (当前版本) - 富文本与AI功能
- ✨ 新增富文本格式保留功能
  - HTML到Markdown智能转换
  - 格式检测和预览
  - 用户可选择格式类型
- 🤖 集成AI智能处理功能
  - 支持多个AI服务提供商
  - 智能标签生成
  - 内容摘要和优化
  - 语言改进和格式整理
- 🎨 增强用户界面
  - 格式选择控件
  - AI功能面板
  - 结果预览和应用
- 🔧 完善错误处理和用户反馈

#### v1.1.1 - 错误修复版本
- 🐛 修复选项页面错误
- 🐛 修复侧边栏打开错误
- 🐛 修复图标下载错误
- 🔧 改进错误处理机制

#### v1.1.0 - 侧边栏功能版本
- ✨ 新增侧边栏编辑功能
- 📱 优化响应式设计
- 🚀 提升用户体验
- 🔒 增强安全性验证

#### v1.0.0 - 基础版本
- 🎉 基础功能实现
- 📝 右键菜单保存
- ⚙️ 设置页面
- 🌐 多语言支持

### 📚 文档

更多详细文档请查看 [docs 目录](./docs/)，包括：
- 项目需求说明
- 功能开发指南
- 测试指南
- 调试指南
- 技术文档
- 问题修复记录

### 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个扩展！

### 📄 许可证

MIT License
