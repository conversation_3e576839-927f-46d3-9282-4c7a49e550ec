// 错误处理配置
const ErrorConfig = {
  // 重试配置
  retry: {
    maxAttempts: 3,
    baseDelay: 1000, // 1秒
    maxDelay: 30000, // 30秒
    backoffMultiplier: 2,
    jitter: true // 添加随机延迟避免雷群效应
  },

  // 超时配置
  timeout: {
    default: 30000, // 30秒
    ai: 60000, // AI请求60秒
    flomo: 15000, // Flomo保存15秒
    config: 5000 // 配置验证5秒
  },

  // 错误分类规则
  errorCategories: {
    // 不可重试的错误关键词
    nonRetryable: [
      'api key', 'unauthorized', '401', 'forbidden', '403',
      'not found', '404', 'bad request', '400',
      'invalid', 'malformed', 'syntax error', 'parse error',
      'quota exceeded', 'billing', 'payment'
    ],
    
    // 认证相关错误
    authentication: [
      'api key', 'unauthorized', '401', 'forbidden', '403',
      'invalid key', 'expired', 'authentication failed'
    ],
    
    // 网络相关错误
    network: [
      'network', 'connection', 'fetch', 'cors',
      'dns', 'resolve', 'unreachable'
    ],
    
    // 超时相关错误
    timeout: [
      'timeout', 'abort', 'cancelled', 'deadline'
    ],
    
    // 频率限制错误
    rateLimit: [
      '429', 'rate limit', 'too many requests',
      'quota', 'throttle', 'limit exceeded'
    ],
    
    // 服务器错误
    serverError: [
      '500', '502', '503', '504',
      'server error', 'internal error',
      'service unavailable', 'bad gateway'
    ]
  },

  // 用户友好错误消息模板
  friendlyMessages: {
    authentication_or_config: {
      title: '配置错误',
      message: '请检查您的API配置是否正确',
      suggestions: [
        '确认API地址格式正确',
        '检查API密钥是否有效',
        '验证服务商和模型选择',
        '查看API文档确认参数格式'
      ],
      actions: ['open_settings', 'check_docs']
    },
    
    timeout: {
      title: '请求超时',
      message: '网络响应超时，请稍后重试',
      suggestions: [
        '检查网络连接状态',
        '尝试切换网络环境',
        '稍后再试',
        '检查防火墙设置'
      ],
      actions: ['retry', 'check_network']
    },
    
    rate_limit: {
      title: '请求频率限制',
      message: '请求过于频繁，请稍后重试',
      suggestions: [
        '等待几分钟后重试',
        '减少同时进行的操作',
        '检查API配额使用情况',
        '考虑升级API套餐'
      ],
      actions: ['wait_retry', 'check_quota']
    },
    
    server_error: {
      title: '服务器错误',
      message: 'AI服务暂时不可用，请稍后重试',
      suggestions: [
        '稍后重试',
        '尝试切换其他AI服务商',
        '检查服务商状态页面',
        '联系服务商技术支持'
      ],
      actions: ['retry_later', 'switch_provider', 'check_status']
    },
    
    network: {
      title: '网络连接问题',
      message: '网络连接失败，请检查网络设置',
      suggestions: [
        '检查网络连接',
        '尝试刷新页面',
        '检查防火墙设置',
        '尝试使用VPN'
      ],
      actions: ['check_connection', 'refresh', 'check_firewall']
    },
    
    unknown: {
      title: '未知错误',
      message: '操作失败，请重试',
      suggestions: [
        '刷新页面重试',
        '检查浏览器控制台',
        '尝试重启浏览器',
        '联系技术支持'
      ],
      actions: ['refresh', 'check_console', 'contact_support']
    }
  },

  // 通知配置
  notifications: {
    showSuccess: true,
    showRetry: true,
    showError: true,
    autoHideDelay: 5000, // 5秒后自动隐藏
    maxVisible: 3 // 最多同时显示3个通知
  },

  // 日志配置
  logging: {
    enabled: true,
    level: 'info', // 'debug', 'info', 'warn', 'error'
    maxHistorySize: 100,
    includeStack: true,
    includeContext: true
  },

  // 监控配置
  monitoring: {
    enabled: true,
    reportInterval: 300000, // 5分钟
    maxReportSize: 50,
    includeUserAgent: true,
    includeTimestamp: true
  },

  // 开发模式配置
  development: {
    enabled: false, // 在生产环境中设为false
    verboseLogging: true,
    showDebugInfo: true,
    skipRetryDelay: false,
    mockErrors: false
  }
};

// 根据环境调整配置
if (typeof chrome !== 'undefined' && chrome.runtime) {
  // 检查是否为开发模式
  chrome.management.getSelf((info) => {
    if (info.installType === 'development') {
      ErrorConfig.development.enabled = true;
      ErrorConfig.logging.level = 'debug';
      console.log('🔧 错误处理器运行在开发模式');
    }
  });
}

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ErrorConfig;
} else if (typeof window !== 'undefined') {
  window.ErrorConfig = ErrorConfig;
}
