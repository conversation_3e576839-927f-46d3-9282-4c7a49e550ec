<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Save to Flomo - 设置</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            border-bottom: 2px solid #007aff;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .section h2 {
            margin-top: 0;
            color: #333;
            font-size: 18px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        input[type="text"],
        input[type="url"],
        select,
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn.success {
            background: #28a745;
        }

        .btn.danger {
            background: #dc3545;
        }

        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .help-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .info-section {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .info-section ul {
            margin: 12px 0;
            padding-left: 20px;
        }

        .info-section li {
            margin: 8px 0;
            line-height: 1.5;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 0;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>⚙️ Save to Flomo 设置</h1>

        <div class="section">
            <h2>🔗 Flomo 配置</h2>
            <div class="form-group">
                <label for="flomo-api-url">Flomo API URL</label>
                <input type="url" id="flomo-api-url" placeholder="https://flomoapp.com/mine/api">
                <div class="help-text">在 Flomo 设置页面获取您的专属 API 地址</div>
            </div>
        </div>

        <div class="section">
            <h2>🤖 AI 智能处理</h2>
            <div class="info-section">
                <p>✅ AI 功能已预配置并可直接使用：</p>
                <ul>
                    <li><strong>🏷️ 智能标签生成</strong> - 自动为内容生成相关标签</li>
                    <li><strong>📝 内容摘要</strong> - 生成简洁的内容摘要</li>
                    <li><strong>📋 格式整理</strong> - 优化文本结构和格式</li>
                    <li><strong>🌐 中英对照翻译</strong> - 智能翻译并提供对照</li>
                </ul>
                <p>这些功能在侧边栏中可直接使用，无需额外配置。</p>
            </div>
        </div>

        <div class="section">
            <h2>🎯 功能设置</h2>
            <div class="checkbox-group">
                <input type="checkbox" id="auto-ai-tags">
                <label for="auto-ai-tags">自动生成 AI 标签</label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="show-notifications">
                <label for="show-notifications">显示通知</label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="enable-onboarding">
                <label for="enable-onboarding">启用新手引导</label>
            </div>
        </div>

        <div class="section">
            <h2>🚀 性能设置</h2>
            <div class="form-group">
                <label for="performance-mode">性能模式</label>
                <select id="performance-mode">
                    <option value="balanced">平衡模式</option>
                    <option value="performance">性能模式</option>
                    <option value="battery">省电模式</option>
                </select>
                <div class="help-text">性能模式影响缓存策略和响应速度</div>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="enable-cache">
                <label for="enable-cache">启用智能缓存</label>
            </div>
        </div>

        <div class="section">
            <h2>📊 数据管理</h2>
            <button id="export-settings-btn" class="btn">导出设置</button>
            <button id="import-settings-btn" class="btn">导入设置</button>
            <button id="reset-settings-btn" class="btn danger">重置所有设置</button>
            <input type="file" id="import-file" accept=".json" style="display: none;">
        </div>

        <div class="section">
            <h2>ℹ️ 关于</h2>
            <p>Save to Flomo Chrome 扩展</p>
            <p>版本：<span id="version">1.0.0</span></p>
            <p>
                <a href="https://github.com/your-repo" target="_blank">GitHub</a> |
                <a href="mailto:<EMAIL>">反馈问题</a>
            </p>
        </div>

        <div class="status" id="status"></div>

        <div style="text-align: center; margin-top: 30px;">
            <button id="save-btn" class="btn success">保存设置</button>
            <button id="cancel-btn" class="btn">取消</button>
        </div>
    </div>

    <script src="options.js"></script>
</body>

</html>