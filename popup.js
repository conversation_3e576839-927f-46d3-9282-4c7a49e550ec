// 简化版 popup.js - 移除 AI 配置，只保留 Flomo 配置
document.addEventListener('DOMContentLoaded', () => {
  // 多语言支持
  const locales = {
    en: {
      settingsTitle: "flomo Settings",
      apiUrlLabel: "Your flomo API URL",
      apiUrlPlaceholder: "e.g., https://flomoapp.com/api/...",
      saveButton: "Save",
      savedSuccess: "Settings saved successfully!",
      invalidUrl: "Please enter a valid HTTPS URL.",
      emptyUrl: "API URL cannot be empty.",
      aiConfigInfo: "AI features are pre-configured and ready to use."
    },
    zh: {
      settingsTitle: "flomo 设置",
      apiUrlLabel: "你的 flomo API 地址",
      apiUrlPlaceholder: "例如: https://flomoapp.com/api/...",
      saveButton: "保 存",
      savedSuccess: "设置已成功保存！",
      invalidUrl: "请输入一个有效的 HTTPS 网址。",
      emptyUrl: "API 地址不能为空。",
      aiConfigInfo: "AI 功能已预配置，可直接使用。"
    }
  };

  const lang = navigator.language.startsWith('zh') ? 'zh' : 'en';
  const t = (key) => locales[lang][key];

  // 应用翻译
  const applyTranslations = () => {
    document.querySelectorAll('[data-i18n]').forEach(el => {
      const key = el.getAttribute('data-i18n');
      el.textContent = t(key);
    });
    document.querySelectorAll('[data-i18n-placeholder]').forEach(el => {
      const key = el.getAttribute('data-i18n-placeholder');
      el.placeholder = t(key);
    });
    document.title = t('settingsTitle');
  };

  // DOM 元素
  const apiUrlInput = document.getElementById('api-url');
  const saveButton = document.getElementById('save-button');
  const statusMessage = document.getElementById('status-message');
  let statusTimeout;

  // URL 验证
  const isValidUrl = (string) => {
    try {
      const url = new URL(string);
      return url.protocol === "https:";
    } catch (_) {
      return false;
    }
  };

  // 显示状态消息
  const showStatus = (message, type) => {
    if (statusTimeout) clearTimeout(statusTimeout);
    
    statusMessage.textContent = message;
    statusMessage.className = `status ${type}`;
    statusMessage.style.display = 'block';
    
    if (type === 'success') {
      statusTimeout = setTimeout(() => {
        statusMessage.style.display = 'none';
      }, 3000);
    }
  };

  // 初始化页面
  async function initializePage() {
    console.log('🚀 开始初始化页面...');
    loadSavedSettings();
  }

  // 加载保存的设置
  async function loadSavedSettings() {
    console.log('🔄 开始加载保存的设置...');

    try {
      const data = await new Promise(resolve => {
        chrome.storage.sync.get(['flomoConfig', 'flomoApiUrl'], resolve);
      });

      console.log('📥 获取到保存的设置:', data);

      // 优先使用新结构，如果不存在则尝试旧键名
      if (data.flomoConfig && data.flomoConfig.apiUrl) {
        apiUrlInput.value = data.flomoConfig.apiUrl;
      } else if (data.flomoApiUrl) {
        apiUrlInput.value = data.flomoApiUrl;
        
        // 将旧格式迁移到新格式
        await new Promise(resolve => {
          chrome.storage.sync.set({
            flomoConfig: { apiUrl: data.flomoApiUrl }
          }, resolve);
        });
        console.log('✅ 已将旧格式配置迁移到新格式');
      }

      console.log('✅ 设置加载完成');
    } catch (error) {
      console.error('❌ 加载设置失败:', error);
    }
  }

  // 启动初始化
  initializePage();

  // 保存设置
  saveButton.addEventListener('click', async () => {
    const apiUrl = apiUrlInput.value.trim();

    // 验证输入
    if (!apiUrl) {
      showStatus(t('emptyUrl'), 'error');
      return;
    }

    if (!isValidUrl(apiUrl)) {
      showStatus(t('invalidUrl'), 'error');
      return;
    }

    // 显示加载状态
    saveButton.disabled = true;
    saveButton.classList.add('loading');
    const originalText = saveButton.textContent;
    saveButton.textContent = '保存中...';

    try {
      // 保存设置
      const settings = {
        flomoConfig: {
          apiUrl: apiUrl
        }
      };

      await new Promise(resolve => {
        chrome.storage.sync.set(settings, resolve);
      });

      showStatus(t('savedSuccess'), 'success');
      setTimeout(() => window.close(), 1000);

    } catch (error) {
      console.error('保存设置失败:', error);
      showStatus('保存失败，请重试', 'error');
    } finally {
      saveButton.disabled = false;
      saveButton.classList.remove('loading');
      saveButton.textContent = originalText;
    }
  });

  // 键盘导航
  const addKeyboardNavigation = () => {
    // Enter 键保存
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && e.ctrlKey) {
        e.preventDefault();
        saveButton.click();
      }
    });
  };

  // 表单验证
  const addFormValidation = () => {
    // API URL 实时验证
    apiUrlInput.addEventListener('input', () => {
      const url = apiUrlInput.value.trim();
      if (url && !isValidUrl(url)) {
        apiUrlInput.style.borderColor = 'var(--error-color)';
      } else {
        apiUrlInput.style.borderColor = '';
      }
    });
  };

  // 应用所有增强功能
  applyTranslations();
  addKeyboardNavigation();
  addFormValidation();

  console.log('✅ Popup 页面初始化完成');
});
