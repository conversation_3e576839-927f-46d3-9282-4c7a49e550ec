// 引导调试工具
class OnboardingDebug {
  constructor() {
    this.init();
  }

  init() {
    // 添加调试面板
    this.createDebugPanel();
    
    // 添加快捷键
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+O 打开调试面板
      if (e.ctrlKey && e.shiftKey && e.key === 'O') {
        e.preventDefault();
        this.toggleDebugPanel();
      }
    });
  }

  createDebugPanel() {
    const panel = document.createElement('div');
    panel.id = 'onboarding-debug-panel';
    panel.innerHTML = `
      <style>
        #onboarding-debug-panel {
          position: fixed;
          top: 10px;
          left: 10px;
          background: #2d2d2d;
          color: white;
          padding: 16px;
          border-radius: 8px;
          font-family: 'Monaco', 'Consolas', monospace;
          font-size: 12px;
          z-index: 999999;
          max-width: 300px;
          display: none;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        #onboarding-debug-panel h4 {
          margin: 0 0 12px 0;
          color: #4CAF50;
          font-size: 14px;
        }
        
        #onboarding-debug-panel button {
          background: #4CAF50;
          color: white;
          border: none;
          padding: 6px 12px;
          margin: 4px 4px 4px 0;
          border-radius: 4px;
          cursor: pointer;
          font-size: 11px;
        }
        
        #onboarding-debug-panel button:hover {
          background: #45a049;
        }
        
        #onboarding-debug-panel .status {
          background: #333;
          padding: 8px;
          border-radius: 4px;
          margin: 8px 0;
          font-size: 11px;
        }
        
        #onboarding-debug-panel .close-btn {
          position: absolute;
          top: 4px;
          right: 8px;
          background: #f44336;
          padding: 2px 6px;
          font-size: 10px;
        }
      </style>
      
      <button class="close-btn" onclick="this.parentElement.style.display='none'">×</button>
      
      <h4>🔧 引导调试工具</h4>
      
      <div>
        <button onclick="onboardingDebug.resetAll()">重置所有引导</button>
        <button onclick="onboardingDebug.startPopupGuide()">启动设置引导</button>
        <button onclick="onboardingDebug.startWebGuide()">启动网页引导</button>
      </div>
      
      <div>
        <button onclick="onboardingDebug.showStatus()">查看状态</button>
        <button onclick="onboardingDebug.clearStorage()">清空存储</button>
      </div>
      
      <div class="status" id="debug-status">
        按 Ctrl+Shift+O 切换面板
      </div>
      
      <div style="margin-top: 12px; font-size: 10px; color: #888;">
        快捷键：Ctrl+Shift+O
      </div>
    `;
    
    document.body.appendChild(panel);
  }

  toggleDebugPanel() {
    const panel = document.getElementById('onboarding-debug-panel');
    if (panel) {
      panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
      if (panel.style.display === 'block') {
        this.showStatus();
      }
    }
  }

  async resetAll() {
    try {
      await chrome.storage.sync.remove([
        'hasCompletedOnboarding',
        'onboardingVersion',
        'onboardingCompletedAt',
        'hasSeenWebPageGuide',
        'webPageGuideCompletedAt',
        'webPageGuideRemindAt'
      ]);
      
      this.updateStatus('✅ 所有引导状态已重置');
      
      // 如果在设置页面，重新启动引导
      if (window.location.pathname.includes('popup.html') && window.onboardingManager) {
        setTimeout(() => {
          window.onboardingManager.startOnboarding();
        }, 1000);
      }
    } catch (error) {
      this.updateStatus('❌ 重置失败: ' + error.message);
    }
  }

  async startPopupGuide() {
    try {
      if (window.onboardingManager) {
        await window.onboardingManager.resetOnboarding();
        setTimeout(() => {
          window.onboardingManager.startOnboarding();
        }, 500);
        this.updateStatus('✅ 设置引导已启动');
      } else {
        this.updateStatus('❌ 设置引导不可用（不在设置页面）');
      }
    } catch (error) {
      this.updateStatus('❌ 启动设置引导失败: ' + error.message);
    }
  }

  async startWebGuide() {
    try {
      // 重置网页引导状态
      await chrome.storage.sync.remove(['hasSeenWebPageGuide', 'webPageGuideCompletedAt']);
      
      if (window.contentOnboarding) {
        // 如果在内容页面
        setTimeout(() => {
          window.contentOnboarding.showWebPageGuide();
        }, 500);
        this.updateStatus('✅ 网页引导已启动');
      } else {
        // 如果在扩展页面，发送消息到内容脚本
        try {
          await chrome.runtime.sendMessage({ action: 'triggerWebPageGuide' });
          this.updateStatus('✅ 已触发网页引导');
        } catch (error) {
          this.updateStatus('❌ 触发网页引导失败: ' + error.message);
        }
      }
    } catch (error) {
      this.updateStatus('❌ 启动网页引导失败: ' + error.message);
    }
  }

  async showStatus() {
    try {
      const result = await chrome.storage.sync.get([
        'hasCompletedOnboarding',
        'onboardingVersion',
        'onboardingCompletedAt',
        'hasSeenWebPageGuide',
        'webPageGuideCompletedAt',
        'webPageGuideRemindAt'
      ]);

      const status = [
        `设置引导: ${result.hasCompletedOnboarding ? '✅ 已完成' : '❌ 未完成'}`,
        `引导版本: ${result.onboardingVersion || '未设置'}`,
        `网页引导: ${result.hasSeenWebPageGuide ? '✅ 已完成' : '❌ 未完成'}`,
        `提醒时间: ${result.webPageGuideRemindAt ? new Date(result.webPageGuideRemindAt).toLocaleString() : '无'}`
      ].join('\n');

      this.updateStatus(status);
    } catch (error) {
      this.updateStatus('❌ 获取状态失败: ' + error.message);
    }
  }

  async clearStorage() {
    try {
      await chrome.storage.sync.clear();
      await chrome.storage.local.clear();
      this.updateStatus('✅ 所有存储已清空');
    } catch (error) {
      this.updateStatus('❌ 清空存储失败: ' + error.message);
    }
  }

  updateStatus(message) {
    const statusElement = document.getElementById('debug-status');
    if (statusElement) {
      statusElement.textContent = message;
    }
    console.log('[引导调试]', message);
  }
}

// 创建调试工具实例
const onboardingDebug = new OnboardingDebug();

// 添加到全局作用域
if (typeof window !== 'undefined') {
  window.onboardingDebug = onboardingDebug;
}

// 在控制台中提供帮助信息
console.log(`
🔧 Flomo 扩展引导调试工具

快捷键：
- Ctrl+Shift+O: 切换调试面板

控制台命令：
- onboardingDebug.resetAll(): 重置所有引导状态
- onboardingDebug.startPopupGuide(): 启动设置引导
- onboardingDebug.startWebGuide(): 启动网页引导
- onboardingDebug.showStatus(): 查看当前状态
- onboardingDebug.clearStorage(): 清空所有存储

引导管理器：
- onboardingManager: 设置页面引导管理器
- contentOnboarding: 网页内容引导管理器
`);
