// 增强的错误处理系统
class ErrorHandler {
  constructor(config) {
    // 加载配置
    this.config = config || (typeof ErrorConfig !== 'undefined' ? ErrorConfig : {
      retry: { maxAttempts: 3, baseDelay: 1000, maxDelay: 30000, backoffMultiplier: 2, jitter: true },
      timeout: { default: 30000, ai: 60000, flomo: 15000, config: 5000 },
      logging: { enabled: true, level: 'info', maxHistorySize: 100 }
    });

    this.retryAttempts = new Map(); // 跟踪重试次数
    this.errorHistory = []; // 错误历史记录
    this.maxRetries = this.config.retry.maxAttempts;
    this.retryDelay = this.config.retry.baseDelay;
    this.init();
  }

  init() {
    // 监听全局错误 - 兼容Service Worker环境
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.logError('JavaScript Error', event.error);
      });

      window.addEventListener('unhandledrejection', (event) => {
        this.logError('Unhandled Promise Rejection', event.reason);
      });
    } else if (typeof self !== 'undefined') {
      // Service Worker环境
      self.addEventListener('error', (event) => {
        this.logError('Service Worker Error', event.error);
      });

      self.addEventListener('unhandledrejection', (event) => {
        this.logError('Service Worker Promise Rejection', event.reason);
      });
    }
  }

  // 记录错误
  logError(type, error, context = {}) {
    const errorInfo = {
      type,
      message: error?.message || error,
      stack: error?.stack,
      timestamp: Date.now(),
      context,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Service Worker',
      url: typeof window !== 'undefined' ? window.location.href : 'Service Worker Context'
    };

    this.errorHistory.push(errorInfo);

    // 保持错误历史在合理范围内
    const maxSize = this.config.logging?.maxHistorySize || 100;
    if (this.errorHistory.length > maxSize) {
      this.errorHistory = this.errorHistory.slice(-Math.floor(maxSize / 2));
    }

    // 根据配置决定是否记录日志
    if (this.config.logging?.enabled !== false) {
      const level = this.config.logging?.level || 'info';
      if (level === 'debug' || type.includes('Error')) {
        console.error(`[${type}]`, errorInfo);
      }
    }
  }

  // 网络请求错误处理
  async handleNetworkError(error, operation, retryFn, context = {}) {
    const operationKey = `${operation}_${JSON.stringify(context)}`;
    const currentAttempts = this.retryAttempts.get(operationKey) || 0;

    this.logError('Network Error', error, { operation, attempts: currentAttempts, ...context });

    // 分析错误类型
    const errorAnalysis = this.analyzeNetworkError(error);

    // 如果是可重试的错误且未达到最大重试次数
    if (errorAnalysis.retryable && currentAttempts < this.maxRetries) {
      const nextAttempt = currentAttempts + 1;
      this.retryAttempts.set(operationKey, nextAttempt);

      // 计算延迟时间（指数退避 + 抖动）
      let delay = this.config.retry.baseDelay * Math.pow(this.config.retry.backoffMultiplier, currentAttempts);

      // 应用最大延迟限制
      delay = Math.min(delay, this.config.retry.maxDelay);

      // 添加抖动避免雷群效应
      if (this.config.retry.jitter) {
        const jitterRange = delay * 0.1; // 10%的抖动
        delay += (Math.random() - 0.5) * 2 * jitterRange;
      }

      delay = Math.max(delay, 100); // 最小延迟100ms

      console.log(`🔄 准备重试 ${operation}，第 ${nextAttempt} 次尝试，延迟 ${Math.round(delay)}ms`);

      // 显示重试提示
      this.showRetryNotification(operation, nextAttempt, delay);

      await this.delay(delay);

      try {
        const result = await retryFn();
        // 重试成功，清除重试计数
        this.retryAttempts.delete(operationKey);
        this.showSuccessAfterRetry(operation, nextAttempt);
        return result;
      } catch (retryError) {
        return this.handleNetworkError(retryError, operation, retryFn, context);
      }
    } else {
      // 达到最大重试次数或不可重试的错误
      this.retryAttempts.delete(operationKey);
      throw new Error(this.getFriendlyErrorMessage(error, errorAnalysis, currentAttempts));
    }
  }

  // 分析网络错误
  analyzeNetworkError(error) {
    const message = error.message?.toLowerCase() || '';
    const name = error.name?.toLowerCase() || '';
    const statusCode = error.status?.toString() || '';

    // 使用配置的错误分类规则
    const categories = this.config.errorCategories || {};

    // 检查不可重试的错误
    const nonRetryableKeywords = categories.nonRetryable || [];
    const isNonRetryable = nonRetryableKeywords.some(keyword =>
      message.includes(keyword.toLowerCase()) ||
      name.includes(keyword.toLowerCase()) ||
      statusCode.includes(keyword)
    );

    if (isNonRetryable) {
      // 进一步分类
      if (this.matchesCategory(message, name, statusCode, categories.authentication)) {
        return { retryable: false, category: 'authentication_or_config' };
      }
      return { retryable: false, category: 'authentication_or_config' };
    }

    // 检查各种可重试的错误类型
    if (this.matchesCategory(message, name, statusCode, categories.timeout)) {
      return { retryable: true, category: 'timeout' };
    }

    if (this.matchesCategory(message, name, statusCode, categories.rateLimit)) {
      return { retryable: true, category: 'rate_limit' };
    }

    if (this.matchesCategory(message, name, statusCode, categories.serverError)) {
      return { retryable: true, category: 'server_error' };
    }

    if (this.matchesCategory(message, name, statusCode, categories.network)) {
      return { retryable: true, category: 'network' };
    }

    // 默认为可重试的未知错误
    return { retryable: true, category: 'unknown' };
  }

  // 辅助函数：检查是否匹配某个错误类别
  matchesCategory(message, name, statusCode, keywords) {
    if (!keywords || !Array.isArray(keywords)) return false;

    return keywords.some(keyword => {
      const lowerKeyword = keyword.toLowerCase();
      return message.includes(lowerKeyword) ||
        name.includes(lowerKeyword) ||
        statusCode.includes(keyword);
    });
  }

  // 获取友好的错误消息
  getFriendlyErrorMessage(error, analysis, attempts) {
    const message = error?.message || error || '未知错误';
    const category = analysis?.category || 'unknown';

    // 使用配置的友好消息模板
    const messageTemplates = this.config.friendlyMessages || {};
    const errorInfo = messageTemplates[category] || messageTemplates.unknown || {
      title: '错误',
      message: '操作失败，请重试',
      suggestions: ['刷新页面重试', '联系技术支持'],
      actions: ['retry']
    };

    let fullMessage = errorInfo.message;

    if (attempts > 0) {
      fullMessage += `（已重试 ${attempts} 次）`;
    }

    return {
      title: errorInfo.title,
      message: fullMessage,
      suggestions: errorInfo.suggestions || [],
      actions: errorInfo.actions || [],
      originalError: message,
      category
    };
  }

  // 配置错误自动检测
  async validateConfiguration() {
    const issues = [];

    try {
      // 检查Flomo API配置
      const flomoConfig = await chrome.storage.sync.get('flomoApiUrl');
      if (!flomoConfig.flomoApiUrl) {
        issues.push({
          type: 'missing_flomo_api',
          severity: 'error',
          title: 'Flomo API 未配置',
          message: '请配置您的 Flomo API 地址',
          action: 'open_settings'
        });
      } else {
        // 验证URL格式
        try {
          const url = new URL(flomoConfig.flomoApiUrl);
          if (url.protocol !== 'https:') {
            issues.push({
              type: 'insecure_flomo_api',
              severity: 'warning',
              title: 'Flomo API 使用非安全协议',
              message: '建议使用 HTTPS 协议的 API 地址',
              action: 'check_settings'
            });
          }
        } catch (urlError) {
          issues.push({
            type: 'invalid_flomo_api',
            severity: 'error',
            title: 'Flomo API 地址格式错误',
            message: '请检查 API 地址格式是否正确',
            action: 'fix_settings'
          });
        }
      }

      // 检查AI配置
      const aiConfig = await chrome.storage.sync.get(['aiProvider', 'aiApiKeys']);
      if (aiConfig.aiProvider && aiConfig.aiApiKeys) {
        const apiKey = aiConfig.aiApiKeys[aiConfig.aiProvider];
        if (!apiKey) {
          issues.push({
            type: 'missing_ai_key',
            severity: 'warning',
            title: 'AI API 密钥未配置',
            message: `请为 ${aiConfig.aiProvider} 配置 API 密钥`,
            action: 'configure_ai'
          });
        }
      }

      // 检查存储权限
      try {
        await chrome.storage.local.set({ 'test': 'test' });
        await chrome.storage.local.remove('test');
      } catch (storageError) {
        issues.push({
          type: 'storage_permission',
          severity: 'error',
          title: '存储权限错误',
          message: '扩展无法访问本地存储',
          action: 'reinstall_extension'
        });
      }

    } catch (error) {
      this.logError('Configuration Validation', error);
      issues.push({
        type: 'validation_error',
        severity: 'error',
        title: '配置检查失败',
        message: '无法检查配置状态',
        action: 'check_permissions'
      });
    }

    return issues;
  }

  // 显示配置问题
  showConfigurationIssues(issues) {
    if (issues.length === 0) return;

    const errorIssues = issues.filter(issue => issue.severity === 'error');
    const warningIssues = issues.filter(issue => issue.severity === 'warning');

    if (errorIssues.length > 0) {
      this.showConfigurationDialog(errorIssues, 'error');
    } else if (warningIssues.length > 0) {
      this.showConfigurationDialog(warningIssues, 'warning');
    }
  }

  // 显示配置对话框
  showConfigurationDialog(issues, type) {
    const dialog = document.createElement('div');
    dialog.className = 'error-handler-dialog';
    dialog.innerHTML = `
      <style>
        .error-handler-dialog {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.7);
          z-index: 999999;
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
        }
        
        .error-handler-content {
          background: white;
          border-radius: 12px;
          padding: 24px;
          max-width: 480px;
          max-height: 80vh;
          overflow-y: auto;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .error-handler-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 16px;
        }
        
        .error-handler-icon {
          font-size: 24px;
        }
        
        .error-handler-title {
          font-size: 18px;
          font-weight: 600;
          color: #1d1d1f;
        }
        
        .error-handler-issue {
          margin-bottom: 16px;
          padding: 12px;
          border-radius: 8px;
          background: #f8f9fa;
        }
        
        .error-handler-issue.error {
          background: #fef2f2;
          border-left: 4px solid #ef4444;
        }
        
        .error-handler-issue.warning {
          background: #fffbeb;
          border-left: 4px solid #f59e0b;
        }
        
        .error-handler-issue-title {
          font-weight: 600;
          margin-bottom: 4px;
        }
        
        .error-handler-issue-message {
          font-size: 14px;
          color: #6b7280;
        }
        
        .error-handler-buttons {
          display: flex;
          gap: 12px;
          justify-content: flex-end;
          margin-top: 20px;
        }
        
        .error-handler-btn {
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }
        
        .error-handler-btn-secondary {
          background: #f3f4f6;
          color: #374151;
        }
        
        .error-handler-btn-primary {
          background: #3b82f6;
          color: white;
        }
        
        .error-handler-btn:hover {
          opacity: 0.9;
        }
      </style>
      
      <div class="error-handler-content">
        <div class="error-handler-header">
          <div class="error-handler-icon">${type === 'error' ? '❌' : '⚠️'}</div>
          <div class="error-handler-title">
            ${type === 'error' ? '配置错误' : '配置警告'}
          </div>
        </div>
        
        ${issues.map(issue => `
          <div class="error-handler-issue ${issue.severity}">
            <div class="error-handler-issue-title">${issue.title}</div>
            <div class="error-handler-issue-message">${issue.message}</div>
          </div>
        `).join('')}
        
        <div class="error-handler-buttons">
          <button class="error-handler-btn error-handler-btn-secondary" data-action="dismiss">
            稍后处理
          </button>
          <button class="error-handler-btn error-handler-btn-primary" data-action="fix">
            立即修复
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(dialog);

    // 绑定事件
    dialog.addEventListener('click', (e) => {
      if (e.target === dialog || e.target.dataset.action === 'dismiss') {
        dialog.remove();
      } else if (e.target.dataset.action === 'fix') {
        dialog.remove();
        this.handleConfigurationFix(issues);
      }
    });
  }

  // 处理配置修复
  handleConfigurationFix(issues) {
    const hasFlomoIssue = issues.some(issue =>
      issue.type.includes('flomo') || issue.action === 'open_settings'
    );

    if (hasFlomoIssue) {
      // 打开设置页面
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.openOptionsPage();
      }
    }
  }

  // 显示重试通知
  showRetryNotification(operation, attempt, delay) {
    console.log(`🔄 ${operation} 重试中... (第${attempt}次，${delay}ms后)`);

    // 如果有通知管理器，显示重试通知
    if (typeof notificationManager !== 'undefined' && this.config.notifications?.showRetry !== false) {
      notificationManager.retry(operation, attempt, delay);
    }
  }

  // 显示重试成功通知
  showSuccessAfterRetry(operation, attempts) {
    console.log(`✅ ${operation} 重试成功 (共${attempts}次尝试)`);

    // 如果有通知管理器，显示成功通知
    if (typeof notificationManager !== 'undefined' && this.config.notifications?.showSuccess !== false) {
      notificationManager.success(
        '重试成功',
        `${operation} 在第${attempts}次尝试后成功完成`
      );
    }
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 获取错误统计
  getErrorStats() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const recentErrors = this.errorHistory.filter(error =>
      now - error.timestamp < oneHour
    );

    const errorsByType = {};
    recentErrors.forEach(error => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
    });

    return {
      total: this.errorHistory.length,
      recent: recentErrors.length,
      byType: errorsByType,
      retryOperations: Array.from(this.retryAttempts.keys())
    };
  }

  // 清理错误历史
  clearErrorHistory() {
    this.errorHistory = [];
    this.retryAttempts.clear();
  }

  // 统一的错误处理方法
  handleError(error, context = {}, options = {}) {
    const errorInfo = this.analyzeError(error, context);

    // 记录错误
    this.logError(errorInfo.type, error, context);

    // 显示用户友好的错误消息
    if (options.showToUser !== false) {
      this.showUserError(errorInfo);
    }

    // 错误上报（如果启用）
    if (this.config.reporting?.enabled) {
      this.reportError(errorInfo);
    }

    // 返回错误恢复建议
    return {
      error: errorInfo,
      recovery: this.getRecoveryActions(errorInfo)
    };
  }

  // 分析错误类型和严重程度
  analyzeError(error, context = {}) {
    const message = error?.message || error || '未知错误';
    const stack = error?.stack;
    const name = error?.name || 'Error';

    let type = 'unknown';
    let severity = 'medium';
    let category = 'general';

    // 网络错误
    if (message.includes('fetch') || message.includes('network') || message.includes('连接')) {
      type = 'network';
      category = 'network';
      severity = 'high';
    }
    // API错误
    else if (message.includes('API') || message.includes('401') || message.includes('403')) {
      type = 'api';
      category = 'authentication';
      severity = 'high';
    }
    // 验证错误
    else if (message.includes('验证') || message.includes('validation') || message.includes('invalid')) {
      type = 'validation';
      category = 'input';
      severity = 'medium';
    }
    // 配置错误
    else if (message.includes('配置') || message.includes('config') || message.includes('设置')) {
      type = 'configuration';
      category = 'config';
      severity = 'medium';
    }
    // 权限错误
    else if (message.includes('权限') || message.includes('permission') || message.includes('forbidden')) {
      type = 'permission';
      category = 'authorization';
      severity = 'high';
    }

    return {
      type,
      severity,
      category,
      message,
      stack,
      name,
      timestamp: Date.now(),
      context
    };
  }

  // 显示用户友好的错误消息
  showUserError(errorInfo) {
    const userMessage = this.getUserFriendlyMessage(errorInfo);

    // 根据环境选择显示方式
    if (typeof chrome !== 'undefined' && chrome.notifications) {
      // 扩展环境：使用通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icon.png',
        title: this.getErrorTitle(errorInfo.severity),
        message: userMessage
      });
    } else if (typeof window !== 'undefined') {
      // 网页环境：使用页面提示
      this.showPageError(userMessage, errorInfo.severity);
    } else {
      // 其他环境：控制台输出
      console.error('用户错误:', userMessage);
    }
  }

  // 获取用户友好的错误消息
  getUserFriendlyMessage(errorInfo) {
    const { type, category, message } = errorInfo;

    // 预定义的用户友好消息
    const friendlyMessages = {
      network: '网络连接失败，请检查您的网络连接后重试',
      api: 'API服务暂时不可用，请稍后重试或检查API配置',
      validation: '输入的信息格式不正确，请检查后重新输入',
      configuration: '配置信息有误，请检查设置页面的配置',
      permission: '权限不足，请检查API密钥或联系管理员',
      unknown: '操作失败，请重试或联系技术支持'
    };

    return friendlyMessages[type] || friendlyMessages.unknown;
  }

  // 获取错误标题
  getErrorTitle(severity) {
    const titles = {
      low: 'Flomo 提示',
      medium: 'Flomo 警告',
      high: 'Flomo 错误',
      critical: 'Flomo 严重错误'
    };

    return titles[severity] || titles.medium;
  }

  // 在页面中显示错误
  showPageError(message, severity) {
    // 创建或更新错误显示元素
    let errorElement = document.getElementById('global-error-message');

    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.id = 'global-error-message';
      errorElement.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        max-width: 300px;
        padding: 12px 16px;
        border-radius: 6px;
        color: white;
        font-size: 14px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease-out;
      `;
      document.body.appendChild(errorElement);
    }

    // 设置样式和内容
    const colors = {
      low: '#2196F3',
      medium: '#FF9800',
      high: '#F44336',
      critical: '#9C27B0'
    };

    errorElement.style.backgroundColor = colors[severity] || colors.medium;
    errorElement.textContent = message;

    // 自动隐藏
    setTimeout(() => {
      if (errorElement && errorElement.parentNode) {
        errorElement.parentNode.removeChild(errorElement);
      }
    }, 5000);
  }
}

// 导出错误处理器实例
const errorHandler = new ErrorHandler();

// 添加到全局作用域
if (typeof window !== 'undefined') {
  window.errorHandler = errorHandler;
}
