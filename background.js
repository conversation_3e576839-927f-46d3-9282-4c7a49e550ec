
// 引入AI服务管理器和错误处理器
importScripts('error-config.js');
importScripts('input-validator-sw.js');
importScripts('secure-storage.js');
importScripts('request-cache.js');
importScripts('notification-manager-sw.js');
importScripts('ai-service-manager.js');
importScripts('error-handler-sw.js');

chrome.runtime.onInstalled.addListener(async (details) => {
  chrome.contextMenus.create({
    id: "save-to-flomo",
    title: "保存到 Flomo",
    contexts: ["selection"]
  });

  // 初始化安全存储
  try {
    if (SecureStorage.isSupported()) {
      console.log('🔒 初始化安全存储系统...');

      // 如果是更新，尝试迁移旧的明文存储
      if (details.reason === 'update') {
        await secureStorage.migrateFromPlainStorage();
      }
    } else {
      console.warn('⚠️ Web Crypto API 不支持，将使用传统存储');
    }
  } catch (error) {
    console.error('安全存储初始化失败:', error);
  }

  // 如果是首次安装或更新，触发引导
  if (details.reason === 'install') {
    // 首次安装，打开设置页面
    chrome.runtime.openOptionsPage();
  } else if (details.reason === 'update') {
    // 更新时检查是否需要重新引导
    const result = await chrome.storage.sync.get(['onboardingVersion']);
    const currentVersion = '2.0.0';

    if (result.onboardingVersion !== currentVersion) {
      // 版本更新，重置引导状态
      await chrome.storage.sync.remove(['hasCompletedOnboarding', 'onboardingVersion']);
      chrome.runtime.openOptionsPage();
    }
  }
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "save-to-flomo") {
    // 立即打开侧边栏（在用户手势的直接响应中）
    chrome.sidePanel.open({ tabId: tab.id }).then(() => {
      // 侧边栏打开后，异步处理数据
      handleContentSelection(info, tab);
    }).catch(error => {
      console.error('Error opening side panel:', error);
      notificationManager.showErrorChrome('无法打开编辑面板，请重试。');
    });
  }
});

// 处理内容选择的异步函数
async function handleContentSelection(info, tab) {
  try {
    // 检查是否配置了API URL
    const data = await chrome.storage.sync.get('flomoApiUrl');
    if (!data.flomoApiUrl) {
      // 存储一个标记，让侧边栏知道需要配置API
      await chrome.storage.local.set({
        'pendingContent': {
          needsApiConfig: true,
          selectedText: info.selectionText,
          timestamp: Date.now()
        }
      });
      return;
    }

    // 获取页面信息
    const [result] = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: getPageInfo
    });

    // 获取更详细的内容信息
    const [contentResult] = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: getSelectedContentDetailed
    });

    // 存储选中的内容和页面信息到临时存储
    const contentData = {
      selectedText: info.selectionText,
      pageTitle: result.result.title,
      pageUrl: result.result.url,
      timestamp: Date.now(),
      ...contentResult.result // 包含html, hasFormatting等信息
    };

    await chrome.storage.local.set({ 'pendingContent': contentData });

  } catch (error) {
    // 使用统一错误处理器
    const errorResult = errorHandler.handleError(error, {
      operation: 'handleContentSelection',
      tabId: tab.id
    });

    // 存储错误信息，让侧边栏显示错误
    await chrome.storage.local.set({
      'pendingContent': {
        error: errorResult.error.message || '获取页面信息失败，请重试',
        selectedText: info.selectionText,
        timestamp: Date.now()
      }
    });
  }
}

// 获取页面信息的函数，将在内容脚本中执行
function getPageInfo() {
  return {
    title: document.title,
    url: window.location.href
  };
}

// 获取详细的选中内容信息
function getSelectedContentDetailed() {
  const selection = window.getSelection();
  if (selection.rangeCount === 0) {
    return { html: '', hasFormatting: false };
  }

  const range = selection.getRangeAt(0);
  const container = document.createElement('div');
  container.appendChild(range.cloneContents());

  const html = container.innerHTML;
  const hasFormatting = html !== container.textContent;

  return {
    html: html,
    hasFormatting: hasFormatting
  };
}

// 处理来自侧边栏和popup的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'saveToFlomo') {
    saveContentToFlomo(message.content)
      .then(result => sendResponse({ success: true, result }))
      .catch(error => sendResponse({ success: false, error: error?.message || '保存失败' }));
    return true; // 保持消息通道开放以进行异步响应
  } else if (message.action === 'validateApiKey') {
    validateApiKey(message.provider, message.apiKey, message.model)
      .then(result => sendResponse(result))
      .catch(error => sendResponse({ valid: false, message: error?.message || '验证失败' }));
    return true; // 保持消息通道开放以进行异步响应
  } else if (message.action === 'getProvidersConfig') {
    const providers = aiServiceManager.getProviders();
    sendResponse({ success: true, providers });
    return false; // 同步响应
  } else if (message.action === 'triggerWebPageGuide') {
    // 触发当前活动标签页的网页引导
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { action: 'showWebPageGuide' });
      }
    });
    sendResponse({ success: true });
    return false;
  }
});

// 验证 API Key 的函数
async function validateApiKey(provider, apiKey, model) {
  try {
    console.log('🔑 开始验证 API Key', { provider, model, hasApiKey: !!apiKey });
    const result = await aiServiceManager.validateApiKey(provider, apiKey, model);
    console.log('✅ API Key 验证完成', { valid: result.valid, model: result.model });
    return result;
  } catch (error) {
    // 使用统一错误处理器
    const errorResult = errorHandler.handleError(error, {
      operation: 'validateApiKey',
      provider,
      model
    });

    return {
      valid: false,
      message: errorResult.error.message || '验证失败'
    };
  }
}

// 保存内容到Flomo的函数
async function saveContentToFlomo(content) {
  try {
    // 使用错误处理器的重试机制
    const result = await errorHandler.handleNetworkError(
      null, // 初始没有错误
      'saveToFlomo',
      () => performFlomoSave(content),
      { contentLength: content?.length }
    );

    // 显示成功通知
    notificationManager.showSuccessChrome('内容已成功保存到 Flomo！');

    return result;
  } catch (error) {
    // 使用统一错误处理器
    const errorResult = errorHandler.handleError(error, {
      operation: 'saveContentToFlomo',
      contentLength: content?.length
    });

    throw error;
  }
}

// 实际执行Flomo保存的函数
async function performFlomoSave(content) {
  const data = await chrome.storage.sync.get('flomoApiUrl');
  if (!data.flomoApiUrl) {
    throw new Error('API URL 未配置，请先在扩展设置中配置您的 Flomo API 地址');
  }

  // 验证URL格式
  let apiUrl;
  try {
    apiUrl = new URL(data.flomoApiUrl);
    if (apiUrl.protocol !== 'https:') {
      throw new Error('API URL 必须使用 HTTPS 协议');
    }
  } catch (urlError) {
    throw new Error('API URL 格式无效，请检查您的配置');
  }

  // 使用输入验证器验证内容
  try {
    const validatedContent = inputValidator.sanitizeInput(content, 'content');
    // 使用验证后的内容
    content = validatedContent;
  } catch (error) {
    throw new Error(`内容验证失败: ${error.message}`);
  }

  // 发送请求到Flomo API
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

  try {
    const response = await fetch(data.flomoApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Chrome Extension - Save to Flomo'
      },
      body: JSON.stringify({
        content: content.trim()
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      // 构造HTTP错误，让错误处理器统一处理
      const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
      error.status = response.status;
      error.response = response;
      throw error;
    }

    // 尝试解析响应
    let result;
    try {
      result = await response.json();
    } catch (parseError) {
      // 如果响应不是JSON格式，但状态码是成功的，认为保存成功
      result = { success: true };
    }

    // 显示成功通知
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icon.png',
      title: 'Flomo',
      message: '内容已成功保存到 Flomo！'
    });

    return result;

  } catch (fetchError) {
    clearTimeout(timeoutId);

    if (fetchError.name === 'AbortError') {
      throw new Error('请求超时，请检查网络连接或稍后重试');
    }

    if (fetchError.message.includes('Failed to fetch')) {
      throw new Error('网络连接失败，请检查网络设置或API地址');
    }

    throw fetchError;
  }
}

// 清理过期的临时数据
async function cleanupExpiredData() {
  try {
    const result = await chrome.storage.local.get('pendingContent');
    if (result.pendingContent) {
      const { timestamp } = result.pendingContent;
      const now = Date.now();
      const oneHour = 60 * 60 * 1000; // 1小时

      // 如果数据超过1小时，清理它
      if (now - timestamp > oneHour) {
        await chrome.storage.local.remove('pendingContent');
        console.log('Cleaned up expired pending content');
      }
    }
  } catch (error) {
    console.error('Error cleaning up expired data:', error);
  }
}

// 定期清理过期数据
chrome.alarms.create('cleanupExpiredData', { periodInMinutes: 60 });
chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'cleanupExpiredData') {
    cleanupExpiredData();
  }
});

// 开发环境下进行Service Worker验证
if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
  const manifest = chrome.runtime.getManifest();
  if (manifest.name.includes('Save to Flomo')) {
    // 延迟执行验证，确保所有模块都已加载
    setTimeout(() => {
      try {
        importScripts('service-worker-validation.js');
      } catch (error) {
        console.warn('Service Worker验证脚本加载失败:', error);
      }
    }, 1000);
  }
}
