# 🔒 安全配置指南

## 环境配置文件安全

### 📁 配置文件说明

- `env.config.example.js` - 配置文件模板（可安全提交）
- `env.config.js` - 实际配置文件（**绝不可提交**）

### 🚨 重要安全提醒

1. **绝不要将 `env.config.js` 提交到版本控制系统**
2. **API Keys 必须妥善保管，不得泄露**
3. **定期更换 API Keys**
4. **监控 API 使用情况，防止滥用**

### 🛠️ 配置步骤

1. **复制配置模板**
   ```bash
   cp env.config.example.js env.config.js
   ```

2. **编辑配置文件**
   - 将所有 `your-xxx-api-key-here` 替换为实际的 API Keys
   - 根据需要调整默认供应商和模型
   - 在生产环境中设置 `environment: 'production'`

3. **验证配置**
   - 打开浏览器开发者工具
   - 查看控制台是否有配置验证通过的消息
   - 测试 AI 功能是否正常工作

### 🔑 API Keys 获取

| 供应商 | 获取地址 | 说明 |
|--------|----------|------|
| 硅基流动 | https://cloud.siliconflow.cn/ | 提供免费额度 |
| OpenRouter | https://openrouter.ai/ | 支持多种模型 |
| DeepSeek | https://platform.deepseek.com/ | 高性价比 |
| Moonshot | https://platform.moonshot.cn/ | 长上下文支持 |

### 🛡️ 安全最佳实践

#### 1. API Key 管理
- 使用强密码保护账户
- 启用双因素认证（如果支持）
- 设置 API Key 使用限制
- 定期轮换 API Keys

#### 2. 环境隔离
- 开发环境使用测试 API Keys
- 生产环境使用独立的 API Keys
- 不同项目使用不同的 API Keys

#### 3. 监控和审计
- 定期检查 API 使用情况
- 监控异常请求模式
- 设置使用量告警

#### 4. 代码安全
- 不在代码中硬编码 API Keys
- 不在日志中输出敏感信息
- 使用安全的存储方式

### 🚫 安全禁忌

❌ **绝对不要做的事情：**

1. 将 API Keys 提交到 Git 仓库
2. 在公共场所或不安全网络中配置
3. 与他人分享 API Keys
4. 在代码注释中留下 API Keys
5. 在截图或录屏中暴露 API Keys
6. 使用弱密码保护包含 API Keys 的文件

### 🔧 故障排除

#### 配置验证失败
```javascript
// 在浏览器控制台中运行
if (typeof EnvConfig !== 'undefined') {
  const validation = EnvConfig.validateConfig();
  console.log('配置验证结果:', validation);
} else {
  console.error('环境配置文件未加载');
}
```

#### API 调用失败
1. 检查 API Key 是否正确
2. 确认网络连接正常
3. 验证 API 额度是否充足
4. 查看浏览器控制台错误信息

### 📞 紧急情况处理

#### API Key 泄露
1. **立即更换** 所有可能泄露的 API Keys
2. **检查使用记录** 是否有异常调用
3. **联系供应商** 报告安全事件
4. **更新配置文件** 使用新的 API Keys

#### 异常使用
1. **暂停使用** 相关 API Keys
2. **分析日志** 找出异常原因
3. **加强监控** 防止再次发生
4. **更新安全策略**

### 📋 安全检查清单

在部署前请确认：

- [ ] `env.config.js` 已添加到 `.gitignore`
- [ ] 所有 API Keys 都已正确配置
- [ ] 配置验证通过
- [ ] 测试功能正常工作
- [ ] 生产环境设置正确
- [ ] 监控和告警已配置
- [ ] 团队成员了解安全规范
- [ ] 备份和恢复计划已制定

### 🔄 定期维护

建议每月进行：

1. **API Keys 轮换**
2. **使用情况审计**
3. **安全配置检查**
4. **依赖项更新**
5. **安全策略评估**

---

**记住：安全是一个持续的过程，不是一次性的任务。**

如有安全问题或疑虑，请及时联系开发团队。
