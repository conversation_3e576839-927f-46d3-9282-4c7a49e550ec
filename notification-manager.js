// 增强的通知管理系统
class NotificationManager {
  constructor(config) {
    this.config = config || {
      showSuccess: true,
      showRetry: true,
      showError: true,
      autoHideDelay: 5000,
      maxVisible: 3
    };
    
    this.activeNotifications = new Map();
    this.notificationQueue = [];
    this.init();
  }

  init() {
    // 创建通知容器
    this.createNotificationContainer();
    
    // 监听页面卸载，清理通知
    window.addEventListener('beforeunload', () => {
      this.clearAll();
    });
  }

  createNotificationContainer() {
    // 检查是否已存在容器
    if (document.getElementById('notification-container')) return;

    const container = document.createElement('div');
    container.id = 'notification-container';
    container.innerHTML = `
      <style>
        #notification-container {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 999999;
          pointer-events: none;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
        }
        
        .notification {
          background: white;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          border-left: 4px solid #007aff;
          max-width: 360px;
          pointer-events: auto;
          animation: slideInRight 0.3s ease;
          position: relative;
          overflow: hidden;
        }
        
        .notification.success {
          border-left-color: #34c759;
        }
        
        .notification.error {
          border-left-color: #ff3b30;
        }
        
        .notification.warning {
          border-left-color: #ff9500;
        }
        
        .notification.info {
          border-left-color: #007aff;
        }
        
        .notification-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;
        }
        
        .notification-title {
          font-weight: 600;
          font-size: 14px;
          color: #1d1d1f;
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .notification-close {
          background: none;
          border: none;
          font-size: 18px;
          color: #8e8e93;
          cursor: pointer;
          padding: 0;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          transition: background 0.2s ease;
        }
        
        .notification-close:hover {
          background: #f2f2f7;
        }
        
        .notification-message {
          font-size: 13px;
          color: #6e6e73;
          line-height: 1.4;
          margin-bottom: 12px;
        }
        
        .notification-actions {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
        
        .notification-action {
          background: #f2f2f7;
          border: none;
          border-radius: 4px;
          padding: 6px 12px;
          font-size: 12px;
          font-weight: 500;
          color: #1d1d1f;
          cursor: pointer;
          transition: background 0.2s ease;
        }
        
        .notification-action:hover {
          background: #e5e5ea;
        }
        
        .notification-action.primary {
          background: #007aff;
          color: white;
        }
        
        .notification-action.primary:hover {
          background: #0070e0;
        }
        
        .notification-progress {
          position: absolute;
          bottom: 0;
          left: 0;
          height: 2px;
          background: rgba(0, 122, 255, 0.3);
          transition: width linear;
        }
        
        .notification-progress.success {
          background: rgba(52, 199, 89, 0.3);
        }
        
        .notification-progress.error {
          background: rgba(255, 59, 48, 0.3);
        }
        
        .notification-progress.warning {
          background: rgba(255, 149, 0, 0.3);
        }
        
        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        @keyframes slideOutRight {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }
        
        .notification.hiding {
          animation: slideOutRight 0.3s ease forwards;
        }
        
        @media (max-width: 480px) {
          #notification-container {
            top: 10px;
            right: 10px;
            left: 10px;
          }
          
          .notification {
            max-width: none;
          }
        }
      </style>
    `;
    
    document.body.appendChild(container);
  }

  // 显示通知
  show(options) {
    const {
      type = 'info',
      title,
      message,
      actions = [],
      autoHide = true,
      duration = this.config.autoHideDelay,
      id = this.generateId()
    } = options;

    // 检查配置是否允许显示此类型的通知
    if (!this.shouldShowNotification(type)) {
      return null;
    }

    // 如果已达到最大显示数量，加入队列
    if (this.activeNotifications.size >= this.config.maxVisible) {
      this.notificationQueue.push(options);
      return null;
    }

    const notification = this.createNotificationElement({
      id, type, title, message, actions, autoHide, duration
    });

    const container = document.getElementById('notification-container');
    if (container) {
      container.appendChild(notification);
      this.activeNotifications.set(id, {
        element: notification,
        type,
        autoHide,
        duration
      });

      // 设置自动隐藏
      if (autoHide && duration > 0) {
        this.setAutoHide(id, duration);
      }
    }

    return id;
  }

  // 创建通知元素
  createNotificationElement(options) {
    const { id, type, title, message, actions, autoHide, duration } = options;
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.dataset.id = id;

    const iconMap = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };

    const actionsHtml = actions.map(action => {
      const className = action.primary ? 'notification-action primary' : 'notification-action';
      return `<button class="${className}" data-action="${action.id}">${action.label}</button>`;
    }).join('');

    notification.innerHTML = `
      <div class="notification-header">
        <div class="notification-title">
          <span>${iconMap[type] || 'ℹ️'}</span>
          ${title || '通知'}
        </div>
        <button class="notification-close" data-action="close">×</button>
      </div>
      ${message ? `<div class="notification-message">${message}</div>` : ''}
      ${actionsHtml ? `<div class="notification-actions">${actionsHtml}</div>` : ''}
      ${autoHide && duration > 0 ? `<div class="notification-progress ${type}"></div>` : ''}
    `;

    // 绑定事件
    notification.addEventListener('click', (e) => {
      const action = e.target.dataset.action;
      if (action === 'close') {
        this.hide(id);
      } else if (action && actions.find(a => a.id === action)) {
        const actionConfig = actions.find(a => a.id === action);
        if (actionConfig.handler) {
          actionConfig.handler();
        }
        if (actionConfig.closeOnClick !== false) {
          this.hide(id);
        }
      }
    });

    return notification;
  }

  // 设置自动隐藏
  setAutoHide(id, duration) {
    const notificationData = this.activeNotifications.get(id);
    if (!notificationData) return;

    const progressBar = notificationData.element.querySelector('.notification-progress');
    if (progressBar) {
      progressBar.style.width = '100%';
      progressBar.style.transition = `width ${duration}ms linear`;
      
      // 开始进度条动画
      setTimeout(() => {
        progressBar.style.width = '0%';
      }, 50);
    }

    // 设置自动隐藏定时器
    notificationData.hideTimer = setTimeout(() => {
      this.hide(id);
    }, duration);
  }

  // 隐藏通知
  hide(id) {
    const notificationData = this.activeNotifications.get(id);
    if (!notificationData) return;

    // 清除定时器
    if (notificationData.hideTimer) {
      clearTimeout(notificationData.hideTimer);
    }

    // 添加隐藏动画
    notificationData.element.classList.add('hiding');
    
    // 动画完成后移除元素
    setTimeout(() => {
      if (notificationData.element.parentNode) {
        notificationData.element.parentNode.removeChild(notificationData.element);
      }
      this.activeNotifications.delete(id);
      
      // 处理队列中的通知
      this.processQueue();
    }, 300);
  }

  // 处理队列
  processQueue() {
    if (this.notificationQueue.length > 0 && this.activeNotifications.size < this.config.maxVisible) {
      const nextNotification = this.notificationQueue.shift();
      this.show(nextNotification);
    }
  }

  // 检查是否应该显示通知
  shouldShowNotification(type) {
    switch (type) {
      case 'success':
        return this.config.showSuccess !== false;
      case 'error':
        return this.config.showError !== false;
      case 'warning':
        return this.config.showRetry !== false;
      case 'info':
      default:
        return true;
    }
  }

  // 生成唯一ID
  generateId() {
    return 'notification_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // 显示成功通知
  success(title, message, actions) {
    return this.show({ type: 'success', title, message, actions });
  }

  // 显示错误通知
  error(title, message, actions) {
    return this.show({ 
      type: 'error', 
      title, 
      message, 
      actions,
      autoHide: false // 错误通知不自动隐藏
    });
  }

  // 显示警告通知
  warning(title, message, actions) {
    return this.show({ type: 'warning', title, message, actions });
  }

  // 显示信息通知
  info(title, message, actions) {
    return this.show({ type: 'info', title, message, actions });
  }

  // 显示重试通知
  retry(operation, attempt, delay) {
    return this.show({
      type: 'warning',
      title: '🔄 正在重试',
      message: `${operation} 第${attempt}次重试，${Math.round(delay/1000)}秒后执行`,
      autoHide: true,
      duration: delay
    });
  }

  // 清除所有通知
  clearAll() {
    this.activeNotifications.forEach((_, id) => {
      this.hide(id);
    });
    this.notificationQueue = [];
  }

  // 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  // 统一的Chrome扩展通知方法
  showChromeNotification(message, type = 'info', options = {}) {
    if (typeof chrome === 'undefined' || !chrome.notifications) {
      // 如果不在Chrome扩展环境中，使用页面通知
      return this.show(message, type, options);
    }

    const config = {
      title: 'Flomo',
      duration: 5000,
      ...options
    };

    // 设置标题和图标
    const titles = {
      success: 'Flomo',
      error: 'Flomo 错误',
      warning: 'Flomo 警告',
      info: 'Flomo 提示'
    };

    const notificationOptions = {
      type: 'basic',
      iconUrl: 'icon.png',
      title: titles[type] || titles.info,
      message: message
    };

    return new Promise((resolve) => {
      const notificationId = `flomo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      chrome.notifications.create(notificationId, notificationOptions, (id) => {
        if (chrome.runtime.lastError) {
          console.error('Chrome通知创建失败:', chrome.runtime.lastError);
          // 降级到页面通知
          this.show(message, type, options);
          resolve(false);
        } else {
          // 自动清理通知
          if (config.duration > 0) {
            setTimeout(() => {
              chrome.notifications.clear(id);
            }, config.duration);
          }
          resolve(true);
        }
      });
    });
  }

  // 统一的成功通知（Chrome扩展版本）
  showSuccessChrome(message, options = {}) {
    return this.showChromeNotification(message, 'success', options);
  }

  // 统一的错误通知（Chrome扩展版本）
  showErrorChrome(message, options = {}) {
    return this.showChromeNotification(message, 'error', options);
  }

  // 统一的警告通知（Chrome扩展版本）
  showWarningChrome(message, options = {}) {
    return this.showChromeNotification(message, 'warning', options);
  }

  // 统一的信息通知（Chrome扩展版本）
  showInfoChrome(message, options = {}) {
    return this.showChromeNotification(message, 'info', options);
  }

  // 清理所有Chrome通知
  clearAllChromeNotifications() {
    if (typeof chrome !== 'undefined' && chrome.notifications) {
      chrome.notifications.getAll((notifications) => {
        Object.keys(notifications).forEach(id => {
          if (id.startsWith('flomo-')) {
            chrome.notifications.clear(id);
          }
        });
      });
    }
  }
}

// 创建全局通知管理器实例
const notificationManager = new NotificationManager();

// 添加到全局作用域
if (typeof window !== 'undefined') {
  window.notificationManager = notificationManager;
}
