// 优化的AI功能包装器
class OptimizedAIFunctions {
  constructor() {
    this.aiFunctions = new AIFunctions();
    this.optimizer = performanceOptimizer;
    this.debouncedFunctions = new Map();
    this.init();
  }

  init() {
    // 创建防抖版本的AI功能
    this.createDebouncedFunctions();
  }

  createDebouncedFunctions() {
    const functions = ['generateTags', 'generateSummary', 'formatContent', 'translateContent'];
    
    functions.forEach(funcName => {
      this.debouncedFunctions.set(
        funcName,
        this.optimizer.debounce(
          `ai_${funcName}`,
          (...args) => this.executeWithCache(funcName, ...args),
          500 // AI功能使用较长的防抖延迟
        )
      );
    });
  }

  // 带缓存的AI功能执行
  async executeWithCache(functionName, content, options = {}) {
    // 检查缓存
    const cacheKey = functionName;
    const cacheParams = { content, options };
    const cached = this.optimizer.getCachedResult(cacheKey, cacheParams);
    
    if (cached) {
      console.log(`🎯 使用缓存结果: ${functionName}`);
      return cached;
    }

    // 使用请求队列管理并发
    const requestKey = `${functionName}_${this.optimizer.simpleHash(content)}`;
    
    try {
      const result = await this.optimizer.queueRequest(requestKey, async () => {
        console.log(`🚀 执行AI功能: ${functionName}`);
        return await this.aiFunctions[functionName](content, options);
      });

      // 缓存结果
      this.optimizer.setCachedResult(cacheKey, cacheParams, result);
      
      return result;
    } catch (error) {
      console.error(`❌ AI功能执行失败: ${functionName}`, error);
      throw error;
    }
  }

  // 优化的标签生成
  async generateTags(content, options = {}) {
    return new Promise((resolve, reject) => {
      const debouncedFn = this.debouncedFunctions.get('generateTags');
      debouncedFn(content, options).then(resolve).catch(reject);
    });
  }

  // 优化的摘要生成
  async generateSummary(content, options = {}) {
    return new Promise((resolve, reject) => {
      const debouncedFn = this.debouncedFunctions.get('generateSummary');
      debouncedFn(content, options).then(resolve).catch(reject);
    });
  }

  // 优化的格式整理
  async formatContent(content, options = {}) {
    return new Promise((resolve, reject) => {
      const debouncedFn = this.debouncedFunctions.get('formatContent');
      debouncedFn(content, options).then(resolve).catch(reject);
    });
  }

  // 优化的翻译功能
  async translateContent(content, options = {}) {
    return new Promise((resolve, reject) => {
      const debouncedFn = this.debouncedFunctions.get('translateContent');
      debouncedFn(content, options).then(resolve).catch(reject);
    });
  }

  // 批量处理AI功能
  async batchProcess(items, functionName, options = {}) {
    const batchProcessor = this.optimizer.createBatchProcessor(
      async (batch) => {
        const results = await Promise.allSettled(
          batch.map(item => this.executeWithCache(functionName, item.content, options))
        );
        
        // 处理批量结果
        results.forEach((result, index) => {
          const item = batch[index];
          if (result.status === 'fulfilled') {
            item.callback(null, result.value);
          } else {
            item.callback(result.reason);
          }
        });
      },
      3, // 批量大小
      1000 // 延迟1秒
    );

    // 返回Promise包装的批量处理器
    return (content, callback) => {
      batchProcessor({ content, callback });
    };
  }

  // 预加载常用结果
  async preloadCommonResults() {
    const commonContents = [
      '这是一个测试内容',
      '技术文章内容示例',
      '产品介绍文本'
    ];

    console.log('🔄 预加载常用AI结果...');
    
    for (const content of commonContents) {
      try {
        // 预加载标签和摘要
        await Promise.allSettled([
          this.executeWithCache('generateTags', content),
          this.executeWithCache('generateSummary', content)
        ]);
      } catch (error) {
        console.warn('预加载失败:', error);
      }
    }
    
    console.log('✅ 预加载完成');
  }

  // 智能内容分析
  analyzeContent(content) {
    const analysis = {
      length: content.length,
      complexity: this.calculateComplexity(content),
      language: this.detectLanguage(content),
      type: this.detectContentType(content),
      estimatedTokens: Math.ceil(content.length / 4), // 粗略估算
      recommendedFunctions: []
    };

    // 基于分析推荐AI功能
    if (analysis.length > 500) {
      analysis.recommendedFunctions.push('generateSummary');
    }
    
    if (analysis.complexity > 0.7) {
      analysis.recommendedFunctions.push('formatContent');
    }
    
    if (analysis.language === 'mixed' || analysis.type === 'technical') {
      analysis.recommendedFunctions.push('translateContent');
    }
    
    analysis.recommendedFunctions.push('generateTags');

    return analysis;
  }

  calculateComplexity(content) {
    // 简单的复杂度计算
    const sentences = content.split(/[.!?]+/).length;
    const words = content.split(/\s+/).length;
    const avgWordsPerSentence = words / sentences;
    
    // 标准化复杂度分数 (0-1)
    return Math.min(avgWordsPerSentence / 20, 1);
  }

  detectLanguage(content) {
    const chineseChars = (content.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishChars = (content.match(/[a-zA-Z]/g) || []).length;
    const total = chineseChars + englishChars;
    
    if (total === 0) return 'unknown';
    
    const chineseRatio = chineseChars / total;
    if (chineseRatio > 0.7) return 'chinese';
    if (chineseRatio < 0.3) return 'english';
    return 'mixed';
  }

  detectContentType(content) {
    const technicalKeywords = ['API', '代码', '函数', '算法', '数据库', '服务器'];
    const businessKeywords = ['市场', '销售', '客户', '产品', '策略', '营销'];
    const academicKeywords = ['研究', '分析', '理论', '方法', '结论', '实验'];
    
    const techScore = this.countKeywords(content, technicalKeywords);
    const businessScore = this.countKeywords(content, businessKeywords);
    const academicScore = this.countKeywords(content, academicKeywords);
    
    const maxScore = Math.max(techScore, businessScore, academicScore);
    
    if (maxScore === 0) return 'general';
    if (maxScore === techScore) return 'technical';
    if (maxScore === businessScore) return 'business';
    return 'academic';
  }

  countKeywords(content, keywords) {
    return keywords.reduce((count, keyword) => {
      return count + (content.includes(keyword) ? 1 : 0);
    }, 0);
  }

  // 获取优化统计
  getOptimizationStats() {
    const performanceStats = this.optimizer.getPerformanceStats();
    
    return {
      ...performanceStats,
      debouncedFunctions: this.debouncedFunctions.size,
      cacheEfficiency: this.calculateCacheEfficiency()
    };
  }

  calculateCacheEfficiency() {
    const stats = this.optimizer.getPerformanceStats();
    const hitRate = parseFloat(stats.cache.hitRate);
    
    if (hitRate > 80) return 'excellent';
    if (hitRate > 60) return 'good';
    if (hitRate > 40) return 'fair';
    return 'poor';
  }

  // 清理优化器
  cleanup() {
    this.debouncedFunctions.clear();
    this.optimizer.cleanup();
  }
}

// 创建优化的AI功能实例
const optimizedAIFunctions = new OptimizedAIFunctions();

// 添加到全局作用域
if (typeof window !== 'undefined') {
  window.optimizedAIFunctions = optimizedAIFunctions;
}
