<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能监控面板</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            border-bottom: 2px solid #007aff;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007aff;
        }
        
        .metric-card.warning {
            border-left-color: #ffc107;
        }
        
        .metric-card.danger {
            border-left-color: #dc3545;
        }
        
        .metric-card.success {
            border-left-color: #28a745;
        }
        
        .metric-title {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #007aff;
            margin-bottom: 5px;
        }
        
        .metric-detail {
            font-size: 12px;
            color: #6c757d;
        }
        
        .controls {
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s ease;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #218838;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .chart {
            height: 200px;
            background: #f8f9fa;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        
        .cache-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .cache-item {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
        }
        
        .cache-key {
            font-family: monospace;
            font-size: 12px;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .cache-meta {
            font-size: 11px;
            color: #6c757d;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #007aff;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }
        
        .log-container {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.good {
            background: #28a745;
        }
        
        .status-indicator.warning {
            background: #ffc107;
        }
        
        .status-indicator.danger {
            background: #dc3545;
        }
        
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-left: auto;
        }
        
        .auto-refresh input[type="checkbox"] {
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 性能监控面板</h1>
        
        <div class="controls">
            <button id="refresh-btn" class="btn">刷新数据</button>
            <button id="clear-cache-btn" class="btn danger">清空缓存</button>
            <button id="optimize-btn" class="btn success">执行优化</button>
            <button id="export-btn" class="btn">导出报告</button>
            
            <div class="auto-refresh">
                <input type="checkbox" id="auto-refresh" checked>
                <label for="auto-refresh">自动刷新</label>
            </div>
        </div>
        
        <div class="metrics-grid" id="metrics-grid">
            <!-- 动态生成的指标卡片 -->
        </div>
        
        <div class="chart-container">
            <div class="chart-title">内存使用趋势</div>
            <div class="chart" id="memory-chart">
                <canvas width="100%" height="200"></canvas>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">缓存命中率</div>
            <div class="progress-bar">
                <div class="progress-fill" id="cache-hit-rate" style="width: 0%">0%</div>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">缓存详情</div>
            <div class="cache-stats" id="cache-details">
                <!-- 动态生成的缓存项目 -->
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">性能日志</div>
            <div class="log-container" id="performance-log"></div>
        </div>
    </div>

    <script src="performance-optimizer.js"></script>
    <script src="dom-optimizer.js"></script>
    <script src="ai-functions-optimized.js"></script>
    
    <script>
        class PerformanceMonitor {
            constructor() {
                this.isAutoRefresh = true;
                this.refreshInterval = null;
                this.logs = [];
                this.init();
            }
            
            init() {
                this.bindEvents();
                this.startAutoRefresh();
                this.refreshData();
            }
            
            bindEvents() {
                document.getElementById('refresh-btn').addEventListener('click', () => {
                    this.refreshData();
                });
                
                document.getElementById('clear-cache-btn').addEventListener('click', () => {
                    this.clearCache();
                });
                
                document.getElementById('optimize-btn').addEventListener('click', () => {
                    this.executeOptimization();
                });
                
                document.getElementById('export-btn').addEventListener('click', () => {
                    this.exportReport();
                });
                
                document.getElementById('auto-refresh').addEventListener('change', (e) => {
                    this.isAutoRefresh = e.target.checked;
                    if (this.isAutoRefresh) {
                        this.startAutoRefresh();
                    } else {
                        this.stopAutoRefresh();
                    }
                });
            }
            
            startAutoRefresh() {
                if (this.refreshInterval) return;
                
                this.refreshInterval = setInterval(() => {
                    if (this.isAutoRefresh) {
                        this.refreshData();
                    }
                }, 5000); // 每5秒刷新
            }
            
            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            }
            
            refreshData() {
                this.updateMetrics();
                this.updateCacheDetails();
                this.updateMemoryChart();
                this.addLog('数据已刷新');
            }
            
            updateMetrics() {
                const perfStats = performanceOptimizer.getPerformanceStats();
                const domStats = domOptimizer.getPerformanceStats();
                const aiStats = optimizedAIFunctions.getOptimizationStats();
                
                const metrics = [
                    {
                        title: '缓存命中率',
                        value: `${perfStats.cache.hitRate}%`,
                        detail: `${perfStats.cache.size}/${perfStats.cache.maxSize} 项`,
                        status: this.getCacheStatus(perfStats.cache.hitRate)
                    },
                    {
                        title: '活跃请求',
                        value: perfStats.requests.active,
                        detail: `最大并发: ${perfStats.requests.maxConcurrent}`,
                        status: perfStats.requests.active > 2 ? 'warning' : 'success'
                    },
                    {
                        title: '内存使用',
                        value: perfStats.memory.current,
                        detail: `峰值: ${perfStats.memory.peak}`,
                        status: this.getMemoryStatus(perfStats.memory.trend)
                    },
                    {
                        title: 'DOM节点',
                        value: domStats.domNodes,
                        detail: `更新队列: ${domStats.updateQueue}`,
                        status: domStats.domNodes > 3000 ? 'warning' : 'success'
                    },
                    {
                        title: '防抖定时器',
                        value: perfStats.debounce.activeTimers,
                        detail: 'AI功能防抖',
                        status: 'success'
                    },
                    {
                        title: '缓存效率',
                        value: aiStats.cacheEfficiency,
                        detail: `${aiStats.debouncedFunctions} 个优化函数`,
                        status: this.getEfficiencyStatus(aiStats.cacheEfficiency)
                    }
                ];
                
                this.renderMetrics(metrics);
                this.updateCacheHitRate(perfStats.cache.hitRate);
            }
            
            renderMetrics(metrics) {
                const container = document.getElementById('metrics-grid');
                container.innerHTML = '';
                
                metrics.forEach(metric => {
                    const card = document.createElement('div');
                    card.className = `metric-card ${metric.status}`;
                    card.innerHTML = `
                        <div class="metric-title">
                            <span class="status-indicator ${metric.status}"></span>
                            ${metric.title}
                        </div>
                        <div class="metric-value">${metric.value}</div>
                        <div class="metric-detail">${metric.detail}</div>
                    `;
                    container.appendChild(card);
                });
            }
            
            updateCacheHitRate(hitRate) {
                const element = document.getElementById('cache-hit-rate');
                element.style.width = `${hitRate}%`;
                element.textContent = `${hitRate}%`;
                
                // 更新颜色
                if (hitRate >= 80) {
                    element.style.background = '#28a745';
                } else if (hitRate >= 60) {
                    element.style.background = '#ffc107';
                } else {
                    element.style.background = '#dc3545';
                }
            }
            
            updateCacheDetails() {
                const container = document.getElementById('cache-details');
                container.innerHTML = '';
                
                // 模拟缓存详情（实际应该从performanceOptimizer获取）
                const cacheItems = [
                    { key: 'ai_generateTags_abc123', timestamp: Date.now() - 60000, hits: 5 },
                    { key: 'ai_generateSummary_def456', timestamp: Date.now() - 120000, hits: 3 },
                    { key: 'ai_formatContent_ghi789', timestamp: Date.now() - 30000, hits: 8 }
                ];
                
                cacheItems.forEach(item => {
                    const element = document.createElement('div');
                    element.className = 'cache-item';
                    element.innerHTML = `
                        <div class="cache-key">${item.key}</div>
                        <div class="cache-meta">
                            命中: ${item.hits} 次 | 
                            ${Math.floor((Date.now() - item.timestamp) / 1000)}秒前
                        </div>
                    `;
                    container.appendChild(element);
                });
            }
            
            updateMemoryChart() {
                // 简化的内存图表更新
                const chart = document.getElementById('memory-chart');
                chart.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #6c757d;">
                        内存使用图表<br>
                        <small>当前: ${performanceOptimizer.memoryMonitor.getStats().current}</small>
                    </div>
                `;
            }
            
            clearCache() {
                performanceOptimizer.cache.clear();
                this.addLog('缓存已清空');
                this.refreshData();
            }
            
            executeOptimization() {
                // 执行各种优化操作
                performanceOptimizer.cleanupExpiredCache();
                domOptimizer.cleanup();
                
                this.addLog('优化操作已执行');
                this.refreshData();
            }
            
            exportReport() {
                const report = {
                    timestamp: new Date().toISOString(),
                    performance: performanceOptimizer.getPerformanceStats(),
                    dom: domOptimizer.getPerformanceStats(),
                    ai: optimizedAIFunctions.getOptimizationStats(),
                    logs: this.logs
                };
                
                const blob = new Blob([JSON.stringify(report, null, 2)], {
                    type: 'application/json'
                });
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `performance-report-${Date.now()}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                this.addLog('性能报告已导出');
            }
            
            addLog(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${message}`;
                this.logs.push(logEntry);
                
                // 保持日志数量在合理范围
                if (this.logs.length > 100) {
                    this.logs = this.logs.slice(-50);
                }
                
                const logContainer = document.getElementById('performance-log');
                logContainer.textContent = this.logs.join('\n');
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            
            getCacheStatus(hitRate) {
                if (hitRate >= 80) return 'success';
                if (hitRate >= 60) return 'warning';
                return 'danger';
            }
            
            getMemoryStatus(trend) {
                if (trend === 'increasing') return 'warning';
                if (trend === 'decreasing') return 'success';
                return 'success';
            }
            
            getEfficiencyStatus(efficiency) {
                if (efficiency === 'excellent') return 'success';
                if (efficiency === 'good') return 'success';
                if (efficiency === 'fair') return 'warning';
                return 'danger';
            }
        }
        
        // 初始化性能监控器
        const performanceMonitor = new PerformanceMonitor();
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            performanceMonitor.stopAutoRefresh();
        });
    </script>
</body>
</html>
