// 性能优化配置
const PerformanceConfig = {
  // 缓存配置
  cache: {
    // AI结果缓存
    ai: {
      enabled: true,
      maxSize: 100,
      expiry: 30 * 60 * 1000, // 30分钟
      strategies: {
        tags: { maxSize: 50, expiry: 60 * 60 * 1000 }, // 标签缓存1小时
        summary: { maxSize: 30, expiry: 45 * 60 * 1000 }, // 摘要缓存45分钟
        format: { maxSize: 40, expiry: 30 * 60 * 1000 }, // 格式缓存30分钟
        translate: { maxSize: 20, expiry: 2 * 60 * 60 * 1000 } // 翻译缓存2小时
      }
    },
    
    // DOM片段缓存
    dom: {
      enabled: true,
      maxSize: 50,
      expiry: 10 * 60 * 1000 // 10分钟
    },
    
    // 网络请求缓存
    network: {
      enabled: true,
      maxSize: 20,
      expiry: 5 * 60 * 1000 // 5分钟
    }
  },

  // 防抖配置
  debounce: {
    // 输入防抖
    input: {
      textarea: 300,
      search: 500,
      filter: 200
    },
    
    // AI功能防抖
    ai: {
      generateTags: 500,
      generateSummary: 800,
      formatContent: 400,
      translateContent: 600
    },
    
    // DOM更新防抖
    dom: {
      resize: 100,
      scroll: 50,
      update: 16 // 约60fps
    }
  },

  // 并发控制
  concurrency: {
    // AI请求并发限制
    ai: {
      maxConcurrent: 3,
      queueTimeout: 30000 // 30秒超时
    },
    
    // 网络请求并发限制
    network: {
      maxConcurrent: 5,
      queueTimeout: 15000 // 15秒超时
    },
    
    // DOM操作并发限制
    dom: {
      maxBatchSize: 10,
      batchDelay: 16 // 约60fps
    }
  },

  // 内存管理
  memory: {
    // 内存监控
    monitoring: {
      enabled: true,
      interval: 10000, // 10秒采样
      maxSamples: 100,
      warningThreshold: 50 * 1024 * 1024, // 50MB
      criticalThreshold: 100 * 1024 * 1024 // 100MB
    },
    
    // 垃圾回收提示
    gc: {
      enabled: true,
      interval: 5 * 60 * 1000, // 5分钟
      memoryThreshold: 0.8 // 80%内存使用率
    },
    
    // 内存泄漏检测
    leakDetection: {
      enabled: true,
      checkInterval: 60000, // 1分钟
      maxListeners: 50,
      maxObservers: 20
    }
  },

  // 性能监控
  monitoring: {
    // 性能指标收集
    metrics: {
      enabled: true,
      interval: 5000, // 5秒
      maxHistory: 200
    },
    
    // 性能警告
    warnings: {
      enabled: true,
      thresholds: {
        responseTime: 3000, // 3秒响应时间
        memoryUsage: 0.8, // 80%内存使用
        cacheHitRate: 0.6, // 60%缓存命中率
        domNodes: 3000 // 3000个DOM节点
      }
    },
    
    // 性能报告
    reporting: {
      enabled: true,
      interval: 30 * 60 * 1000, // 30分钟
      autoExport: false
    }
  },

  // 优化策略
  optimization: {
    // 自动优化
    auto: {
      enabled: true,
      interval: 10 * 60 * 1000, // 10分钟
      strategies: [
        'cleanupExpiredCache',
        'optimizeMemory',
        'compactDOM',
        'clearUnusedListeners'
      ]
    },
    
    // 预加载策略
    preload: {
      enabled: true,
      commonQueries: [
        '技术文章',
        '产品介绍',
        '会议纪要',
        '学习笔记'
      ],
      maxPreloadItems: 10
    },
    
    // 批处理优化
    batching: {
      enabled: true,
      ai: {
        batchSize: 3,
        batchDelay: 1000
      },
      dom: {
        batchSize: 10,
        batchDelay: 16
      }
    }
  },

  // 开发模式配置
  development: {
    enabled: false, // 生产环境设为false
    logging: {
      performance: true,
      cache: true,
      memory: true,
      dom: true
    },
    debugging: {
      showMetrics: true,
      showWarnings: true,
      enableProfiler: true
    }
  },

  // 用户偏好
  userPreferences: {
    // 性能模式
    performanceMode: 'balanced', // 'performance', 'balanced', 'battery'
    
    // 缓存偏好
    cachePreference: 'aggressive', // 'conservative', 'balanced', 'aggressive'
    
    // 动画偏好
    animations: true,
    
    // 自动优化
    autoOptimization: true
  }
};

// 性能模式配置
PerformanceConfig.modes = {
  performance: {
    cache: { ...PerformanceConfig.cache, ai: { ...PerformanceConfig.cache.ai, maxSize: 200 } },
    debounce: { ...PerformanceConfig.debounce, ai: { generateTags: 200, generateSummary: 300, formatContent: 200, translateContent: 300 } },
    concurrency: { ...PerformanceConfig.concurrency, ai: { maxConcurrent: 5 } }
  },
  
  balanced: {
    // 使用默认配置
  },
  
  battery: {
    cache: { ...PerformanceConfig.cache, ai: { ...PerformanceConfig.cache.ai, maxSize: 50 } },
    debounce: { ...PerformanceConfig.debounce, ai: { generateTags: 1000, generateSummary: 1500, formatContent: 800, translateContent: 1200 } },
    concurrency: { ...PerformanceConfig.concurrency, ai: { maxConcurrent: 2 } },
    monitoring: { ...PerformanceConfig.monitoring, metrics: { enabled: false } }
  }
};

// 应用性能模式
PerformanceConfig.applyMode = function(mode) {
  if (!this.modes[mode]) {
    console.warn(`未知的性能模式: ${mode}`);
    return;
  }
  
  const modeConfig = this.modes[mode];
  Object.assign(this, modeConfig);
  
  console.log(`🚀 已应用性能模式: ${mode}`);
};

// 获取当前配置
PerformanceConfig.getCurrentConfig = function() {
  return {
    mode: this.userPreferences.performanceMode,
    cache: this.cache,
    debounce: this.debounce,
    concurrency: this.concurrency,
    memory: this.memory,
    monitoring: this.monitoring,
    optimization: this.optimization
  };
};

// 更新配置
PerformanceConfig.updateConfig = function(updates) {
  Object.keys(updates).forEach(key => {
    if (this[key] && typeof this[key] === 'object') {
      Object.assign(this[key], updates[key]);
    } else {
      this[key] = updates[key];
    }
  });
  
  console.log('⚙️ 性能配置已更新');
};

// 重置为默认配置
PerformanceConfig.reset = function() {
  // 重新加载默认配置
  location.reload();
};

// 验证配置
PerformanceConfig.validate = function() {
  const issues = [];
  
  // 检查缓存配置
  if (this.cache.ai.maxSize < 10) {
    issues.push('AI缓存大小过小，可能影响性能');
  }
  
  // 检查防抖配置
  if (this.debounce.ai.generateTags < 100) {
    issues.push('标签生成防抖时间过短，可能导致频繁请求');
  }
  
  // 检查并发配置
  if (this.concurrency.ai.maxConcurrent > 10) {
    issues.push('AI并发数过高，可能影响稳定性');
  }
  
  return issues;
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PerformanceConfig;
} else if (typeof window !== 'undefined') {
  window.PerformanceConfig = PerformanceConfig;
}
