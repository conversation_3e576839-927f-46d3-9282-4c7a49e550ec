// HTML到Markdown转换引擎
class HtmlToMarkdownConverter {
  constructor() {
    this.rules = this.initializeRules();
  }

  // 初始化转换规则
  initializeRules() {
    return [
      // 标题
      {
        tag: 'h1',
        replacement: (content) => `# ${content}\n\n`
      },
      {
        tag: 'h2',
        replacement: (content) => `## ${content}\n\n`
      },
      {
        tag: 'h3',
        replacement: (content) => `### ${content}\n\n`
      },
      {
        tag: 'h4',
        replacement: (content) => `#### ${content}\n\n`
      },
      {
        tag: 'h5',
        replacement: (content) => `##### ${content}\n\n`
      },
      {
        tag: 'h6',
        replacement: (content) => `###### ${content}\n\n`
      },

      // 强调
      {
        tag: 'strong',
        replacement: (content) => `**${content}**`
      },
      {
        tag: 'b',
        replacement: (content) => `**${content}**`
      },
      {
        tag: 'em',
        replacement: (content) => `*${content}*`
      },
      {
        tag: 'i',
        replacement: (content) => `*${content}*`
      },

      // 链接
      {
        tag: 'a',
        replacement: (content, node) => {
          const href = node.getAttribute('href');
          if (!href) return content;
          return `[${content}](${href})`;
        }
      },

      // 图片
      {
        tag: 'img',
        replacement: (content, node) => {
          const src = node.getAttribute('src');
          const alt = node.getAttribute('alt') || '';
          if (!src) return '';
          return `![${alt}](${src})`;
        }
      },

      // 代码
      {
        tag: 'code',
        replacement: (content) => `\`${content}\``
      },
      {
        tag: 'pre',
        replacement: (content) => `\n\`\`\`\n${content}\n\`\`\`\n\n`
      },

      // 引用
      {
        tag: 'blockquote',
        replacement: (content) => {
          return content.split('\n').map(line => `> ${line}`).join('\n') + '\n\n';
        }
      },

      // 列表
      {
        tag: 'ul',
        replacement: (content) => `\n${content}\n`
      },
      {
        tag: 'ol',
        replacement: (content) => `\n${content}\n`
      },
      {
        tag: 'li',
        replacement: (content, node) => {
          const parent = node.parentNode;
          if (parent.tagName.toLowerCase() === 'ol') {
            const index = Array.from(parent.children).indexOf(node) + 1;
            return `${index}. ${content}\n`;
          } else {
            return `- ${content}\n`;
          }
        }
      },

      // 段落
      {
        tag: 'p',
        replacement: (content) => `${content}\n\n`
      },

      // 换行
      {
        tag: 'br',
        replacement: () => '\n'
      },

      // 水平线
      {
        tag: 'hr',
        replacement: () => '\n---\n\n'
      },

      // 表格
      {
        tag: 'table',
        replacement: (content) => `\n${content}\n`
      },
      {
        tag: 'thead',
        replacement: (content) => content
      },
      {
        tag: 'tbody',
        replacement: (content) => content
      },
      {
        tag: 'tr',
        replacement: (content, node) => {
          const cells = content.split('|').filter(cell => cell.trim());
          const result = `| ${cells.join(' | ')} |\n`;
          
          // 如果是表头，添加分隔线
          if (node.parentNode.tagName.toLowerCase() === 'thead') {
            const separator = '|' + cells.map(() => ' --- ').join('|') + '|\n';
            return result + separator;
          }
          return result;
        }
      },
      {
        tag: 'th',
        replacement: (content) => `${content}|`
      },
      {
        tag: 'td',
        replacement: (content) => `${content}|`
      },

      // 删除线
      {
        tag: 'del',
        replacement: (content) => `~~${content}~~`
      },
      {
        tag: 's',
        replacement: (content) => `~~${content}~~`
      },

      // 下划线（Markdown不直接支持，用HTML标签）
      {
        tag: 'u',
        replacement: (content) => `<u>${content}</u>`
      },

      // 上标和下标
      {
        tag: 'sup',
        replacement: (content) => `<sup>${content}</sup>`
      },
      {
        tag: 'sub',
        replacement: (content) => `<sub>${content}</sub>`
      }
    ];
  }

  // 主转换方法
  convert(html) {
    if (!html || typeof html !== 'string') {
      return '';
    }

    // 创建临时DOM元素
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // 递归转换
    const markdown = this.processNode(tempDiv);

    // 清理多余的空行
    return this.cleanupMarkdown(markdown);
  }

  // 处理DOM节点
  processNode(node) {
    if (node.nodeType === Node.TEXT_NODE) {
      return this.escapeMarkdown(node.textContent);
    }

    if (node.nodeType !== Node.ELEMENT_NODE) {
      return '';
    }

    const tagName = node.tagName.toLowerCase();
    const rule = this.rules.find(r => r.tag === tagName);

    // 处理子节点
    let content = '';
    for (const child of node.childNodes) {
      content += this.processNode(child);
    }

    // 应用转换规则
    if (rule) {
      return rule.replacement(content, node);
    }

    // 默认返回内容（忽略未知标签）
    return content;
  }

  // 转义Markdown特殊字符
  escapeMarkdown(text) {
    // 在普通文本中转义Markdown特殊字符
    const specialChars = /([\\`*_{}[\]()#+\-.!])/g;
    return text.replace(specialChars, '\\$1');
  }

  // 清理Markdown格式
  cleanupMarkdown(markdown) {
    return markdown
      // 移除多余的空行
      .replace(/\n{3,}/g, '\n\n')
      // 移除开头和结尾的空行
      .trim()
      // 确保列表项之间有适当的间距
      .replace(/(\n- .+)\n\n(\n- .+)/g, '$1\n$2')
      .replace(/(\n\d+\. .+)\n\n(\n\d+\. .+)/g, '$1\n$2');
  }

  // 获取格式化预览
  getFormattingPreview(html) {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    const elements = {
      headings: tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6').length,
      bold: tempDiv.querySelectorAll('strong, b').length,
      italic: tempDiv.querySelectorAll('em, i').length,
      links: tempDiv.querySelectorAll('a').length,
      images: tempDiv.querySelectorAll('img').length,
      lists: tempDiv.querySelectorAll('ul, ol').length,
      code: tempDiv.querySelectorAll('code, pre').length,
      blockquotes: tempDiv.querySelectorAll('blockquote').length,
      tables: tempDiv.querySelectorAll('table').length
    };

    const formatSummary = [];
    if (elements.headings > 0) formatSummary.push(`${elements.headings}个标题`);
    if (elements.bold > 0) formatSummary.push(`${elements.bold}处粗体`);
    if (elements.italic > 0) formatSummary.push(`${elements.italic}处斜体`);
    if (elements.links > 0) formatSummary.push(`${elements.links}个链接`);
    if (elements.images > 0) formatSummary.push(`${elements.images}张图片`);
    if (elements.lists > 0) formatSummary.push(`${elements.lists}个列表`);
    if (elements.code > 0) formatSummary.push(`${elements.code}处代码`);
    if (elements.blockquotes > 0) formatSummary.push(`${elements.blockquotes}个引用`);
    if (elements.tables > 0) formatSummary.push(`${elements.tables}个表格`);

    return {
      elements,
      summary: formatSummary.length > 0 ? formatSummary.join('、') : '无特殊格式',
      hasFormatting: formatSummary.length > 0
    };
  }
}

// 导出转换器实例
const htmlToMarkdown = new HtmlToMarkdownConverter();
